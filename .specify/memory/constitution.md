<!-- Sync Impact Report -->
<!-- Version Change: 0.0.0 → 1.0.0 (Major) -->
<!-- Modified Principles: All 5 principles established for first time -->
<!-- Added Sections: Development Workflow, Security Guidelines, Testing Standards, Performance Guidelines, Docker Development, Governance -->
<!-- Removed Sections: None -->
<!-- Templates Updated: N/A (new constitution) -->
<!-- Follow-up TODOs: None -->

# Kaikyo Constitution

## Core Principles

### I. Laravel-First Architecture

Every component MUST prioritize Laravel conventions, helpers, and facades over raw PHP functions. Use Eloquent for database operations, <PERSON><PERSON>'s validation system, authentication, and routing. Leverage the full Laravel ecosystem including Jetstream, Fortify, Sanctum, and Cashier. All architectural decisions MUST align with <PERSON><PERSON> best practices and maintain compatibility with the framework's evolution.

### II. Type Safety & SOLID Principles

All PHP files MUST declare `strict_types=1` and use explicit type declarations. Follow SOLID principles for maintainable and scalable code. Use final classes where appropriate, implement proper error handling, and practice dependency injection. Use Value Objects and Data Transfer Objects (DTOs) instead of raw arrays for data transfer between layers. All status handling MUST use enums to ensure type safety and prevent invalid states.

### III. Docker-First Development

ALWAYS use Docker Compose and existing make commands for development tasks, testing, and debugging. NEVER run PHP directly in shell. Check running containers with `docker compose ps` before issuing new commands. Use `make up` to start services, `make dev` for development with logs, and `make test` for testing. All development MUST occur in containerized environments to ensure consistency across team members and deployments.

### IV. Security-First Design

Security MUST be the highest priority in all architectural decisions. Use Laravel Jetstream for user management, implement proper role-based access control, and validate all user inputs. Never commit secrets to version control, use environment variables for configuration, and implement proper CSRF protection. Follow OWASP security guidelines and ensure all third-party integrations meet security requirements. Data protection and user privacy MUST be non-negotiable requirements.

### V. Testing Discipline (NON-NEGOTIABLE)

Comprehensive testing with Pest PHP is MANDATORY for all features. Write unit tests, feature tests, and browser E2E tests. Maintain high test coverage and use factories for test data. Follow TDD principles where appropriate: write tests first, ensure they fail, then implement functionality. All code changes MUST include corresponding tests that validate both happy paths and edge cases. Mock external services appropriately and ensure all tests pass before merging.

## Development Workflow

### Environment Management

Use Docker Compose for consistent development environments. Run `make up` to start development services, `make dev` for development with logs. Access application at `https://kaikyo.test` with Vite dev server on `http://localhost:5173`. All development tasks MUST use make commands or docker compose exec - NEVER run PHP directly in shell. This ensures consistent environments and prevents dependency conflicts.

### Code Quality Standards

Enforce strict code quality through automated tooling: Laravel Pint for PHP formatting, PHPStan at maximum level for static analysis, ESLint and Prettier for JavaScript/TypeScript. All code MUST follow established conventions and pass all quality gates before merging. Use Value Objects and DTOs for data transfer, enums for status handling, and implement proper error handling throughout the application.

## Security Guidelines

### Authentication & Authorization

Implement robust authentication using Laravel Jetstream with Fortify. Use role-based access control with Spatie Permission package. Validate all user inputs and implement proper CSRF protection. Use Laravel Sanctum for API authentication and ensure all endpoints are properly secured. Authorization checks MUST be implemented at both route and policy levels.

### Data Protection

Never commit secrets or sensitive configuration to version control. Use environment variables for all configuration and implement proper encryption for sensitive data. Follow OWASP security guidelines and ensure compliance with relevant data protection regulations. All user data MUST be properly validated, sanitized, and protected according to security best practices.

## Testing Standards

### PHP Testing

Use Pest PHP for all testing. Write comprehensive unit tests, feature tests, and browser E2E tests. Maintain high test coverage and use factories for test data. Mock external services and ensure all tests pass before deployment. Test both success and failure paths, and include proper assertions for all business logic.

### Frontend Testing

Test Vue components using appropriate testing frameworks. Validate user interactions, form submissions, and API integrations. Use proper test data and mock API calls appropriately. Ensure all frontend functionality is thoroughly tested and validated across different browsers and devices when applicable.

## Performance Guidelines

### Backend Performance

Use Laravel Octane for improved performance and implement proper caching strategies with Redis. Optimize database queries through proper indexing, eager loading, and query optimization. Use queue jobs for heavy operations and implement proper monitoring. All performance optimizations MUST be validated through proper benchmarking and testing.

### Frontend Performance

Use Vite for fast builds and HMR. Implement code splitting, optimize images and assets, and use proper caching headers. Minimize bundle size through tree shaking and proper dependency management. Monitor frontend performance and implement appropriate optimizations based on real user metrics.

## Docker Development

### Container Management

Always check running containers with `docker compose ps` before issuing new commands. NEVER run `docker compose up -d` unless `docker compose ps` returns no containers. Avoid starting duplicate containers by verifying container status first. Use existing containers instead of creating new ones when possible. Follow proper container naming conventions and ensure all services are properly configured.

### Environment Consistency

Use Docker Compose for local development to ensure consistency across all environments. Mount source code for live reloading and ensure all dependencies are properly containerized. Production deployments MUST use optimized Docker images with proper health checks, restart policies, and SSL/TLS configuration.

## Governance

This constitution supersedes all other development practices and guidelines. All team members MUST comply with these principles without exception. Amendments to this constitution require documentation, team approval, and a migration plan for existing code. All pull requests and code reviews MUST verify compliance with these constitutional principles.

Code reviewers MUST reject any changes that violate constitutional principles. Complexity MUST be justified and documented. Use the project's CLAUDE.md file for runtime development guidance and specific implementation details. Regular compliance reviews MUST be conducted to ensure ongoing adherence to these principles.

**Version**: 1.0.0 | **Ratified**: 2025-09-29 | **Last Amended**: 2025-09-29
