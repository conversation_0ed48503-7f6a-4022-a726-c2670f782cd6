{"permissions": {"allow": ["mcp__serena__list_dir", "mcp__serena__activate_project", "mcp__serena__read_file", "Bash(php artisan make:migration:*)", "<PERSON><PERSON>(composer install:*)", "mcp__serena__replace_symbol_body", "mcp__serena__replace_regex", "mcp__serena__find_file", "Bash(php artisan test:*)", "Bash(php artisan:*)", "<PERSON><PERSON>(composer:*)", "Bash(./vendor/bin/pest:*)", "Bash(npm install:*)", "<PERSON><PERSON>(npx playwright install:*)", "mcp__playwright__playwright_navigate", "Bash(PLAYWRIGHT_BROWSERS_PATH=0 npx playwright install)", "Bash(PLAYWRIGHT_BROWSERS_PATH=0 npx playwright test)", "Bash(PLAYWRIGHT_BROWSERS_PATH=0 npx playwright test playwright-test.spec.js --headed)", "WebFetch(domain:pestphp.com)", "mcp__serena__find_symbol", "mcp__serena__search_for_pattern", "<PERSON><PERSON>(touch:*)", "Bash(git checkout:*)", "mcp__serena__write_memory", "mcp__serena__create_text_file", "mcp__serena__get_symbols_overview", "mcp__serena__insert_after_symbol", "mcp__serena__execute_shell_command", "Bash(PLAYWRIGHT_BROWSERS_PATH=0 npx playwright install chromium)", "mcp__Package_Version__check_npm_versions", "<PERSON><PERSON>(curl:*)", "Bash(php:*)", "Bash(find:*)", "<PERSON><PERSON>(echo:*)", "Bash(unset:*)", "Read(//Users/<USER>/.claude/**)", "Bash(vendor/bin/pint:*)", "Bash(env:*)", "Bash(APP_KEY=base64:EYeMJ92G/hPh93cVb43fcGb4OcnAT/nfdJhwc+FYDiU= php artisan test --filter=DebugManageBillingTest)", "Bash(APP_KEY=base64:EYeMJ92G/hPh93cVb43fcGb4OcnAT/nfdJhwc+FYDiU= DB_DATABASE=database DB_USERNAME=terraform DB_PASSWORD=password php artisan test --filter=DebugManageBillingTest --timeout=30)", "Bash(APP_KEY=base64:EYeMJ92G/hPh93cVb43fcGb4OcnAT/nfdJhwc+FYDiU= DB_DATABASE=database DB_USERNAME=terraform DB_PASSWORD=password php artisan test --filter=DebugManageBillingTest)", "mcp__postgres__query", "Bash(psql:*)", "Bash(APP_KEY=base64:EYeMJ92G/hPh93cVb43fcGb4OcnAT/nfdJhwc+FYDiU= DB_CONNECTION=pgsql DB_HOST=localhost DB_PORT=5432 DB_DATABASE=kaikyo DB_USERNAME=kaikyo DB_PASSWORD=terraform/password php artisan test tests/Feature/DebugManageBillingTest.php --filter=\"debug manage billing endpoint returns 200\" --env=testing)", "Bash(APP_KEY=base64:EYeMJ92G/hPh93cVb43fcGb4OcnAT/nfdJhwc+FYDiU= DB_CONNECTION=pgsql DB_HOST=127.0.0.1 DB_PORT=5432 DB_DATABASE=database DB_USERNAME=terraform DB_PASSWORD=password php artisan test tests/Feature/DebugManageBillingTest.php --filter=\"debug manage billing endpoint returns 200\" --env=testing)", "Bash(redis-cli:*)", "Bash(brew services start:*)", "Bash(DB_CONNECTION=sqlite DB_DATABASE=:memory: php artisan test tests/Feature/DebugSubscriptionsTest.php --filter=\"debug subscriptions controller test setup\")", "Bash(bash:*)", "Read(//Users/<USER>/**)", "Bash(APP_KEY=base64:EYeMJ92G/hPh93cVb43fcGb4OcnAT/nfdJhwc+FYDiU= php artisan test:*)", "Bash(APP_DEBUG=true php artisan test tests/Feature/Http/Controllers/SubscriptionsControllerTest.php --filter=\"successfully changes subscription plan with valid request\" -v)", "Bash(APP_KEY=base64:xTKsXk3vXHmh2ka9HF7BVE8DsOiPXY08tTAJCiwgtEQ= DB_CONNECTION=sqlite DB_DATABASE=:memory: php artisan test tests/Feature/Http/Controllers/SubscriptionsControllerTest.php --filter=\"successfully changes subscription plan with valid request\" --verbose)", "Bash(APP_KEY=base64:xTKsXk3vXHmh2ka9HF7BVE8DsOiPXY08tTAJCiwgtEQ= DB_CONNECTION=sqlite DB_DATABASE=:memory: php artisan test tests/Feature/Http/Controllers/SubscriptionsControllerTest.php --filter=\"fails with validation error for missing plan\")", "Bash(.specify/scripts/bash/check-prerequisites.sh:*)", "Bash(grep:*)"], "deny": [], "ask": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["laravel-boost"]}