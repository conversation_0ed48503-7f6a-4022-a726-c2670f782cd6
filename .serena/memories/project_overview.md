# Kaikyo Project Overview

## Project Purpose

Kaikyo is a modern SaaS application built on <PERSON><PERSON> for stock, forex, and cryptocurrency trading signals. It's based on the Kaikyo starter kit which provides a VILT (Vue, Inertia, Laravel, TailwindCSS) stack foundation.

## Tech Stack

- **Backend**: Laravel 12.x (PHP 8.3+)
- **Frontend**: Vue.js 3 with Inertia.js
- **Database**: PostgreSQL/MySQL with SQLite for testing
- **CSS**: TailwindCSS 4.x
- **Build Tool**: Vite 6.x
- **Payment**: <PERSON>vel Cashier (Stripe) & Paddle
- **Admin Panel**: FilamentPHP 3.x
- **Authentication**: Laravel Jetstream with Fortify
- **API**: Laravel Sanctum
- **Testing**: Pest PHP
- **Code Quality**: <PERSON><PERSON> Pint, <PERSON>, Larastan

## Code Style and Conventions

- **PHP**: Laravel Pint with custom rules (PSR-12 compliant)
- **Strict Types**: All PHP files use `declare(strict_types=1)`
- **Final Classes**: Models are marked as `final`
- **Type Hints**: Full type coverage with PHPDoc annotations
- **Imports**: Fully qualified imports with global namespace import enabled
- **Order**: Class elements ordered (constants, properties, constructor, methods)

## Key Features

- Multi-tenant with team support (Laravel Jetstream)
- Role-based permissions (Spatie Permission)
- Subscription management with trials
- Watchlist management for stocks/forex/crypto
- FilamentPHP admin interface
- Real-time features with Laravel Reverb
- AI integration ready
- Docker production ready

## Database Structure

### Key Models:

- **User**: Main user model with Paddle Billable trait
- **Plan**: Subscription plans (key, name, price, features)
- **Subscription**: User subscriptions with trial support
- **Watchlist**: User watchlists for different asset types
- **Team**: Multi-tenancy support
- **Role/Permission**: RBAC system

### Test Data:

- Admin user: <EMAIL> / password
- Test user: <EMAIL> / password
- Pro plan with 7-day trial
- Sample watchlists for stocks, forex, crypto
