# Task Completion Checklist

## When a task is completed, perform the following steps:

### 1. Code Quality Checks

```bash
# Format code
./vendor/bin/pint --ansi
./vendor/bin/rector --ansi

# Run static analysis
./vendor/bin/phpstan analyse -c phpstan.neon --ansi
```

### 2. Frontend Checks (if applicable)

```bash
# Lint and format
bun run lint
bun run format:check

# Build for production
bun run build
```

### 3. Testing

```bash
# Run all tests
./vendor/bin/pest --parallel

# Run specific tests if needed
./vendor/bin/pest tests/Feature/SpecificTest.php
```

### 4. Database Migrations (if applicable)

```bash
# Check migration status
php artisan migrate:status

# Run fresh migrations in testing
php artisan migrate:fresh --seed --env=testing
```

### 5. Cache Operations (if configuration changed)

```bash
# Clear all caches
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

# Rebuild caches for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 6. Generate Documentation (if API changes)

```bash
# Generate API docs
php artisan scribe:generate

# Generate IDE helpers
php artisan ide-helper:generate
php artisan ide-helper:meta
```

### 7. Security Checks

- Review any new dependencies added
- Ensure no sensitive data is committed
- Check environment variables are properly configured
- Validate input sanitization and validation rules

### 8. Performance Checks

- Check for N+1 queries with Laravel Telescope
- Verify caching strategies are in place
- Test loading times for critical pages

### 9. Git Operations

```bash
# Stage changes
git add .

# Commit with descriptive message
git commit -m "feat: add user verification criteria checking"

# Push to remote
git push origin main
```

### 10. Documentation Updates

- Update README.md if functionality changed
- Update API documentation if endpoints changed
- Update inline code comments if complex logic added
