# Suggested Commands

## Development Commands

### Laravel Artisan Commands

```bash
# Start development environment
php artisan serve

# Run queue worker
php artisan queue:listen --tries=1

# View logs
php artisan pail --timeout=0

# Database operations
php artisan migrate
php artisan migrate:fresh --seed
php artisan migrate:rollback --step=1

# Clear caches
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

# Generate IDE helpers
php artisan ide-helper:generate
php artisan ide-helper:meta
```

### Composer Scripts

```bash
# Start full development environment
composer run dev

# Setup project
composer run setup

# Run tests
composer run test
./vendor/bin/pest --parallel

# Code analysis
composer run analyse
./vendor/bin/phpstan analyse -c phpstan.neon --ansi

# Code formatting
composer run format
./vendor/bin/pint --ansi
./vendor/bin/rector --ansi
```

### Node/Bun Commands

```bash
# Install dependencies
bun install

# Development build with watch
bun run dev

# Production build
bun run build

# Linting and formatting
bun run lint
bun run format
bun run format:check
```

### Docker Commands (if using Docker)

```bash
# Start development environment
make up
make dev

# Access containers
make shell      # Web container
make shell-dev  # Dev container

# Database operations
make migrate
make fresh

# View logs
make logs

# Clean up
make down
make clean
```

### Testing Commands

```bash
# Run all tests
./vendor/bin/pest --parallel

# Run specific test suite
./vendor/bin/pest tests/Feature
./vendor/bin/pest tests/Unit

# Run with coverage
./vendor/bin/pest --coverage
```

### System Commands (macOS)

```bash
# File operations
ls -la
find . -name "*.php" -type f
grep -r "pattern" app/

# Git operations
git status
git add .
git commit -m "message"
git push origin main

# Process management
ps aux | grep php
killall php
```
