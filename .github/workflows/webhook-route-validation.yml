name: Webhook Route Validation

on:
  push:
    branches: [ main, features/** ]
  pull_request:
    branches: [ main, features/** ]
  workflow_dispatch:

jobs:
  webhook-route-validation:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.3'
        extensions: mbstring, xml, ctype, iconv, intl, pdo_sqlite, curl, zip
        coverage: none

    - name: Cache composer dependencies
      uses: actions/cache@v3
      with:
        path: vendor
        key: composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          composer-

    - name: Install PHP dependencies
      run: |
        composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev

    - name: Setup environment
      run: |
        cp .env.example .env
        php artisan key:generate

    - name: Run webhook route validation
      run: |
        chmod +x scripts/check-webhook-routes.sh
        ./scripts/check-webhook-routes.sh

    - name: Verify Paddle configuration
      run: |
        php artisan tinker --execute="
        echo '🔍 Checking Paddle configuration...';
        echo 'Cashier config:';
        print_r(config('cashier.paddle', 'Not configured'));

        echo 'Webhook secret configured: ' . (config('cashier.paddle.webhook.secret') ? '✅ Yes' : '❌ No');
        echo 'Paddle routes disabled: ' . (config('cashier.ignore_routes') ? '✅ Yes' : '❌ No');
        "

    - name: Generate route list report
      if: failure()
      run: |
        echo "📋 Full route list for debugging:"
        php artisan route:list --columns=uri,name,method

    - name: Upload route validation results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: webhook-validation-results
        path: |
          .env
          storage/logs/*.log
        retention-days: 7