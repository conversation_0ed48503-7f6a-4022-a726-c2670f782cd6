<?php

declare(strict_types=1);

namespace App\Collections;

use App\ValueObjects\Symbol;
use Illuminate\Support\Collection;
use Illuminate\Contracts\Support\Arrayable;

/**
 * @extends Collection<int, Symbol>
 */
final class SymbolsCollection extends Collection
{
    /**
     * @param  iterable<int, Symbol|array<string, mixed>>  $items
     */
    public function __construct($items = [])
    {
        $processedItems = [];
        $itemsArray = is_array($items) ? $items : ($items instanceof Arrayable ? $items->toArray() : []);

        foreach ($itemsArray as $item) {
            if ($item instanceof Symbol) {
                $processedItems[] = $item;
            } elseif (is_array($item)) {
                // Ensure array has string keys
                $stringKeyedArray = [];
                foreach ($item as $key => $value) {
                    $stringKeyedArray[(string) $key] = $value;
                }

                $processedItems[] = Symbol::fromArray($stringKeyedArray);
            }
        }

        parent::__construct($processedItems);
    }

    /**
     * Get symbols as an array of strings
     *
     * @return array<int, string>
     */
    public function toSymbolArray(): array
    {
        /** @var array<int, string> */
        return $this->map(fn (Symbol $item): string => $item->getCode())->values()->all();
    }

    /**
     * Filter active symbols
     */
    public function active(): self
    {
        return $this->filter(fn (Symbol $item): bool => $item->isActive());
    }

    /**
     * Filter inactive symbols
     */
    public function inactive(): self
    {
        return $this->reject(fn (Symbol $item): bool => $item->isActive());
    }

    /**
     * Search for symbols by keyword
     */
    public function searchFor(string $keyword): int|false
    {
        return $this->search(fn (Symbol $item): bool => mb_stripos($item->getCode(), $keyword) !== false || mb_stripos($item->getName(), $keyword) !== false);
    }
}
