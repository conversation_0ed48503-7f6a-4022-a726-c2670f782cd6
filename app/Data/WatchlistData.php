<?php

declare(strict_types=1);

namespace App\Data;

use App\Enums\WatchlistType;
use Spa<PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Attributes\LoadRelation;
use Spa<PERSON>\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;
use Spatie\LaravelData\Attributes\DataCollectionOf;

#[MapInputName(SnakeCaseMapper::class)]
final class WatchlistData extends Data
{
    public function __construct(
        public int $id,
        public string $name,
        public string $description,
        public WatchlistType $type,
        /** @var array<WatchlistAssetData> */
        #[DataCollectionOf(WatchlistAssetData::class)]
        #[LoadRelation]
        public array $assets
    ) {}
}
