<?php

declare(strict_types=1);

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Data;
use Illuminate\Database\Eloquent\Collection;
use <PERSON><PERSON>\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;
use <PERSON><PERSON>\TypeScriptTransformer\Attributes\TypeScript;

#[TypeScript(name: 'PlanData')]
#[MapInputName(SnakeCaseMapper::class)]
final class PlanData extends Data
{
    public function __construct(
        public int $id,
        public string $key,
        public string $name,
        public string $description,
        public int $price,
        public string $currency,
        public string $processorPlanId,
        public string $processorPriceId,
        public bool $active,
        public string $processorType,
        public string $billingInterval,
        // public array $metadata = [],
        /** @var array<PlanFeatureData> */
        public ?Collection $features,
    ) {}
}
