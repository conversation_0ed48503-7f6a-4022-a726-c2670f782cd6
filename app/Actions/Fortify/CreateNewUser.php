<?php

declare(strict_types=1);

namespace App\Actions\Fortify;

use App\Models\Team;
use App\Models\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Laravel\Jetstream\Jetstream;
use Laravel\Paddle\Subscription;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Validator;
use Laravel\Fortify\Contracts\CreatesNewUsers;

final class CreateNewUser implements CreatesNewUsers
{
    use PasswordValidationRules;

    /**
     * Create a newly registered user.
     *
     * This action creates users with no active subscriptions and no payment methods by default.
     * Users are created with basic authentication information, a personal team, and a customer record,
     * but no subscription plans or payment methods are automatically assigned.
     *
     * @param  array<string, string>  $input
     */
    public function create(array $input): User
    {
        Validator::make($input, [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => Arr::get($input, 'password') ? $this->passwordRules() : 'sometimes',
            'terms' => Jetstream::hasTermsAndPrivacyPolicyFeature() ? ['accepted', 'required'] : '',
        ])->validate();

        return DB::transaction(fn () => tap(User::query()->create([
            'name' => $input['name'],
            'email' => $input['email'],
            'password' => Arr::get($input, 'password') ? Hash::make($input['password']) : Str::random(12),
        ]), function (User $user): void {
            $this->createTeam($user);
            $this->createCustomer();
            // Note: New users start with no active subscriptions and no payment methods
        }));
    }

    /**
     * Create a personal team for the user.
     */
    private function createTeam(User $user): void
    {
        $user->ownedTeams()->save(Team::query()->forceCreate([
            'user_id' => $user->id,
            'name' => explode(' ', $user->name, 2)[0]."'s Team",
            'personal_team' => true,
        ]));
    }

    /**
     * Create a billing customer for the user.
     *
     * This method only creates customer metadata when Paddle is configured.
     * No subscriptions or payment methods are created during user registration.
     */
    private function createCustomer(): void
    {
        if (! Config::get('services.paddle.vendor_id')) {
            return;
        }

        // For Paddle, customer creation happens automatically during subscription creation
        // We don't need to explicitly create a customer record like with Stripe
        // Note: This method intentionally does not create any subscriptions or payment methods
    }
}
