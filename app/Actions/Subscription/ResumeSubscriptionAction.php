<?php

declare(strict_types=1);

namespace App\Actions\Subscription;

use Exception;
use App\Models\User;
use App\Enums\SubscriptionStatus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Services\Subscription\SubscriptionService;
use App\Contracts\Subscription\SubscriptionInterface;

final readonly class ResumeSubscriptionAction
{
    public function __construct(
        private SubscriptionService $subscriptionService
    ) {}

    /**
     * Execute the subscription resumption action.
     *
     * @return array{success: bool, message: string}
     */
    public function handle(User $user): array
    {
        // Get the user's cancelled subscription
        $subscription = $this->getCancelledSubscription($user);

        if (! $subscription instanceof SubscriptionInterface) {
            return [
                'success' => false,
                'message' => 'No canceled subscription to resume.',
            ];
        }

        // Check if subscription can be resumed
        $canResume = $this->canResumeSubscription($subscription, $user);
        if (! $canResume['can_resume']) {
            return [
                'success' => false,
                'message' => $canResume['reason'],
            ];
        }

        try {
            Log::info('Attempting subscription resumption', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->getId(),
                'current_status' => $subscription->getStatus(),
            ]);

            // Resume the subscription through the service
            $result = $this->subscriptionService->resumeSubscription($subscription);

            if (! $result) {
                Log::error('Subscription resumption failed in service', [
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->getId(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to resume subscription.',
                ];
            }

            // Clear cached subscription data
            $this->clearUserSubscriptionCache($user);

            Log::info('Subscription resumption successful', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->getId(),
            ]);

            return [
                'success' => true,
                'message' => 'Subscription resumed successfully.',
            ];

        } catch (Exception $exception) {
            Log::error('Subscription resumption failed in action', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->getId(),
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while processing your resumption request.',
            ];
        }
    }

    /**
     * Get resumption preview for the user.
     *
     * @return array{subscription: SubscriptionInterface|null, resumption_details: array<string, mixed>}
     */
    public function getResumptionPreview(User $user): array
    {
        $subscription = $this->getCancelledSubscription($user);

        if (! $subscription instanceof SubscriptionInterface) {
            return [
                'subscription' => null,
                'resumption_details' => [
                    'can_resume' => false,
                    'reason' => 'No canceled subscription found.',
                ],
            ];
        }

        $canResume = $this->canResumeSubscription($subscription, $user);
        $resumptionWindow = $this->getResumptionWindow($subscription);

        $details = [
            'can_resume' => $canResume['can_resume'],
            'reason' => $canResume['reason'],
            'current_plan' => $subscription->getPlanName(),
            'cancelled_at' => $subscription->getEndsAt()?->toISOString(),
            'next_billing_date' => $subscription->getCurrentPeriodEnd()?->toISOString(),
        ];

        if ($resumptionWindow['can_resume']) {
            $details['days_remaining_in_grace_period'] = $resumptionWindow['days_remaining'] ?? null;
        }

        return [
            'subscription' => $subscription,
            'resumption_details' => $details,
        ];
    }

    /**
     * Get the user's cancelled subscription.
     */
    private function getCancelledSubscription(User $user): ?SubscriptionInterface
    {
        // Look for cancelled subscriptions
        $subscription = $user->subscriptions()
            ->whereIn('status', [SubscriptionStatus::CANCELLED, 'canceled'])
            ->orderBy('updated_at', 'desc')
            ->first();

        if ($subscription) {
            return $this->subscriptionService->wrapSubscription($subscription);
        }

        return null;
    }

    /**
     * Check if subscription can be resumed.
     *
     * @return array{can_resume: bool, reason: string}
     */
    private function canResumeSubscription(SubscriptionInterface $subscription, User $user): array
    {
        // Check resumption window
        $resumptionWindow = $this->getResumptionWindow($subscription);
        if (! $resumptionWindow['can_resume']) {
            return [
                'can_resume' => false,
                'reason' => $resumptionWindow['reason'],
            ];
        }

        // Check if user already has an active subscription
        $activeSubscription = $this->subscriptionService->getActiveSubscription($user);
        if ($activeSubscription && ! $activeSubscription->isCancelled()) {
            return [
                'can_resume' => false,
                'reason' => 'You already have an active subscription.',
            ];
        }

        return [
            'can_resume' => true,
            'reason' => '',
        ];
    }

    /**
     * Get resumption window information.
     *
     * @return array{can_resume: bool, reason: string, days_remaining?: int}
     */
    private function getResumptionWindow(SubscriptionInterface $subscription): array
    {
        // Business logic: allow resumption within 30 days of cancellation
        $gracePeriodDays = 30;

        // Get the cancellation date (ends_at or updated_at)
        $subscriptionModel = $subscription->getSubscription();
        $cancelledAt = $subscription->getEndsAt() ?? $subscriptionModel?->updated_at;

        if (! $cancelledAt) {
            return [
                'can_resume' => false,
                'reason' => 'Cannot determine cancellation date.',
            ];
        }

        // Calculate days remaining in grace period
        $gracePeriodEnd = $cancelledAt->addDays($gracePeriodDays);
        $now = now();

        if ($now->gt($gracePeriodEnd)) {
            $daysPast = (int) $now->diffInDays($gracePeriodEnd);

            return [
                'can_resume' => false,
                'reason' => "Subscription cannot be resumed. It was cancelled more than {$gracePeriodDays} days ago ({$daysPast} days ago).",
            ];
        }

        $daysRemaining = (int) $now->diffInDays($gracePeriodEnd);

        return [
            'can_resume' => true,
            'reason' => '',
            'days_remaining' => $daysRemaining,
        ];
    }

    /**
     * Clear user subscription cache.
     */
    private function clearUserSubscriptionCache(User $user): void
    {
        Cache::forget("user_payment_methods_{$user->id}");
        Cache::forget("user_recent_invoices_{$user->id}");
    }
}
