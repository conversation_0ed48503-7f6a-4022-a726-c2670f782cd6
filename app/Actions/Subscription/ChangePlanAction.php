<?php

declare(strict_types=1);

namespace App\Actions\Subscription;

use Exception;
use Carbon\Carbon;
use App\Models\Plan;
use App\Models\User;
use App\Models\Customer;
use App\Models\PaymentMethod;
use App\Enums\SubscriptionStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Services\Subscription\SubscriptionService;
use App\Contracts\Actions\ChangePlanActionInterface;
use App\Contracts\Subscription\SubscriptionInterface;
use App\ValueObjects\PaymentProcessor\ValidationData;
use App\Services\PaymentProcessor\PaymentProcessorFactory;

final readonly class ChangePlanAction implements ChangePlanActionInterface
{
    public function __construct(
        private SubscriptionService $subscriptionService
    ) {}

    /**
     * Execute the plan change action.
     *
     * @return array{success: bool, message: string}
     */
    public function handle(User $user, string|int $planIdentifier): array
    {
        Log::info('ChangePlanAction::handle called', [
            'user_id' => $user->id,
            'plan_identifier' => $planIdentifier,
        ]);

        // TEMPORARY: Bypass additional validation since it's now handled in form request
        // Find the target plan by ID or key with additional validation
        $newPlan = $this->findPlan($planIdentifier);
        if (! $newPlan instanceof Plan) {
            Log::warning('Plan change attempt with non-existent plan', [
                'user_id' => $user->id,
                'plan_identifier' => $planIdentifier,
            ]);

            return [
                'success' => false,
                'message' => 'Plan not found.',
            ];
        }

        // Additional security check - ensure plan is active
        if (! $newPlan->isActive()) {
            Log::warning('Plan change attempt to inactive plan', [
                'user_id' => $user->id,
                'plan_id' => $newPlan->id,
                'plan_key' => $newPlan->key,
            ]);

            return [
                'success' => false,
                'message' => 'The selected plan is not available.',
            ];
        }

        // Validate that the user can change to this plan
        $validationResult = $this->validatePlanChange($user, $newPlan);
        if (! $validationResult['valid']) {
            return [
                'success' => false,
                'message' => $validationResult['message'],
            ];
        }

        // Validate user payment methods before proceeding with plan change
        $paymentMethodValidation = $this->validateUserPaymentMethods($user);
        if (! $paymentMethodValidation['valid']) {
            Log::warning('Payment method validation failed during plan change', [
                'user_id' => $user->id,
                'new_plan_key' => $newPlan->key,
                'validation_message' => $paymentMethodValidation['message'],
            ]);

            return [
                'success' => false,
                'message' => $paymentMethodValidation['message'],
            ];
        }

        // Get the user's active subscription
        Log::info('Getting active subscription for plan change', [
            'user_id' => $user->id,
            'new_plan_key' => $newPlan->key,
        ]);

        $subscription = $this->subscriptionService->getActiveSubscription($user);
        if (! $subscription instanceof SubscriptionInterface) {
            Log::error('No active subscription found during plan change', [
                'user_id' => $user->id,
                'new_plan_key' => $newPlan->key,
            ]);

            return [
                'success' => false,
                'message' => 'No active subscription found.',
            ];
        }

        Log::info('Found active subscription', [
            'user_id' => $user->id,
            'subscription_id' => $subscription->getId(),
            'new_plan_key' => $newPlan->key,
        ]);

        try {
            Log::info('Calculating prorated information', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->getId(),
                'new_plan_key' => $newPlan->key,
            ]);

            // Calculate prorated information
            $proratedInfo = $this->subscriptionService->calculateProratedAmount($subscription, $newPlan);

            Log::info('Prorated info calculated', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->getId(),
                'new_plan_key' => $newPlan->key,
                'prorated_info' => $proratedInfo,
            ]);

            Log::info('Attempting to initiate plan change', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->getId(),
                'current_plan' => $subscription->getPlan(),
                'new_plan_key' => $newPlan->key,
            ]);

            // Initiate the plan change
            $initiateResult = $this->subscriptionService->initiatePlanChange($subscription, $newPlan);

            Log::info('Plan change initiation result', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->getId(),
                'new_plan_key' => $newPlan->key,
                'initiate_result' => $initiateResult,
            ]);

            if (! $initiateResult) {
                Log::error('Plan change failed in service', [
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->getId(),
                    'new_plan_key' => $newPlan->key,
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to change plan. Your payment method may have been declined.',
                ];
            }

            Log::info('Wrapping subscription for execution', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->getId(),
                'new_plan_key' => $newPlan->key,
            ]);

            $wrappedSubscription = $this->subscriptionService->wrapSubscription($subscription);

            Log::info('Attempting to execute plan change', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->getId(),
                'wrapped_subscription_exists' => (bool) $wrappedSubscription,
                'new_plan_key' => $newPlan->key,
            ]);

            if ($wrappedSubscription && ! $this->subscriptionService->executePlanChange($wrappedSubscription, $newPlan)) {
                Log::error('Plan change execution failed', [
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->getId(),
                    'new_plan_key' => $newPlan->key,
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to execute plan change. Please try again or contact support.',
                ];
            }

            Log::info('Plan change executed successfully', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->getId(),
                'new_plan_key' => $newPlan->key,
            ]);

            // Clear cached subscription data
            $this->clearUserSubscriptionCache($user);

            // Generate success message with prorated information
            $safeProratedInfo = [
                'prorated_amount' => $this->safeFloatCast($proratedInfo['prorated_amount'] ?? 0),
                'credit_amount' => $this->safeFloatCast($proratedInfo['credit_amount'] ?? 0),
            ];
            $successMessage = $this->generateSuccessMessage($safeProratedInfo);

            // Create audit trail for successful plan change
            $this->createPlanChangeAuditTrail($user, $subscription, $newPlan, $safeProratedInfo);

            Log::info('Plan change successful', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->getId(),
                'new_plan_key' => $newPlan->key,
                'success_message' => $successMessage,
                'prorated_amount' => $proratedInfo['prorated_amount'] ?? 0,
                'credit_amount' => $proratedInfo['credit_amount'] ?? 0,
            ]);

            return [
                'success' => true,
                'message' => $successMessage,
                'prorated_info' => $proratedInfo,
            ];
        } catch (Exception $exception) {
            Log::error('Plan change failed in action', [
                'user_id' => $user->id,
                'new_plan_key' => $newPlan->key,
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while processing your plan change.',
            ];
        }
    }

    /**
     * Find a plan by ID or key.
     */
    private function findPlan(int|string $identifier): ?Plan
    {
        return is_int($identifier)
            ? Plan::query()->find($identifier)
            : Plan::query()->where('key', $identifier)->first();
    }

    /**
     * Validate plan change eligibility.
     *
     * @return array{valid: bool, message: string}
     */
    private function validatePlanChange(User $user, Plan $newPlan): array
    {
        // Check if user can change to this plan
        if (! $this->subscriptionService->canUserChangeToPlan($user, (string) $newPlan->key)) {
            // Get more specific error message
            $subscription = $this->subscriptionService->getActiveSubscription($user);

            if (! $subscription instanceof SubscriptionInterface) {
                return [
                    'valid' => false,
                    'message' => 'No active subscription found to change.',
                ];
            }

            // Check for pending plan changes
            $hasPending = $this->hasPendingPlanChange($user);
            if ($hasPending) {
                return [
                    'valid' => false,
                    'message' => 'You have a pending plan change. Please wait for it to complete.',
                ];
            }

            // Check subscription eligibility
            if (! $this->subscriptionService->isSubscriptionPeriodEligibleForChange($subscription)) {
                return [
                    'valid' => false,
                    'message' => 'Your current subscription is not eligible for plan changes at this time.',
                ];
            }

            return [
                'valid' => false,
                'message' => 'Cannot change to this plan. Please contact support if you need assistance.',
            ];
        }

        return [
            'valid' => true,
            'message' => '',
        ];
    }

    /**
     * Check if user has pending plan changes.
     */
    private function hasPendingPlanChange(User $user): bool
    {
        return DB::table('subscription_plan_changes')
            ->where('user_id', $user->id)
            ->where('status', SubscriptionStatus::PENDING)
            ->orWhereDate('created_at', '>', now()->subMinutes(5))
            ->exists();
    }

    /**
     * Generate success message with prorated information.
     *
     * @param  array{prorated_amount: int|float, credit_amount: int|float}  $proratedInfo
     */
    private function generateSuccessMessage(array $proratedInfo): string
    {
        $message = 'Plan changed successfully!';

        if ($proratedInfo['prorated_amount'] > 0) {
            $message .= ' You will be charged $'.number_format($proratedInfo['prorated_amount'] / 100, 2).' for the prorated amount.';
        } elseif ($proratedInfo['credit_amount'] > 0) {
            $message .= ' You will receive a credit of $'.number_format($proratedInfo['credit_amount'] / 100, 2).' for the unused portion.';
        }

        return $message;
    }

    /**
     * Clear user subscription cache.
     */
    private function clearUserSubscriptionCache(User $user): void
    {
        Cache::forget("user_payment_methods_{$user->id}");
        Cache::forget("user_recent_invoices_{$user->id}");

        // Clear payment method validation cache
        $user->paymentMethods()->each(function ($paymentMethod) use ($user): void {
            Cache::forget("payment_method_validation_{$user->id}_{$paymentMethod->id}_*");
        });
    }

    /**
     * Validate user payment methods before plan change.
     *
     * @return array{valid: bool, payment_method_id: ?int, message: string}
     */
    private function validateUserPaymentMethods(User $user): array
    {
        Log::info('Starting payment method validation', [
            'user_id' => $user->id,
        ]);

        // Clear payment method cache to ensure we have fresh data
        Log::info('Clearing payment method cache', [
            'user_id' => $user->id,
        ]);

        $user->clearPaymentMethodCache();

        Log::info('Getting usable payment methods', [
            'user_id' => $user->id,
        ]);

        // Get usable payment methods for subscriptions
        $usablePaymentMethods = $user->getUsablePaymentMethods();

        Log::info('Retrieved usable payment methods', [
            'user_id' => $user->id,
            'payment_methods_count' => $usablePaymentMethods->count(),
        ]);

        if ($usablePaymentMethods->isEmpty()) {
            Log::warning('No valid payment methods available for plan change', [
                'user_id' => $user->id,
                'payment_methods_count' => $user->paymentMethods()->count(),
            ]);

            return [
                'valid' => false,
                'payment_method_id' => null,
                'message' => 'No valid payment methods available for plan changes. Please add a payment method.',
            ];
        }

        Log::info('Checking default payment method', [
            'user_id' => $user->id,
        ]);

        // Try default payment method first (priority-based fallback)
        $defaultPaymentMethod = $user->getDefaultPaymentMethod();
        if ($defaultPaymentMethod instanceof PaymentMethod) {
            Log::info('Found default payment method, validating locally', [
                'user_id' => $user->id,
                'payment_method_id' => $defaultPaymentMethod->getKey(),
            ]);

            $validation = $this->validatePaymentMethodLocally($defaultPaymentMethod, $user);
            if ($validation['valid']) {
                Log::info('Local validation passed, validating with processor', [
                    'user_id' => $user->id,
                    'payment_method_id' => $defaultPaymentMethod->getKey(),
                ]);

                // Validate with processor for the default payment method
                $processorValidation = $this->validatePaymentMethodWithProcessor($defaultPaymentMethod);
                if ($processorValidation['valid']) {
                    Log::info('Default payment method validated successfully', [
                        'user_id' => $user->id,
                        'payment_method_id' => $this->safeIntCast($defaultPaymentMethod->getKey()),
                        'processor_type' => $defaultPaymentMethod->processor_type,
                    ]);

                    return [
                        'valid' => true,
                        'payment_method_id' => $this->safeIntCast($defaultPaymentMethod->getKey()),
                        'message' => 'Default payment method validated successfully.',
                    ];
                }
            }
        }

        // Fallback to other payment methods if default fails or doesn't exist
        foreach ($usablePaymentMethods as $paymentMethod) {
            // Skip the default payment method since we already tried it
            if ($defaultPaymentMethod && $paymentMethod->getKey() === $defaultPaymentMethod->getKey()) {
                continue;
            }

            if ($paymentMethod instanceof PaymentMethod) {
                $localValidation = $this->validatePaymentMethodLocally($paymentMethod, $user);
                if ($localValidation['valid']) {
                    $processorValidation = $this->validatePaymentMethodWithProcessor($paymentMethod);
                    if ($processorValidation['valid']) {
                        Log::info('Alternative payment method validated successfully', [
                            'user_id' => $user->id,
                            'payment_method_id' => $this->safeIntCast($paymentMethod->getKey()),
                            'processor_type' => $paymentMethod->processor_type,
                        ]);

                        return [
                            'valid' => true,
                            'payment_method_id' => $this->safeIntCast($paymentMethod->getKey()),
                            'message' => 'Payment method validated successfully.',
                        ];
                    }
                }
            }
        }

        Log::error('No valid payment methods after validation', [
            'user_id' => $user->id,
            'usable_methods_count' => $usablePaymentMethods->count(),
        ]);

        return [
            'valid' => false,
            'payment_method_id' => null,
            'message' => 'No valid payment methods available. Please check your payment methods or contact support.',
        ];
    }

    /**
     * Validate payment method locally (defensive validation).
     *
     * @return array{valid: bool, message: string}
     */
    private function validatePaymentMethodLocally(PaymentMethod $paymentMethod, User $user): array
    {
        // Check if payment method belongs to the user
        if (! $paymentMethod->isValidForUser($user->id)) {
            return [
                'valid' => false,
                'message' => 'Payment method ownership validation failed.',
            ];
        }

        // Check if payment method has a valid processor ID
        if (! $paymentMethod->hasValidProcessorId()) {
            return [
                'valid' => false,
                'message' => 'Payment method processor ID is missing or invalid.',
            ];
        }

        // Check if payment method can be used for subscriptions
        if (! $paymentMethod->canBeUsedForSubscriptions()) {
            return [
                'valid' => false,
                'message' => 'Payment method is not eligible for subscription payments.',
            ];
        }

        // Check if payment method is expired (for credit cards)
        if ($paymentMethod->isExpired()) {
            return [
                'valid' => false,
                'message' => 'Payment method has expired.',
            ];
        }

        // Check if payment method has recent validation results (5 minutes TTL)
        $validationStatus = $paymentMethod->getValidationStatus($paymentMethod->processor_type);
        if ($validationStatus && $validationStatus['status'] && is_string($validationStatus['validated_at'])) {
            $validatedAt = Carbon::parse($validationStatus['validated_at']);
            if ($validatedAt->diffInMinutes(now()) < 5) {
                return [
                    'valid' => true,
                    'message' => 'Payment method validated successfully (cached).',
                ];
            }
        }

        return [
            'valid' => true,
            'message' => 'Payment method passed local validation.',
        ];
    }

    /**
     * Validate payment method with processor.
     *
     * @return array{valid: bool, message: string}
     */
    private function validatePaymentMethodWithProcessor(PaymentMethod $paymentMethod): array
    {
        // Add cache to prevent redundant Paddle API calls during plan change
        $cacheKey = "payment_method_validation_{$paymentMethod->getKey()}_{$paymentMethod->processor_type}";
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult !== null) {
            Log::info('Using cached payment method validation result', [
                'payment_method_id' => $paymentMethod->getKey(),
                'processor_type' => $paymentMethod->processor_type,
            ]);

            return $cachedResult;
        }

        Log::info('Starting processor validation', [
            'payment_method_id' => $paymentMethod->getKey(),
            'processor_type' => $paymentMethod->processor_type,
        ]);

        try {
            Log::info('Getting customer for payment method validation', [
                'payment_method_id' => $paymentMethod->getKey(),
                'user_id' => $paymentMethod->user_id,
                'processor_type' => $paymentMethod->processor_type,
            ]);

            // Get customer for the payment method using the correct processor
            $customer = $this->getCustomerForUser($paymentMethod->user_id, $paymentMethod->processor_type);
            if (! $customer instanceof Customer) {
                Log::warning('Customer not found for payment method validation', [
                    'user_id' => $paymentMethod->user_id,
                    'payment_method_id' => $paymentMethod->getKey(),
                ]);

                return [
                    'valid' => false,
                    'message' => 'Customer information not found for payment validation.',
                ];
            }

            // Create payment processor instance
            $paymentProcessor = PaymentProcessorFactory::make($paymentMethod->processor_type);

            Log::info('Calling paymentProcessor->validatePaymentMethod', [
                'payment_method_id' => $paymentMethod->getKey(),
                'processor_type' => $paymentMethod->processor_type,
            ]);

            // Perform validation and get ValidationData result
            $validationResult = $paymentProcessor->validatePaymentMethod($customer, $paymentMethod);

            Log::info('PaymentProcessor validation completed', [
                'payment_method_id' => $paymentMethod->getKey(),
                'processor_type' => $paymentMethod->processor_type,
                'is_valid' => $validationResult->isValid(),
            ]);

            Log::info('validatePaymentMethod returned to ChangePlan', [
                'payment_method_id' => $paymentMethod->getKey(),
                'is_valid' => $validationResult->isValid(),
            ]);

            Log::info('Starting post-validation logic', [
                'payment_method_id' => $paymentMethod->getKey(),
                'is_valid' => $validationResult->isValid(),
            ]);

            if ($validationResult->isValid()) {
                // Store validation result in payment method metadata
                /** @var array<string, mixed> $validationResultData */
                $validationResultData = $validationResult->toArray();
                $paymentMethod->markAsValidated(
                    $paymentMethod->processor_type,
                    $validationResultData
                );

                Log::info('Payment method validated with processor', [
                    'user_id' => $paymentMethod->user_id,
                    'payment_method_id' => $this->safeIntCast($paymentMethod->getKey()),
                    'processor_type' => $paymentMethod->processor_type,
                    'validation_result' => $validationResult->toArray(),
                ]);

                $result = [
                    'valid' => true,
                    'message' => 'Payment method validated successfully with processor.',
                ];

                // Cache the result for 5 minutes to prevent redundant API calls
                Cache::put($cacheKey, $result, 300);

                return $result;
            }

            // Mark payment method as invalid
            $paymentMethod->markAsInvalid(
                $paymentMethod->processor_type,
                $validationResult->reason ?? 'Payment method validation failed.'
            );

            Log::warning('Payment method validation failed with processor', [
                'user_id' => $paymentMethod->user_id,
                'payment_method_id' => $this->safeIntCast($paymentMethod->getKey()),
                'processor_type' => $paymentMethod->processor_type,
                'error' => $validationResult->reason,
            ]);

            $result = [
                'valid' => false,
                'message' => $validationResult->reason ?? 'Payment method validation failed.',
            ];

            // Cache the failed result for 1 minute to prevent rapid retry loops
            Cache::put($cacheKey, $result, 60);

            return $result;

        } catch (Exception $exception) {
            Log::error('Payment method processor validation error', [
                'user_id' => $paymentMethod->user_id,
                'payment_method_id' => $paymentMethod->id,
                'processor_type' => $paymentMethod->processor_type,
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);

            // Don't fail the entire plan change due to processor validation errors
            // Log the error but allow proceeding with local validation only
            return [
                'valid' => true,
                'message' => 'Processor validation unavailable, using local validation only.',
            ];
        }
    }

    /**
     * Safely cast mixed value to int.
     */
    private function safeIntCast(mixed $value): int
    {
        if (is_int($value)) {
            return $value;
        }

        if (is_numeric($value)) {
            return (int) $value;
        }

        return 0;
    }

    /**
     * Safely cast mixed value to float.
     */
    private function safeFloatCast(mixed $value): float
    {
        if (is_float($value)) {
            return $value;
        }

        if (is_int($value)) {
            return (float) $value;
        }

        if (is_numeric($value)) {
            return (float) $value;
        }

        return 0.0;
    }

    /**
     * Get customer for user.
     */
    private function getCustomerForUser(int $userId, string $processorType): ?Customer
    {
        Log::info('getCustomerForUser called', [
            'user_id' => $userId,
            'processor_type' => $processorType,
        ]);

        // Use the User model's polymorphic relationship with processor key to ensure correct customer
        $user = User::query()->find($userId);
        if (! $user) {
            Log::warning('User not found in getCustomerForUser', [
                'user_id' => $userId,
                'processor_type' => $processorType,
            ]);

            return null;
        }

        Log::info('User found, calling getCustomerFor', [
            'user_id' => $userId,
            'processor_type' => $processorType,
        ]);

        return $user->getCustomerFor($processorType);
    }

    /**
     * Create audit trail for plan change.
     *
     * @param  array{prorated_amount?: int|float, credit_amount?: int|float}  $proratedInfo
     */
    private function createPlanChangeAuditTrail(User $user, mixed $subscription, Plan $newPlan, array $proratedInfo = []): void
    {
        $subscriptionId = null;
        $currentPlan = null;

        try {
            $currentPlan = is_object($subscription) && method_exists($subscription, 'getPlan') ? $subscription->getPlan() : null;
            if (is_object($subscription) && method_exists($subscription, 'getId')) {
                $subscriptionId = $subscription->getId();
            }

            $currentPlanId = $currentPlan && is_object($currentPlan) && property_exists($currentPlan, 'id') ? $currentPlan->id : null;

            DB::table('subscription_plan_changes')->insert([
                'user_id' => $user->id,
                'subscription_id' => $subscriptionId,
                'from_plan_id' => $currentPlanId,
                'to_plan_id' => $newPlan->id,
                'status' => SubscriptionStatus::COMPLETED,
                'prorated_amount' => $proratedInfo['prorated_amount'] ?? 0,
                'credit_amount' => $proratedInfo['credit_amount'] ?? 0,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            Log::info('Plan change audit trail created', [
                'user_id' => $user->id,
                'subscription_id' => $subscriptionId,
                'from_plan_id' => $currentPlanId,
                'to_plan_id' => $newPlan->id,
                'prorated_amount' => $proratedInfo['prorated_amount'] ?? 0,
                'credit_amount' => $proratedInfo['credit_amount'] ?? 0,
            ]);
        } catch (Exception $exception) {
            Log::error('Failed to create plan change audit trail', [
                'user_id' => $user->id,
                'subscription_id' => $subscriptionId,
                'new_plan_id' => $newPlan->id,
                'error' => $exception->getMessage(),
            ]);
            // Don't fail the plan change if audit trail creation fails
        }
    }
}
