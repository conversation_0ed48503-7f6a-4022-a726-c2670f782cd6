<?php

declare(strict_types=1);

namespace App\Actions\Subscription;

use Exception;
use App\Models\User;
use Carbon\CarbonInterface;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Services\Subscription\SubscriptionService;
use App\Contracts\Subscription\SubscriptionInterface;

final readonly class CancelSubscriptionAction
{
    public function __construct(
        private SubscriptionService $subscriptionService
    ) {}

    /**
     * Execute the subscription cancellation action.
     *
     * @return array{success: bool, message: string}
     */
    public function handle(User $user): array
    {
        // Get the user's active subscription
        $subscription = $this->subscriptionService->getActiveSubscription($user);

        if (! $subscription instanceof SubscriptionInterface) {
            return [
                'success' => false,
                'message' => 'No active subscription to cancel.',
            ];
        }

        // Check if subscription is already cancelled
        if ($subscription->isCancelled()) {
            return [
                'success' => false,
                'message' => 'Subscription is already cancelled.',
            ];
        }

        try {
            Log::info('Attempting subscription cancellation', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->getId(),
                'current_status' => $subscription->getStatus(),
            ]);

            // Cancel the subscription through the service
            $result = $this->subscriptionService->cancelSubscription($subscription);

            if (! $result) {
                Log::error('Subscription cancellation failed in service', [
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->getId(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to cancel subscription.',
                ];
            }

            // Clear cached subscription data
            $this->clearUserSubscriptionCache($user);

            Log::info('Subscription cancellation successful', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->getId(),
            ]);

            return [
                'success' => true,
                'message' => 'Subscription cancelled successfully.',
            ];

        } catch (Exception $exception) {
            Log::error('Subscription cancellation failed in action', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->getId(),
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while processing your cancellation request.',
            ];
        }
    }

    /**
     * Get the user's subscription before cancellation for preview.
     *
     * @return array{subscription: SubscriptionInterface|null, cancellation_details: array<string, mixed>}
     */
    public function getCancellationPreview(User $user): array
    {
        $subscription = $this->subscriptionService->getActiveSubscription($user);

        if (! $subscription instanceof SubscriptionInterface) {
            return [
                'subscription' => null,
                'cancellation_details' => [
                    'can_cancel' => false,
                    'reason' => 'No active subscription found.',
                ],
            ];
        }

        if ($subscription->isCancelled()) {
            return [
                'subscription' => $subscription,
                'cancellation_details' => [
                    'can_cancel' => false,
                    'reason' => 'Subscription is already cancelled.',
                ],
            ];
        }

        // Get cancellation details from subscription
        $details = $this->getCancellationDetails($subscription);

        return [
            'subscription' => $subscription,
            'cancellation_details' => array_merge($details, [
                'can_cancel' => true,
                'reason' => '',
            ]),
        ];
    }

    /**
     * Get detailed cancellation information.
     *
     * @return array{access_until: string|null, will_refund: bool, refund_amount: float|null}
     */
    private function getCancellationDetails(SubscriptionInterface $subscription): array
    {
        $currentPeriodEnd = $subscription->getCurrentPeriodEnd();
        $accessUntil = $currentPeriodEnd instanceof CarbonInterface ? $currentPeriodEnd->toISOString() : null;

        // Check if subscription is eligible for refund
        $willRefund = $this->isEligibleForRefund();
        $refundAmount = $willRefund ? $this->calculateRefundAmount() : null;

        return [
            'access_until' => $accessUntil,
            'will_refund' => $willRefund,
            'refund_amount' => $refundAmount,
        ];
    }

    /**
     * Check if subscription is eligible for refund upon cancellation.
     */
    private function isEligibleForRefund(): bool
    {
        // Implement refund eligibility logic based on business rules
        // For now, we'll return false as refunds typically require specific business logic
        return false;
    }

    /**
     * Calculate refund amount if applicable.
     */
    private function calculateRefundAmount(): null
    {
        // Implement refund calculation logic
        // This would typically involve calculating prorated amounts
        return null;
    }

    /**
     * Clear user subscription cache.
     */
    private function clearUserSubscriptionCache(User $user): void
    {
        Cache::forget("user_payment_methods_{$user->id}");
        Cache::forget("user_recent_invoices_{$user->id}");
    }
}
