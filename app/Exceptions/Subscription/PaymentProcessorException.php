<?php

declare(strict_types=1);

namespace App\Exceptions\Subscription;

use Exception;
use Throwable;

/**
 * Exception thrown when payment processor operations fail during subscription changes.
 */
final class PaymentProcessorException extends Exception
{
    public function __construct(
        string $message = '',
        int $code = 0,
        ?Throwable $previous = null,
        private array $processorResponse = [],
        private readonly string $processorName = ''
    ) {
        parent::__construct($message, $code, $previous);
    }

    /**
     * Create an exception for initialization failures.
     */
    public static function initializationFailed(string $processorName, Throwable $previous): self
    {
        return new self(
            "Failed to initialize {$processorName} payment processor",
            0,
            $previous,
            [],
            $processorName
        );
    }

    /**
     * Create an exception for checkout failures.
     */
    public static function checkoutFailed(string $processorName, string $message, ?Throwable $previous = null): self
    {
        return new self(
            "Checkout failed: {$message}",
            0,
            $previous,
            [],
            $processorName
        );
    }

    /**
     * Create an exception for subscription failures.
     */
    public static function subscriptionFailed(string $processorName, string $message, ?Throwable $previous = null): self
    {
        return new self(
            "Subscription failed: {$message}",
            0,
            $previous,
            [],
            $processorName
        );
    }

    /**
     * Create an exception for payment processing failures.
     */
    public static function paymentProcessingFailed(string $processorName, string $message, ?Throwable $previous = null): self
    {
        return new self(
            "Payment processing failed: {$message}",
            0,
            $previous,
            [],
            $processorName
        );
    }

    /**
     * Create an exception for subscription processing failures.
     */
    public static function subscriptionProcessingFailed(string $processorName, string $message, ?Throwable $previous = null): self
    {
        return new self(
            "Subscription processing failed: {$message}",
            0,
            $previous,
            [],
            $processorName
        );
    }

    /**
     * Create an exception for webhook handling failures.
     */
    public static function webhookHandlingFailed(string $processorName, string $message, ?Throwable $previous = null): self
    {
        return new self(
            "Webhook handling failed: {$message}",
            0,
            $previous,
            [],
            $processorName
        );
    }

    /**
     * Create an exception for webhook verification failures.
     */
    public static function webhookVerificationFailed(string $processorName, string $message, ?Throwable $previous = null): self
    {
        return new self(
            "Webhook verification failed: {$message}",
            0,
            $previous,
            [],
            $processorName
        );
    }

    /**
     * Create an exception for subscription cancellation failures.
     */
    public static function subscriptionCancellationFailed(string $processorName, string $message, ?Throwable $previous = null): self
    {
        return new self(
            "Subscription cancellation failed: {$message}",
            0,
            $previous,
            [],
            $processorName
        );
    }

    /**
     * Create an exception for subscription resumption failures.
     */
    public static function subscriptionResumptionFailed(string $processorName, string $message, ?Throwable $previous = null): self
    {
        return new self(
            "Subscription resumption failed: {$message}",
            0,
            $previous,
            [],
            $processorName
        );
    }

    /**
     * Create an exception for subscription update failures.
     */
    public static function subscriptionUpdateFailed(string $processorName, string $message, ?Throwable $previous = null): self
    {
        return new self(
            "Subscription update failed: {$message}",
            0,
            $previous,
            [],
            $processorName
        );
    }

    /**
     * Create an exception for setup intent creation failures.
     */
    public static function setupIntentCreationFailed(string $processorName, string $message, ?Throwable $previous = null): self
    {
        return new self(
            "Failed to create setup intent with {$processorName}: {$message}",
            0,
            $previous,
            [],
            $processorName
        );
    }

    /**
     * Get the payment processor response that caused the exception.
     *
     * @return array<string, mixed>
     */
    public function getProcessorResponse(): array
    {
        return $this->processorResponse;
    }

    /**
     * Get the name of the payment processor that failed.
     */
    public function getProcessorName(): string
    {
        return $this->processorName;
    }

    /**
     * Check if this is a temporary/retryable error.
     */
    public function isRetryable(): bool
    {
        // First, inspect processorResponse for HTTP status codes and headers
        if ($this->isRetryableFromProcessorResponse()) {
            return true;
        }

        // Fall back to message pattern checks
        return $this->isRetryableFromMessage();
    }

    /**
     * Check if error is retryable based on processor response HTTP status codes and headers.
     */
    private function isRetryableFromProcessorResponse(): bool
    {
        if ($this->processorResponse === []) {
            return false;
        }

        // Check for HTTP status codes that indicate retryable errors
        if (filled($this->processorResponse['status'])) {
            $statusCode = (int) $this->processorResponse['status'];

            // 408 Request Timeout
            if ($statusCode === 408) {
                return true;
            }

            // 429 Too Many Requests
            if ($statusCode === 429) {
                return true;
            }

            // Any 5xx server error (retryable)
            if ($statusCode >= 500 && $statusCode < 600) {
                return true;
            }
        }

        // Check for retry-after header
        if (filled($this->processorResponse['headers'])) {
            $headers = $this->processorResponse['headers'];

            // Check for retry-after header (case-insensitive)
            foreach ($headers as $key => $value) {
                if (mb_strtolower((string) $key) === 'retry-after') {
                    return true;
                }
            }
        }

        // Check for rate limiting indicators in response body
        if (filled($this->processorResponse['body'])) {
            $body = is_array($this->processorResponse['body'])
                ? json_encode($this->processorResponse['body'])
                : $this->processorResponse['body'];

            $bodyLower = mb_strtolower((string) $body);

            $rateLimitPatterns = [
                'rate limit',
                'rate_limit',
                'too many requests',
                'throttled',
                'quota exceeded',
                'retry after',
            ];

            foreach ($rateLimitPatterns as $pattern) {
                if (str_contains($bodyLower, $pattern)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check if error is retryable based on exception message patterns.
     */
    private function isRetryableFromMessage(): bool
    {
        // Common retryable error patterns
        $retryablePatterns = [
            'network',
            'timeout',
            'temporary',
            'rate limit',
            'service unavailable',
            'connection',
            'unreachable',
            'gateway timeout',
            'bad gateway',
        ];

        $message = mb_strtolower($this->getMessage());

        foreach ($retryablePatterns as $pattern) {
            if (str_contains($message, $pattern)) {
                return true;
            }
        }

        return false;
    }
}
