<?php

declare(strict_types=1);

namespace App\Exceptions\Subscription;

use Exception;

/**
 * Exception thrown when a plan change operation fails.
 */
final class PlanChangeException extends Exception
{
    /**
     * @param  array<string, mixed>  $context
     */
    public function __construct(string $message = '', int $code = 0, ?Exception $previous = null, private array $context = [])
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * Get additional context information about the exception.
     *
     * @return array<string, mixed>
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Set additional context information.
     *
     * @param  array<string, mixed>  $context
     */
    public function setContext(array $context): self
    {
        $this->context = $context;

        return $this;
    }
}
