<?php

declare(strict_types=1);

namespace App\Exceptions\Subscription;

use Exception;

/**
 * Exception thrown when a subscription cannot be found during operations.
 */
final class SubscriptionNotFoundException extends Exception
{
    public function __construct(
        string $message = '',
        int $code = 0,
        ?Exception $previous = null,
        private readonly string $subscriptionId = '',
        private readonly int $userId = 0
    ) {
        parent::__construct($message, $code, $previous);
    }

    /**
     * Create exception for missing subscription by ID.
     */
    public static function byId(string $subscriptionId, int $userId = 0): self
    {
        return new self(
            "Subscription with ID '{$subscriptionId}' not found",
            404,
            null,
            $subscriptionId,
            $userId
        );
    }

    /**
     * Create exception for user with no active subscription.
     */
    public static function noActiveSubscription(int $userId): self
    {
        return new self(
            "No active subscription found for user ID '{$userId}'",
            404,
            null,
            '',
            $userId
        );
    }

    /**
     * Get the subscription ID that was not found.
     */
    public function getSubscriptionId(): string
    {
        return $this->subscriptionId;
    }

    /**
     * Get the user ID associated with the missing subscription.
     */
    public function getUserId(): int
    {
        return $this->userId;
    }
}
