<?php

declare(strict_types=1);

namespace App\Exceptions;

use Exception;
use Illuminate\Support\Str;
use App\ValueObjects\PaymentProcessor\ErrorData;

final class PaymentProcessorException extends Exception
{
    private readonly string $correlationId;

    public function __construct(
        private readonly ErrorData $errorData,
        private array $context = [],
        ?Exception $previous = null
    ) {
        $this->correlationId = Str::uuid()->toString();

        parent::__construct(
            $this->errorData->data['user_message'] ?? $this->errorData->message,
            $this->errorData->code,
            $previous
        );
    }

    public static function categories(): array
    {
        return [
            'VALIDATION_ERROR' => 'validation_error',
            'PROCESSOR_ERROR' => 'processor_error',
            'NETWORK_ERROR' => 'network_error',
            'INSUFFICIENT_FUNDS' => 'insufficient_funds',
            'CARD_DECLINED' => 'card_declined',
            'RATE_LIMIT_EXCEEDED' => 'rate_limit_exceeded',
            'AUTHENTICATION_ERROR' => 'authentication_error',
            'CONFIGURATION_ERROR' => 'configuration_error',
            'UNKNOWN_ERROR' => 'unknown_error',
        ];
    }

    public static function create(
        string $errorType,
        string $technicalMessage,
        string $userMessage,
        int $statusCode = 500,
        array $context = [],
        ?Exception $previous = null
    ): self {
        $errorData = new ErrorData(
            errorType: $errorType,
            message: $technicalMessage,
            operation: $context['operation'] ?? 'unknown',
            processor: $context['processor_name'] ?? 'unknown',
            code: $statusCode,
            data: [
                'user_message' => $userMessage,
                'recovery_suggestions' => self::getRecoverySuggestions($errorType),
            ],
            retryable: self::isErrorRetryable($errorType),
            errorCode: $context['processor_error_code'] ?? null,
            processorName: $context['processor_name'] ?? 'unknown'
        );

        return new self($errorData, $context, $previous);
    }

    public function getErrorData(): ErrorData
    {
        return $this->errorData;
    }

    public function getContext(): array
    {
        return $this->context;
    }

    public function getCorrelationId(): string
    {
        return $this->correlationId;
    }

    public function getUserId(): ?string
    {
        $userId = $this->context['user_id'] ?? null;

        return $userId !== null ? (string) $userId : null;
    }

    public function getPlanId(): ?string
    {
        $planId = $this->context['plan_id'] ?? null;

        return $planId !== null ? (string) $planId : null;
    }

    public function getTransactionId(): ?string
    {
        $transactionId = $this->context['transaction_id'] ?? null;

        return $transactionId !== null ? (string) $transactionId : null;
    }

    public function getProcessorName(): string
    {
        return $this->errorData->processorName ?? $this->errorData->processor;
    }

    public function getProcessorErrorCode(): ?string
    {
        return $this->errorData->errorCode;
    }

    public function isRetryable(): bool
    {
        return $this->errorData->retryable;
    }

    public function getRecoverySuggestionsData(): array
    {
        return $this->errorData->data['recovery_suggestions'] ?? [];
    }

    public function getUserMessage(): string
    {
        return $this->errorData->data['user_message'] ?? $this->errorData->message;
    }

    public function getSeverity(): string
    {
        return match ($this->errorData->errorType) {
            'VALIDATION_ERROR' => 'low',
            'NETWORK_ERROR' => 'medium',
            'INSUFFICIENT_FUNDS', 'CARD_DECLINED' => 'medium',
            'PROCESSOR_ERROR', 'AUTHENTICATION_ERROR' => 'high',
            'CONFIGURATION_ERROR' => 'high',
            default => 'medium',
        };
    }

    public function shouldAlert(): bool
    {
        if ($this->getSeverity() === 'high') {
            return true;
        }

        return $this->errorData->errorCode === 'critical_failure';
    }

    public function toArray(): array
    {
        return [
            'error_type' => $this->errorData->errorType,
            'technical_message' => $this->errorData->message,
            'user_message' => $this->errorData->data['user_message'] ?? $this->errorData->message,
            'status_code' => $this->errorData->code,
            'error_code' => $this->errorData->errorCode,
            'processor_name' => $this->errorData->processorName,
            'retryable' => $this->errorData->retryable,
            'recovery_suggestions' => $this->errorData->data['recovery_suggestions'] ?? [],
            'context' => $this->context,
            'correlation_id' => $this->correlationId,
            'severity' => $this->getSeverity(),
            'should_alert' => $this->shouldAlert(),
        ];
    }

    private static function isErrorRetryable(string $errorType): bool
    {
        return in_array($errorType, [
            'NETWORK_ERROR',
            'RATE_LIMIT_EXCEEDED',
            'PROCESSOR_ERROR',
        ]);
    }

    private static function getRecoverySuggestions(string $errorType): array
    {
        return match ($errorType) {
            'VALIDATION_ERROR' => [
                'Please check your payment information and try again.',
                'Ensure all required fields are filled correctly.',
            ],
            'NETWORK_ERROR' => [
                'Please check your internet connection and try again.',
                'If the problem persists, wait a few minutes before retrying.',
            ],
            'INSUFFICIENT_FUNDS' => [
                'Please check your account balance and use a different payment method if needed.',
                'Contact your bank if you believe this is an error.',
            ],
            'CARD_DECLINED' => [
                'Please check your card details or use a different payment method.',
                'Contact your bank if you believe your card should work.',
            ],
            'RATE_LIMIT_EXCEEDED' => [
                'Please wait a few minutes before trying again.',
                'Contact support if this issue persists.',
            ],
            'AUTHENTICATION_ERROR' => [
                'Please log out and log back in to refresh your session.',
                'Contact support if you continue to experience issues.',
            ],
            'CONFIGURATION_ERROR' => [
                'There is a system configuration issue preventing payment processing.',
                'Please contact support and reference this error.',
            ],
            'PROCESSOR_ERROR' => [
                'The payment processor encountered an error.',
                'Please try again or contact support if the problem persists.',
            ],
            default => [
                'An unexpected error occurred while processing your payment.',
                'Please try again or contact support if the problem persists.',
            ],
        };
    }
}
