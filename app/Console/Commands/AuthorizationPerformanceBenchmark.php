<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Exception;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Services\AuthorizationService;
use App\Services\PerformanceMetricsService;

final class AuthorizationPerformanceBenchmark extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'authorization:performance-benchmark 
                            {--iterations=100 : Number of test iterations}
                            {--users=10 : Number of test users to create}
                            {--permissions=50 : Number of permission checks per user}
                            {--warmup=10 : Number of warmup iterations}
                            {--export= : Export results to file}
                            {--cleanup : Clean up test data after benchmark}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run authorization performance benchmarks and stress tests';

    /** @var array<string, array<string, mixed>> */
    private array $benchmarkResults = [];

    /** @var array<int, User> */
    private array $testUsers = [];

    /**
     * Execute the console command.
     */
    public function handle(
        PerformanceMetricsService $metricsService,
        AuthorizationService $authorizationService
    ): int {
        $iterations = (int) $this->option('iterations');
        $userCount = (int) $this->option('users');
        $permissionChecks = (int) $this->option('permissions');
        $warmupIterations = (int) $this->option('warmup');
        $exportPath = $this->option('export');
        $cleanup = $this->option('cleanup');

        $this->info('Starting Authorization Performance Benchmark');
        $this->info("Iterations: {$iterations}, Users: {$userCount}, Permission Checks: {$permissionChecks}");
        $this->newLine();

        try {
            // Setup test environment
            $this->setupTestEnvironment($userCount);

            // Run warmup
            if ($warmupIterations > 0) {
                $this->info("Running {$warmupIterations} warmup iterations...");
                $this->runWarmup($warmupIterations, $authorizationService);
            }

            // Run benchmarks
            $this->runBenchmarks($iterations, $permissionChecks, $authorizationService);

            // Display results
            $this->displayResults();

            // Export results if requested
            if ($exportPath) {
                $this->exportResults($exportPath);
                $this->info("Results exported to: {$exportPath}");
            }

            // Cleanup if requested
            if ($cleanup) {
                $this->cleanupTestData();
                $this->info('Test data cleaned up.');
            }

            return self::SUCCESS;
        } catch (Exception $exception) {
            $this->error("Benchmark failed: {$exception->getMessage()}");

            return self::FAILURE;
        }
    }

    /**
     * Setup test environment with users and permissions.
     */
    private function setupTestEnvironment(int $userCount): void
    {
        $this->info('Setting up test environment...');

        DB::transaction(function () use ($userCount): void {
            // Create test users
            for ($i = 0; $i < $userCount; $i++) {
                $user = User::factory()->create([
                    'name' => "Benchmark User {$i}",
                    'email' => "benchmark.user.{$i}@test.com",
                ]);

                // Assign random roles/permissions for realistic testing
                if ($i % 3 === 0) {
                    $user->assignRole('admin');
                } elseif ($i % 2 === 0) {
                    $user->assignRole('editor');
                } else {
                    $user->assignRole('viewer');
                }

                $this->testUsers[] = $user;
            }
        });

        $this->info("Created {$userCount} test users.");
    }

    /**
     * Run warmup iterations to prepare caches.
     */
    private function runWarmup(int $iterations, AuthorizationService $authorizationService): void
    {
        $progressBar = $this->output->createProgressBar($iterations);
        $progressBar->start();

        for ($i = 0; $i < $iterations; $i++) {
            $user = $this->testUsers[array_rand($this->testUsers)];

            // Perform some authorization checks
            $authorizationService->can($user, 'view');
            $authorizationService->can($user, 'create');
            $authorizationService->can($user, 'delete');

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
    }

    /**
     * Run the main benchmark tests.
     */
    private function runBenchmarks(
        int $iterations,
        int $permissionChecks,
        AuthorizationService $authorizationService
    ): void {
        $this->info('Running benchmark tests...');

        $progressBar = $this->output->createProgressBar($iterations);
        $progressBar->start();

        $results = [
            'authorization_checks' => [],
            'cache_performance' => [],
            'database_queries' => [],
            'memory_usage' => [],
        ];

        for ($i = 0; $i < $iterations; $i++) {
            // Test authorization performance
            $authResults = $this->benchmarkAuthorizationChecks(
                $permissionChecks,
                $authorizationService
            );
            $results['authorization_checks'][] = $authResults;

            // Test cache performance
            $cacheResults = $this->benchmarkCachePerformance();
            $results['cache_performance'][] = $cacheResults;

            // Test database query performance
            $dbResults = $this->benchmarkDatabaseQueries();
            $results['database_queries'][] = $dbResults;

            // Record memory usage
            $results['memory_usage'][] = memory_get_usage(true);

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        $this->benchmarkResults = $this->calculateStatistics($results);
    }

    /**
     * Benchmark authorization checks.
     *
     * @return array<string, float|int>
     */
    private function benchmarkAuthorizationChecks(
        int $checks,
        AuthorizationService $authorizationService
    ): array {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);

        $permissions = ['view', 'create', 'edit', 'delete'];
        $resources = ['users', 'posts', 'comments', 'settings'];

        for ($i = 0; $i < $checks; $i++) {
            $user = $this->testUsers[array_rand($this->testUsers)];
            $permission = $permissions[array_rand($permissions)];
            $resource = $resources[array_rand($resources)];

            $authorizationService->can($user, $permission);
        }

        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);

        return [
            'duration' => ($endTime - $startTime) * 1000, // Convert to milliseconds
            'memory_delta' => $endMemory - $startMemory,
            'checks_per_second' => $checks / ($endTime - $startTime),
        ];
    }

    /**
     * Benchmark cache performance.
     *
     * @return array<string, float|int>
     */
    private function benchmarkCachePerformance(): array
    {
        $startTime = microtime(true);

        // Test cache operations
        for ($i = 0; $i < 50; $i++) {
            $key = "benchmark_test_{$i}";
            Cache::put($key, "test_value_{$i}", 60);
            Cache::get($key);
            Cache::forget($key);
        }

        $endTime = microtime(true);

        return [
            'duration' => ($endTime - $startTime) * 1000,
            'operations_per_second' => 150 / ($endTime - $startTime), // 3 ops per iteration
        ];
    }

    /**
     * Benchmark database queries.
     *
     * @return array<string, float|int>
     */
    private function benchmarkDatabaseQueries(): array
    {
        $startTime = microtime(true);
        $queryCount = DB::getQueryLog() ? count(DB::getQueryLog()) : 0;

        // Perform some database operations
        for ($i = 0; $i < 10; $i++) {
            User::query()->where('email', 'like', '%test%')->count();
            User::with('roles')->limit(5)->get();
        }

        $endTime = microtime(true);
        $finalQueryCount = DB::getQueryLog() ? count(DB::getQueryLog()) : 0;

        return [
            'duration' => ($endTime - $startTime) * 1000,
            'query_count' => $finalQueryCount - $queryCount,
            'queries_per_second' => ($finalQueryCount - $queryCount) / ($endTime - $startTime),
        ];
    }

    /**
     * Calculate statistics from benchmark results.
     *
     * @param  array<string, array<int, array<string, float|int>|int>>  $results
     * @return array<string, array<string, mixed>>
     */
    private function calculateStatistics(array $results): array
    {
        $stats = [];

        foreach ($results as $category => $data) {
            if ($category === 'memory_usage') {
                $stats[$category] = [
                    'min' => $data === [] ? 0 : min($data),
                    'max' => $data === [] ? 0 : max($data),
                    'avg' => $data === [] ? 0.0 : array_sum(array_map('floatval', $data)) / max(1, count($data)),
                    'peak_delta' => $data === [] ? 0 : ((int) max($data) - (int) min($data)),
                ];
            } else {
                $stats[$category] = [
                    'min_duration' => 0,
                    'max_duration' => 0,
                    'avg_duration' => 0,
                    'total_operations' => count($data),
                ];

                if (! empty($data)) {
                    $durations = array_column($data, 'duration');
                    if ($durations !== []) {
                        $stats[$category]['min_duration'] = min($durations);
                        $stats[$category]['max_duration'] = max($durations);
                        $stats[$category]['avg_duration'] = array_sum($durations);
                    }

                    // Add category-specific metrics
                    if ($category === 'authorization_checks' && count($data) > 0) {
                        $checksPerSec = [];
                        foreach ($data as $item) {
                            if (filled($item['checks_per_second']) && is_numeric($item['checks_per_second']) && $item['checks_per_second'] > 0) {
                                $checksPerSec[] = $item['checks_per_second'];
                            }
                        }

                        if ($checksPerSec !== []) {
                            $stats[$category]['avg_checks_per_second'] = array_sum($checksPerSec) / max(1, count($checksPerSec));
                        }
                    }
                }
            }
        }

        return $stats;
    }

    /**
     * Display benchmark results.
     */
    private function displayResults(): void
    {
        $this->newLine();
        $this->info('Benchmark Results:');
        $this->newLine();

        foreach ($this->benchmarkResults as $category => $stats) {
            $this->info(ucfirst(str_replace('_', ' ', $category)).':');

            $tableData = [];
            foreach ($stats as $metric => $value) {
                if (is_array($value)) {
                    $formattedValue = json_encode($value);
                } elseif (is_float($value)) {
                    $formattedValue = number_format($value, 2);
                } else {
                    $formattedValue = is_scalar($value) ? (string) $value : '';
                }

                $tableData[] = [ucfirst(str_replace('_', ' ', $metric)), $formattedValue];
            }

            $this->table(['Metric', 'Value'], $tableData);
            $this->newLine();
        }
    }

    /**
     * Export results to file.
     */
    private function exportResults(string $path): void
    {
        $exportData = [
            'timestamp' => now()->toIso8601String(),
            'configuration' => [
                'iterations' => $this->option('iterations'),
                'users' => $this->option('users'),
                'permissions' => $this->option('permissions'),
                'warmup' => $this->option('warmup'),
            ],
            'results' => $this->benchmarkResults,
        ];

        file_put_contents($path, json_encode($exportData, JSON_PRETTY_PRINT));
    }

    /**
     * Clean up test data.
     */
    private function cleanupTestData(): void
    {
        DB::transaction(function (): void {
            foreach ($this->testUsers as $user) {
                $user->delete();
            }
        });

        // Clear benchmark-related cache entries
        for ($i = 0; $i < 50; $i++) {
            Cache::forget("benchmark_test_{$i}");
        }
    }
}
