<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Exception;
use Carbon\Carbon;
use RuntimeException;
use SimpleXMLElement;
use App\Models\AuditLog;
use Illuminate\Console\Command;
use App\Services\AuditLogService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;

final class AuthorizationAuditExport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'authorization:audit-export 
                            {--from= : Start date (Y-m-d format)}
                            {--to= : End date (Y-m-d format)}
                            {--user= : Filter by user ID}
                            {--action= : Filter by action type}
                            {--resource= : Filter by resource type}
                            {--format=json : Export format (json, csv, xml)}
                            {--output= : Output file path (default: storage/exports)}
                            {--compress : Compress the output file}
                            {--split-by-date : Split export by date}
                            {--include-metadata : Include system metadata}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Export authorization audit logs to various formats';

    /** @var array<int, string> */
    private array $supportedFormats = ['json', 'csv', 'xml'];

    /** @var array<int, array<string, bool|int|string>> */
    private array $exportStats = [];

    /**
     * Execute the console command.
     */
    public function handle(AuditLogService $auditLogService): int
    {
        $this->info('Starting Authorization Audit Export');

        try {
            // Validate inputs
            if (! $this->validateInputs()) {
                return self::FAILURE;
            }

            // Get filters
            $filters = $this->buildFilters();
            $format = $this->option('format');
            $outputPath = $this->option('output') ?: 'exports';
            $compress = $this->option('compress');
            $splitByDate = $this->option('split-by-date');
            $includeMetadata = $this->option('include-metadata');

            $this->info('Retrieving audit logs...');

            // Get audit logs based on filters
            $auditLogs = $this->getAuditLogs($auditLogService, $filters);

            if ($auditLogs->isEmpty()) {
                $this->warn('No audit logs found matching the specified criteria.');

                return self::SUCCESS;
            }

            $this->info("Found {$auditLogs->count()} audit log entries.");

            // Export the data
            if ($splitByDate) {
                $this->exportByDate($auditLogs, $format ?: 'json', $outputPath, $compress, $includeMetadata);
            } else {
                $this->exportSingle($auditLogs, $format ?: 'json', $outputPath, $compress, $includeMetadata);
            }

            // Display export statistics
            $this->displayExportStats();

            $this->info('Audit export completed successfully.');

            return self::SUCCESS;

        } catch (Exception $exception) {
            $this->error("Export failed: {$exception->getMessage()}");

            return self::FAILURE;
        }
    }

    /**
     * Validate command inputs.
     */
    private function validateInputs(): bool
    {
        $format = $this->option('format');
        if (! in_array($format, $this->supportedFormats)) {
            $this->error("Unsupported format: {$format}. Supported formats: ".implode(', ', $this->supportedFormats));

            return false;
        }

        $from = $this->option('from');
        $to = $this->option('to');

        if ($from && ! $this->isValidDate($from)) {
            $this->error('Invalid from date format. Use Y-m-d format.');

            return false;
        }

        if ($to && ! $this->isValidDate($to)) {
            $this->error('Invalid to date format. Use Y-m-d format.');

            return false;
        }

        if ($from && $to && (new Carbon($from))->gt(new Carbon($to))) {
            $this->error('From date cannot be later than to date.');

            return false;
        }

        return true;
    }

    /**
     * Check if date string is valid.
     */
    private function isValidDate(string $date): bool
    {
        try {
            Carbon::createFromFormat('Y-m-d', $date);

            return true;
        } catch (Exception) {
            return false;
        }
    }

    /**
     * Build filters array from command options.
     *
     * @return array<string, int|string|Carbon>
     */
    private function buildFilters(): array
    {
        $filters = [];

        if ($from = $this->option('from')) {
            $filters['from'] = (new Carbon($from))->startOfDay();
        }

        if ($to = $this->option('to')) {
            $filters['to'] = (new Carbon($to))->endOfDay();
        }

        if ($userId = $this->option('user')) {
            $filters['user_id'] = (int) $userId;
        }

        if ($action = $this->option('action')) {
            $filters['action'] = $action;
        }

        if ($resource = $this->option('resource')) {
            $filters['resource'] = $resource;
        }

        return $filters;
    }

    /**
     * Get audit logs from service with filters.
     *
     * @param  array<string, int|string|Carbon>  $filters
     * @return Collection<int, AuditLog>
     */
    private function getAuditLogs(AuditLogService $auditLogService, array $filters): Collection
    {
        return $auditLogService->getFilteredLogs($filters);
    }

    /**
     * Export all logs to a single file.
     *
     * @param  Collection<int, AuditLog>  $auditLogs
     */
    private function exportSingle(
        Collection $auditLogs,
        ?string $format,
        string $outputPath,
        bool $compress,
        bool $includeMetadata
    ): void {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "audit_export_{$timestamp}.{$format}";

        $this->info("Exporting to {$filename}...");

        $content = $this->formatData($auditLogs, $format, $includeMetadata);
        $filePath = $this->saveFile($outputPath, $filename, $content, $compress);

        $this->exportStats[] = [
            'file' => $filePath,
            'records' => $auditLogs->count(),
            'size' => $this->getFileSize($filePath),
            'compressed' => $compress,
        ];
    }

    /**
     * Export logs split by date.
     *
     * @param  Collection<int, AuditLog>  $auditLogs
     */
    private function exportByDate(
        Collection $auditLogs,
        ?string $format,
        string $outputPath,
        bool $compress,
        bool $includeMetadata
    ): void {
        $logsByDate = $auditLogs->groupBy(fn ($log) => $log->created_at->format('Y-m-d'));

        $progressBar = $this->output->createProgressBar($logsByDate->count());
        $progressBar->start();

        foreach ($logsByDate as $date => $logs) {
            $filename = "audit_export_{$date}.{$format}";
            $content = $this->formatData($logs, $format, $includeMetadata);
            $filePath = $this->saveFile($outputPath, $filename, $content, $compress);

            $this->exportStats[] = [
                'file' => $filePath,
                'date' => $date,
                'records' => $logs->count(),
                'size' => $this->getFileSize($filePath),
                'compressed' => $compress,
            ];

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
    }

    /**
     * Format data according to specified format.
     *
     * @param  Collection<int, AuditLog>  $auditLogs
     */
    private function formatData(Collection $auditLogs, ?string $format, bool $includeMetadata): string
    {
        return match ($format ?? 'json') {
            'json' => $this->formatAsJson($auditLogs, $includeMetadata),
            'csv' => $this->formatAsCsv($auditLogs, $includeMetadata),
            'xml' => $this->formatAsXml($auditLogs, $includeMetadata),
            default => throw new \InvalidArgumentException("Unsupported format: {$format}"),
        };
    }

    /**
     * Format data as JSON.
     *
     * @param  Collection<int, AuditLog>  $auditLogs
     */
    private function formatAsJson(Collection $auditLogs, bool $includeMetadata): string
    {
        $data = [
            'export_info' => [
                'timestamp' => now()->toISOString(),
                'total_records' => $auditLogs->count(),
                'filters' => $this->buildFilters(),
            ],
            'audit_logs' => $auditLogs->map(function ($log) use ($includeMetadata): array {
                $data = [
                    'id' => $log->id,
                    'user_id' => $log->user_id,
                    'user_name' => $log->user->name ?? '',
                    'user_email' => $log->user->email ?? '',
                    'action' => $log->action,
                    'model_type' => $log->model_type,
                    'model_id' => $log->model_id,
                    'ip_address' => $log->ip_address,
                    'user_agent' => $log->user_agent,
                    'created_at' => $log->created_at->toISOString(),
                ];

                if ($includeMetadata) {
                    $data['old_values'] = $log->old_values ?? [];
                    $data['new_values'] = $log->new_values ?? [];
                }

                return $data;
            })->toArray(),
        ];

        $result = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);

        throw_if($result === false, new RuntimeException('Failed to encode JSON data'));

        return $result;
    }

    /**
     * Format data as CSV.
     *
     * @param  Collection<int, AuditLog>  $auditLogs
     */
    private function formatAsCsv(Collection $auditLogs, bool $includeMetadata): string
    {
        $headers = [
            'ID', 'User ID', 'User Name', 'User Email', 'Action',
            'Model Type', 'Model ID', 'IP Address', 'User Agent', 'Created At',
        ];

        if ($includeMetadata) {
            $headers[] = 'Old Values';
            $headers[] = 'New Values';
        }

        $csv = implode(',', $headers)."\n";

        foreach ($auditLogs as $log) {
            $row = [
                $log->id,
                $log->user_id,
                $this->escapeCsvValue($log->user->name ?? ''),
                $this->escapeCsvValue($log->user->email ?? ''),
                $this->escapeCsvValue($log->action),
                $this->escapeCsvValue($log->model_type),
                $log->model_id,
                $this->escapeCsvValue($log->ip_address),
                $this->escapeCsvValue($log->user_agent),
                $log->created_at->toISOString(),
            ];

            if ($includeMetadata) {
                $oldValues = json_encode($log->old_values ?? []);
                if ($oldValues === false) {
                    $oldValues = '[]';
                }

                $row[] = $this->escapeCsvValue($oldValues);

                $newValues = json_encode($log->new_values ?? []);
                if ($newValues === false) {
                    $newValues = '[]';
                }

                $row[] = $this->escapeCsvValue($newValues);
            }

            $csv .= implode(',', $row)."\n";
        }

        return $csv;
    }

    /**
     * Format data as XML.
     *
     * @param  Collection<int, AuditLog>  $auditLogs
     */
    private function formatAsXml(Collection $auditLogs, bool $includeMetadata): string
    {
        $xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><audit_export></audit_export>');

        // Add export info
        $exportInfo = $xml->addChild('export_info');
        $exportInfo->addChild('timestamp', now()->toISOString());
        $exportInfo->addChild('total_records', (string) $auditLogs->count());

        // Add filters
        $filters = $exportInfo->addChild('filters');
        foreach ($this->buildFilters() as $key => $value) {
            $filters->addChild($key, htmlspecialchars((string) $value));
        }

        // Add audit logs
        $logsElement = $xml->addChild('audit_logs');

        foreach ($auditLogs as $log) {
            $logElement = $logsElement->addChild('audit_log');
            $logElement->addChild('id', (string) $log->id);
            $logElement->addChild('user_id', (string) $log->user_id);
            $logElement->addChild('user_name', htmlspecialchars($log->user->name ?? ''));
            $logElement->addChild('user_email', htmlspecialchars($log->user->email ?? ''));
            $logElement->addChild('action', htmlspecialchars((string) $log->action));
            $logElement->addChild('resource_type', htmlspecialchars((string) $log->model_type));
            $logElement->addChild('resource_id', (string) $log->model_id);
            $logElement->addChild('ip_address', htmlspecialchars((string) $log->ip_address));
            $logElement->addChild('user_agent', htmlspecialchars((string) $log->user_agent));
            $logElement->addChild('result', 'success');
            $logElement->addChild('created_at', $log->created_at->toISOString());

            if ($includeMetadata) {
                $metadata = json_encode($log->metadata ?? []);
                if ($metadata === false) {
                    $metadata = '[]';
                }

                $logElement->addChild('metadata', htmlspecialchars($metadata));

                $context = json_encode($log->context ?? []);
                if ($context === false) {
                    $context = '[]';
                }

                $logElement->addChild('context', htmlspecialchars($context));
            }
        }

        $result = $xml->asXML();

        throw_if($result === false, new RuntimeException('Failed to generate XML'));

        return $result;
    }

    /**
     * Escape CSV values.
     */
    private function escapeCsvValue(string $value): string
    {
        if (str_contains($value, ',') || str_contains($value, '"') || str_contains($value, "\n")) {
            return '"'.str_replace('"', '""', $value).'"';
        }

        return $value;
    }

    /**
     * Save file to storage.
     */
    private function saveFile(string $outputPath, string $filename, string $content, bool $compress): string
    {
        $fullPath = $outputPath.'/'.$filename;

        if ($compress) {
            $compressed = gzencode($content);
            throw_if($compressed === false, new RuntimeException('Failed to compress content'));

            $content = $compressed;
            $fullPath .= '.gz';
        }

        Storage::disk('local')->put($fullPath, $content);

        return Storage::disk('local')->path($fullPath);
    }

    /**
     * Get file size in human readable format.
     */
    private function getFileSize(string $filePath): string
    {
        if ($filePath === false) {
            return 'Unknown';
        }

        $bytes = filesize($filePath);
        if ($bytes === false) {
            return 'Unknown';
        }

        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2).' '.$units[$i];
    }

    /**
     * Display export statistics.
     */
    private function displayExportStats(): void
    {
        $this->newLine();
        $this->info('Export Statistics:');

        $tableData = [];
        $totalRecords = 0;

        foreach ($this->exportStats as $stat) {
            $tableData[] = [
                basename((string) $stat['file']),
                $stat['date'] ?? 'All dates',
                (string) $stat['records'],
                $stat['size'],
                $stat['compressed'] ? 'Yes' : 'No',
            ];
            $totalRecords += (int) $stat['records'];
        }

        $this->table(
            ['File', 'Date', 'Records', 'Size', 'Compressed'],
            $tableData
        );

        $this->info("Total records exported: {$totalRecords}");
        $this->info('Total files created: '.count($this->exportStats));
    }
}
