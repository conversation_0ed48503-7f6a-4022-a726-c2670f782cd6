<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\Role;
use App\Models\Permission;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

final class AuthorizationSyncPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'authorization:sync-permissions {--force : Force sync without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync permissions and roles from configuration to database';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        if (! $this->option('force') && ! $this->confirm('This will sync permissions and roles from config. Continue?')) {
            $this->info('Operation cancelled.');

            return self::SUCCESS;
        }

        $this->info('Syncing permissions and roles...');

        DB::transaction(function (): void {
            $this->syncPermissions();
            $this->syncRoles();
        });

        $this->info('Permissions and roles synced successfully!');

        return self::SUCCESS;
    }

    /**
     * Sync permissions from configuration
     */
    private function syncPermissions(): void
    {
        $configPermissions = config('authorization.permissions', []);
        $created = 0;
        $updated = 0;

        foreach ($configPermissions as $permission) {
            $permissionName = data_get($permission, 'name');
            if (blank($permissionName) || empty($permissionName)) {
                $this->warn('Skipping permission with missing name');

                continue;
            }

            $model = Permission::query()->firstOrCreate(['name' => $permissionName]);
            $wasRecentlyCreated = $model->wasRecentlyCreated;

            if ($wasRecentlyCreated) {
                $created++;
            } else {
                $updated++;
            }
        }

        $this->line("Created {$created} permissions, updated {$updated} permissions");
    }

    /**
     * Sync roles from configuration
     */
    private function syncRoles(): void
    {
        $configRoles = config('authorization.roles', []);
        $created = 0;
        $updated = 0;

        foreach ($configRoles as $role) {
            $roleName = data_get($role, 'name');
            if (blank($roleName) || empty($roleName)) {
                $this->warn('Skipping role with missing name');

                continue;
            }

            $roleModel = Role::query()->firstOrCreate(['name' => $roleName]);
            $wasRecentlyCreated = $roleModel->wasRecentlyCreated;

            // Sync role permissions if specified
            $rolePermissions = data_get($role, 'permissions');
            if (filled($rolePermissions) && is_array($rolePermissions)) {
                $permissions = Permission::query()->whereIn('name', $rolePermissions)->get();
                $foundPermissionNames = $permissions->pluck('name')->toArray();
                $missingPermissions = array_diff($rolePermissions, $foundPermissionNames);

                if ($missingPermissions !== []) {
                    $this->warn("Role '{$roleName}' references non-existent permissions: ".implode(', ', $missingPermissions));
                }

                $roleModel->permissions()->sync($permissions->pluck('id'));
            }

            if ($wasRecentlyCreated) {
                $created++;
            } else {
                $updated++;
            }
        }

        $this->line("Created {$created} roles, updated {$updated} roles");
    }
}
