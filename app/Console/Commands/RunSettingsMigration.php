<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Exception;
use ReflectionClass;
use Illuminate\Console\Command;
use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigrator;
use Spatie\LaravelSettings\SettingsRepositories\DatabaseSettingsRepository;

final class RunSettingsMigration extends Command
{
    protected $signature = 'settings:run-migration {file}';

    protected $description = 'Run a specific settings migration file';

    public function handle(): int
    {
        $file = $this->argument('file');
        $migrationPath = database_path('settings/'.$file);

        if (! file_exists($migrationPath)) {
            $this->error("Migration file not found: {$migrationPath}");

            return 1;
        }

        $this->info("Running settings migration: {$file}");

        try {
            // Load the migration file
            $migration = require $migrationPath;

            // Create migrator instance with proper config
            $config = config('settings.repositories.database', [
                'connection' => null,
                'table' => 'settings',
                'group' => null,
            ]);
            $repository = new DatabaseSettingsRepository($config);
            $migrator = new SettingsMigrator($repository);

            // Set the migrator on the migration instance using reflection
            $reflection = new ReflectionClass($migration);
            $migratorProperty = $reflection->getProperty('migrator');
            $migratorProperty->setValue($migration, $migrator);

            // Run the migration
            $migration->up();

            $this->info('Settings migration completed successfully!');

            return 0;

        } catch (Exception $exception) {
            $this->error('Migration failed: '.$exception->getMessage());

            return 1;
        }
    }
}
