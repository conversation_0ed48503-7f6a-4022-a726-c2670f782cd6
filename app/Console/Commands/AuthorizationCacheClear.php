<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use App\Services\AuthorizationService;

final class AuthorizationCacheClear extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'authorization:cache-clear {--user= : Clear cache for specific user ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear authorization cache for all users or a specific user';

    /**
     * Execute the console command.
     */
    public function handle(AuthorizationService $authorizationService): int
    {
        $userId = $this->option('user');

        if ($userId) {
            $user = User::query()->find((int) $userId);
            if (! $user) {
                $this->error("User with ID {$userId} not found.");

                return self::FAILURE;
            }

            $authorizationService->clearUserCache($user);
            $this->info("Authorization cache cleared for user ID: {$userId}");
        } else {
            $authorizationService->clearAllCache();
            $this->info('Authorization cache cleared for all users');
        }

        return self::SUCCESS;
    }
}
