<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Exception;
use App\Models\User;
use Illuminate\Console\Command;

final class SyncUserTypes extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sync:user-types
                            {--dry-run : Show what would be changed without making changes}
                            {--admin-only : Only sync admin and super admin users}';

    /**
     * The console command description.
     */
    protected $description = 'Synchronize user_type column with role-based logic to ensure admins are never classified as customers';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔄 User Type Synchronization Tool');
        $this->newLine();

        $dryRun = $this->option('dry-run');
        $adminOnly = $this->option('admin-only');

        if ($dryRun) {
            $this->warn('🧪 DRY RUN MODE - No changes will be made');
            $this->newLine();
        }

        // Build query
        $query = User::query();

        if ($adminOnly) {
            $query->whereHas('roles', function ($q): void {
                $q->whereIn('name', ['admin', 'super_admin']);
            });
            $this->info('🎯 Focusing on admin and super admin users only');
        }

        $users = $query->get();
        $this->info("📊 Found {$users->count()} users to process");
        $this->newLine();

        $changes = [];
        $errors = [];

        foreach ($users as $user) {
            try {
                $currentType = $user->user_type;
                $calculatedType = $user->getUserType();

                if ($currentType !== $calculatedType) {
                    $changes[] = [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'current_type' => $currentType,
                        'calculated_type' => $calculatedType,
                        'roles' => $user->roles->pluck('name')->toArray(),
                        'has_subscriptions' => $user->subscriptions()->exists(),
                    ];

                    if (! $dryRun) {
                        $user->update(['user_type' => $calculatedType]);
                    }
                }
            } catch (Exception $e) {
                $errors[] = [
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                ];
            }
        }

        // Display results
        if ($changes === []) {
            $this->info('✅ All user types are already correct!');

            return 0;
        }

        $this->warn('📝 Found '.count($changes).' users with incorrect user_type:');
        $this->newLine();

        $headers = ['ID', 'Name', 'Email', 'Current Type', 'Should Be', 'Roles', 'Has Subscriptions'];
        $rows = [];

        foreach ($changes as $change) {
            $rows[] = [
                $change['id'],
                $change['name'],
                $change['email'],
                $change['current_type'] ?: 'null',
                $change['calculated_type'],
                implode(', ', $change['roles']),
                $change['has_subscriptions'] ? 'Yes' : 'No',
            ];
        }

        $this->table($headers, $rows);

        if (! $dryRun) {
            $this->newLine();
            $this->info('✅ Updated '.count($changes).' user(s) successfully!');
        } else {
            $this->newLine();
            $this->info('💡 Run without --dry-run to apply these changes');
        }

        // Show any errors
        if ($errors !== []) {
            $this->newLine();
            $this->error('❌ Errors occurred:');
            foreach ($errors as $error) {
                $this->line("  User {$error['user_id']}: {$error['error']}");
            }
        }

        // Show summary by type
        $typeSummary = [];
        foreach ($changes as $change) {
            $key = "{$change['current_type']} → {$change['calculated_type']}";
            $typeSummary[$key] = ($typeSummary[$key] ?? 0) + 1;
        }

        $this->newLine();
        $this->info('📈 Change Summary:');
        foreach ($typeSummary as $change => $count) {
            $this->line("  {$change}: {$count} user(s)");
        }

        return $errors === [] ? 0 : 1;
    }
}
