<?php

declare(strict_types=1);

namespace App\Console\Commands;

use stdClass;
use Exception;
use Carbon\Carbon;
use RuntimeException;
use Illuminate\Console\Command;
use App\Services\AuditLogService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

final class AuthorizationAuditCleanup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'authorization:audit-cleanup 
                            {--days=90 : Number of days to retain audit logs}
                            {--batch-size=1000 : Number of records to process per batch}
                            {--dry-run : Show what would be deleted without actually deleting}
                            {--force : Skip confirmation prompt}
                            {--archive : Archive old logs before deletion}
                            {--archive-format=json : Archive format (json, csv)}
                            {--keep-critical : Keep critical security events regardless of age}
                            {--vacuum : Run database vacuum after cleanup}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old authorization audit logs based on retention policy';

    /** @var array<int, string> */
    private array $criticalActions = [
        'login_failed',
        'permission_denied',
        'role_escalation_attempt',
        'suspicious_activity',
        'security_breach',
        'unauthorized_access',
    ];

    /** @var array<string, int> */
    private array $cleanupStats = [
        'total_processed' => 0,
        'deleted_count' => 0,
        'archived_count' => 0,
        'kept_critical' => 0,
        'freed_space' => 0,
    ];

    /**
     * Execute the console command.
     */
    public function handle(AuditLogService $auditLogService): int
    {
        $retentionDays = (int) $this->option('days');
        $batchSize = (int) $this->option('batch-size');
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');
        $archive = $this->option('archive');
        $archiveFormat = $this->option('archive-format');
        $keepCritical = $this->option('keep-critical');
        $vacuum = $this->option('vacuum');

        $this->info('Starting Authorization Audit Cleanup');
        $this->info("Retention period: {$retentionDays} days");
        $this->info("Batch size: {$batchSize}");

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No data will be actually deleted');
        }

        $this->newLine();

        try {
            // Calculate cutoff date
            $cutoffDate = Carbon::now()->subDays($retentionDays);
            $this->info("Cleaning up audit logs older than: {$cutoffDate->format('Y-m-d H:i:s')}");

            // Get cleanup statistics
            $stats = $this->getCleanupStatistics($cutoffDate, $keepCritical);
            $this->displayCleanupPreview($stats);

            // Confirm deletion unless forced or dry run
            if (! $dryRun && ! $force && ! $this->confirmCleanup($stats)) {
                $this->info('Cleanup cancelled.');

                return self::SUCCESS;
            }

            // Archive logs if requested
            if ($archive && ! $dryRun) {
                $this->info('Archiving old audit logs...');
                $this->archiveOldLogs($cutoffDate, $archiveFormat ?? 'json', $keepCritical);
            }

            // Perform cleanup
            $this->performCleanup($cutoffDate, $batchSize, $keepCritical, $dryRun);

            // Vacuum database if requested
            if ($vacuum && ! $dryRun) {
                $this->vacuumDatabase();
            }

            // Display final statistics
            $this->displayCleanupResults($dryRun);

            $this->info('Audit cleanup completed successfully.');

            return self::SUCCESS;

        } catch (Exception $exception) {
            $this->error("Cleanup failed: {$exception->getMessage()}");

            return self::FAILURE;
        }
    }

    /**
     * Get cleanup statistics before performing cleanup.
     */
    /** @return array<string, int> */
    private function getCleanupStatistics(Carbon $cutoffDate, bool $keepCritical): array
    {
        $baseQuery = DB::table('audit_logs')->where('created_at', '<', $cutoffDate);

        $totalOldLogs = $baseQuery->count();

        $criticalLogs = 0;
        if ($keepCritical) {
            $criticalLogs = (clone $baseQuery)
                ->whereIn('action', $this->criticalActions)
                ->count();
        }

        $logsToDelete = $totalOldLogs - ($keepCritical ? $criticalLogs : 0);

        // Estimate space usage
        $avgRowSize = $this->estimateAverageRowSize();
        $estimatedSpace = $logsToDelete * $avgRowSize;

        return [
            'total_old_logs' => $totalOldLogs,
            'critical_logs' => $criticalLogs,
            'logs_to_delete' => $logsToDelete,
            'estimated_space_freed' => $estimatedSpace,
        ];
    }

    /**
     * Estimate average row size for space calculation.
     */
    private function estimateAverageRowSize(): int
    {
        // Sample a few rows to estimate average size
        $sampleRows = DB::table('audit_logs')
            ->select(DB::raw('LENGTH(CONCAT_WS("", id, user_id, action, resource_type, resource_id, ip_address, user_agent, result, metadata, context, created_at, updated_at)) as row_size'))
            ->limit(100)
            ->pluck('row_size');

        if ($sampleRows->isEmpty()) {
            return 500; // Default estimate
        }

        return (int) $sampleRows->avg();
    }

    /**
     * Display cleanup preview.
     */
    /** @param array<string, int> $stats */
    private function displayCleanupPreview(array $stats): void
    {
        $this->info('Cleanup Preview:');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total old audit logs', number_format($stats['total_old_logs'])],
                ['Critical logs (will be kept)', number_format($stats['critical_logs'])],
                ['Logs to be deleted', number_format($stats['logs_to_delete'])],
                ['Estimated space freed', $this->formatBytes($stats['estimated_space_freed'])],
            ]
        );
        $this->newLine();
    }

    /**
     * Confirm cleanup with user.
     */
    /** @param array<string, int> $stats */
    private function confirmCleanup(array $stats): bool
    {
        if ($stats['logs_to_delete'] === 0) {
            $this->info('No logs to delete.');

            return false;
        }

        $message = "Are you sure you want to delete {$stats['logs_to_delete']} audit log entries?";

        return $this->confirm($message);
    }

    /**
     * Archive old logs before deletion.
     */
    private function archiveOldLogs(Carbon $cutoffDate, string $format, bool $keepCritical): void
    {
        $query = DB::table('audit_logs')
            ->where('created_at', '<', $cutoffDate);

        if ($keepCritical) {
            $query->whereNotIn('action', $this->criticalActions);
        }

        $logsToArchive = $query->get();

        if ($logsToArchive->isEmpty()) {
            $this->info('No logs to archive.');

            return;
        }

        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "audit_archive_{$timestamp}.{$format}";

        $content = $this->formatArchiveData($logsToArchive, $format);
        $this->saveArchiveFile($filename, $content);

        $this->cleanupStats['archived_count'] = $logsToArchive->count();
        $this->info("Archived {$logsToArchive->count()} logs to {$filename}");
    }

    /**
     * Format archive data.
     */
    /** @param Collection<int, stdClass> $logs */
    private function formatArchiveData(Collection $logs, string $format): string
    {
        switch ($format) {
            case 'json':
                $result = json_encode([
                    'archive_info' => [
                        'created_at' => now()->toISOString(),
                        'total_records' => $logs->count(),
                    ],
                    'audit_logs' => $logs->toArray(),
                ], JSON_PRETTY_PRINT);

                throw_if($result === false, new RuntimeException('Failed to encode JSON data'));

                return $result;

            case 'csv':
                $output = fopen('php://temp', 'w+');
                throw_if($output === false, new RuntimeException('Failed to create temporary file'));

                $headers = ['id', 'action', 'model_type', 'model_id', 'old_values', 'new_values',
                    'user_id', 'ip_address', 'user_agent', 'created_at', 'updated_at'];
                fputcsv($output, $headers);

                foreach ($logs as $log) {
                    /** @var stdClass $log */
                    fputcsv($output, [
                        (string) (data_get($log, 'id', '')),
                        (string) (data_get($log, 'action', '')),
                        (string) (data_get($log, 'model_type', '')),
                        (string) (data_get($log, 'model_id', '')),
                        (string) (data_get($log, 'old_values', '')),
                        (string) (data_get($log, 'new_values', '')),
                        (string) (data_get($log, 'user_id', '')),
                        (string) (data_get($log, 'ip_address', '')),
                        (string) (data_get($log, 'user_agent', '')),
                        (string) (data_get($log, 'created_at', '')),
                        (string) (data_get($log, 'updated_at', '')),
                    ]);
                }

                rewind($output);
                $csv = stream_get_contents($output);
                fclose($output);

                throw_if($csv === false, new RuntimeException('Failed to read CSV data'));

                return $csv;

            default:
                throw new \InvalidArgumentException("Unsupported archive format: {$format}");
        }
    }

    /**
     * Save archive file.
     */
    private function saveArchiveFile(string $filename, string $content): void
    {
        $path = 'archives/'.$filename;
        Storage::disk('local')->put($path, $content);
    }

    /**
     * Perform the actual cleanup.
     */
    private function performCleanup(
        Carbon $cutoffDate,
        int $batchSize,
        bool $keepCritical,
        bool $dryRun
    ): void {
        $this->info('Starting cleanup process...');

        $query = DB::table('audit_logs')
            ->where('created_at', '<', $cutoffDate);

        if ($keepCritical) {
            $query->whereNotIn('action', $this->criticalActions);
        }

        $totalToDelete = $query->count();

        if ($totalToDelete === 0) {
            $this->info('No logs to delete.');

            return;
        }

        $this->cleanupStats['total_processed'] = $totalToDelete;

        if ($dryRun) {
            $this->info("Would delete {$totalToDelete} audit log entries.");
            $this->cleanupStats['deleted_count'] = $totalToDelete;

            return;
        }

        $progressBar = $this->output->createProgressBar($totalToDelete);
        $progressBar->start();

        $deletedCount = 0;

        // Process in batches to avoid memory issues
        do {
            $batch = (clone $query)->limit($batchSize)->pluck('id');

            if ($batch->isEmpty()) {
                break;
            }

            $batchDeleted = DB::table('audit_logs')
                ->whereIn('id', $batch)
                ->delete();

            $deletedCount += $batchDeleted;
            $progressBar->advance($batchDeleted);

            // Small delay to prevent overwhelming the database
            usleep(10000); // 10ms

        } while ($batch->count() === $batchSize);

        $progressBar->finish();
        $this->newLine();

        $this->cleanupStats['deleted_count'] = $deletedCount;

        // Count critical logs kept
        if ($keepCritical) {
            $this->cleanupStats['kept_critical'] = DB::table('audit_logs')
                ->where('created_at', '<', $cutoffDate)
                ->whereIn('action', $this->criticalActions)
                ->count();
        }
    }

    /**
     * Vacuum database to reclaim space.
     */
    private function vacuumDatabase(): void
    {
        $this->info('Running database vacuum to reclaim space...');

        try {
            // For PostgreSQL
            if (DB::getDriverName() === 'pgsql') {
                DB::statement('VACUUM ANALYZE audit_logs');
            }
            // For MySQL, optimize table
            elseif (DB::getDriverName() === 'mysql') {
                DB::statement('OPTIMIZE TABLE audit_logs');
            }
            // For SQLite
            elseif (DB::getDriverName() === 'sqlite') {
                DB::statement('VACUUM');
            }

            $this->info('Database vacuum completed.');
        } catch (Exception $exception) {
            $this->warn("Database vacuum failed: {$exception->getMessage()}");
        }
    }

    /**
     * Display cleanup results.
     */
    private function displayCleanupResults(bool $dryRun): void
    {
        $this->newLine();
        $this->info($dryRun ? 'Cleanup Summary (Dry Run):' : 'Cleanup Results:');

        $tableData = [
            ['Total processed', number_format($this->cleanupStats['total_processed'])],
            ['Records deleted', number_format($this->cleanupStats['deleted_count'])],
        ];

        if ($this->cleanupStats['archived_count'] > 0) {
            $tableData[] = ['Records archived', number_format($this->cleanupStats['archived_count'])];
        }

        if ($this->cleanupStats['kept_critical'] > 0) {
            $tableData[] = ['Critical records kept', number_format($this->cleanupStats['kept_critical'])];
        }

        $this->table(['Metric', 'Count'], $tableData);

        if (! $dryRun && $this->cleanupStats['deleted_count'] > 0) {
            $estimatedSpace = $this->cleanupStats['deleted_count'] * $this->estimateAverageRowSize();
            $this->info("Estimated space freed: {$this->formatBytes($estimatedSpace)}");
        }
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2).' '.$units[$i];
    }
}
