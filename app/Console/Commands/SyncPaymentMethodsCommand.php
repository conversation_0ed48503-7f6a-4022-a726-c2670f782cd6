<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Exception;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Services\PaymentProcessor\PaymentProcessorFactory;
use App\Contracts\PaymentProcessor\PaymentMethodSyncServiceInterface;

final class SyncPaymentMethodsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:payment-methods
                            {--processor=paddle : Sync payment methods for a specific processor (paddle, stripe, paypal, lemonsqueezy)}
                            {--user= : Sync payment methods for a specific user ID}
                            {--dry-run : Show what would be synced without making changes}
                            {--cleanup : Also cleanup orphaned payment method records}
                            {--force : Force sync even if recently synced}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize payment methods from payment processors to local database';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting payment method synchronization...');

        try {
            $userId = $this->option('user');
            $dryRun = $this->option('dry-run');
            $cleanup = $this->option('cleanup');
            $force = $this->option('force');
            $processor = $this->option('processor');

            $paymentProcessorFactory = resolve(PaymentProcessorFactory::class);
            $paymentSyncService = $paymentProcessorFactory->getPaymentSyncService($processor);

            if ($dryRun) {
                $this->warn('DRY RUN MODE: No changes will be made to the database');
            }

            // Sync for specific user or all users
            if ($userId) {
                $this->syncSpecificUser((int) $userId, $dryRun, $processor, $paymentSyncService);
            } else {
                $this->syncAllUsers($dryRun, $paymentSyncService);
            }

            // Cleanup orphaned records if requested
            if ($cleanup && ! $dryRun) {
                $this->cleanupOrphanedRecords($paymentSyncService);
            }

            $this->displaySyncStatus($paymentSyncService);

            $this->info('Payment method synchronization completed successfully!');

            return 0;
        } catch (Exception $exception) {
            $this->error('Payment method synchronization failed: '.$exception->getMessage());
            Log::error('Payment method sync command failed', [
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
                'options' => $this->options(),
            ]);

            return 1;
        }
    }

    /**
     * Sync payment methods for a specific user.
     */
    private function syncSpecificUser(int $userId, bool $dryRun, string $processor, PaymentMethodSyncServiceInterface $paymentSyncService): void
    {
        $user = User::query()->find($userId);

        throw_unless($user, new Exception("User with ID {$userId} not found"));

        $this->info("Syncing payment methods for user: {$user->name} (ID: {$userId})");

        if ($dryRun) {
            $this->simulateUserSync($user);
        } else {
            $result = $paymentSyncService->syncUserPaymentMethods($user, $processor);
            $this->displayUserSyncResult($user, $result);
        }
    }

    /**
     * Sync payment methods for all users.
     */
    private function syncAllUsers(bool $dryRun, PaymentMethodSyncServiceInterface $paymentSyncService): void
    {
        $this->info('Syncing payment methods for all users...');

        if ($dryRun) {
            $this->simulateAllUsersSync();
        } else {
            $result = $paymentSyncService->syncAllPaymentMethods();
            $this->displayAllUsersSyncResult($result);
        }
    }

    /**
     * Cleanup orphaned payment method records.
     */
    private function cleanupOrphanedRecords(PaymentMethodSyncServiceInterface $paymentSyncService): void
    {
        $this->info('Cleaning up orphaned payment method records...');

        $result = $paymentSyncService->cleanupOrphanedPaymentMethods();

        $this->info("Cleaned up {$result['removed_count']} orphaned payment method records");

        if (! empty($result['errors'])) {
            $this->warn('Some cleanup operations failed:');
            foreach ($result['errors'] as $error) {
                $this->error("- {$error}");
            }
        }
    }

    /**
     * Display sync status information.
     */
    private function displaySyncStatus(PaymentMethodSyncServiceInterface $paymentSyncService): void
    {
        $status = $paymentSyncService->getSyncStatus();

        $this->newLine();
        $this->info('=== Sync Status ===');
        // $this->info("Total users processed: {$status['total_users']}");
        // $this->info("Total payment methods: {$status['total_payment_methods']}");
        // $this->info("Payment methods created: {$status['created_count']}");
        // $this->info("Payment methods updated: {$status['updated_count']}");
        // $this->info("Payment methods removed: {$status['removed_count']}");

        if (! empty($status['errors'])) {
            $this->warn('Errors encountered: '.count($status['errors']));
            foreach ($status['errors'] as $error) {
                $this->error("- {$error}");
            }
        }

        if (! empty($status['last_sync_at'])) {
            $this->info("Last sync completed: {$status['last_sync_at']}");
        }
    }

    /**
     * Simulate sync for a specific user (dry run).
     */
    private function simulateUserSync(User $user): void
    {
        $this->info("Would sync payment methods for: {$user->name} (ID: {$user->id})");

        // In a real implementation, we could fetch payment methods from processors
        // and compare with local records to show what would change
        $this->comment('- Would fetch payment methods from all configured processors');
        $this->comment('- Would compare with existing local payment methods');
        $this->comment('- Would create/update/remove payment methods as needed');
    }

    /**
     * Simulate sync for all users (dry run).
     */
    private function simulateAllUsersSync(): void
    {
        $userCount = User::query()->count();
        $this->info("Would sync payment methods for {$userCount} users");

        $this->comment('- Would iterate through all users');
        $this->comment('- Would fetch payment methods from all configured processors');
        $this->comment('- Would compare with existing local payment methods');
        $this->comment('- Would create/update/remove payment methods as needed');
    }

    /**
     * Display sync result for a specific user.
     */
    private function displayUserSyncResult(User $user, array $result): void
    {
        $this->info("Sync completed for {$user->name}:");
        // $this->info("- Created: {$result['created_count']} payment methods");
        // $this->info("- Updated: {$result['updated_count']} payment methods");
        // $this->info("- Removed: {$result['removed_count']} payment methods");

        if (! empty($result['errors'])) {
            $this->warn('Errors encountered:');
            foreach ($result['errors'] as $error) {
                $this->error("- {$error}");
            }
        }
    }

    /**
     * Display sync result for all users.
     */
    private function displayAllUsersSyncResult(array $result): void
    {
        $this->info('Sync completed for all users:');
        // $this->info("- Processed: {$result['total_users']} users");
        // $this->info("- Created: {$result['created_count']} payment methods");
        // $this->info("- Updated: {$result['updated_count']} payment methods");
        // $this->info("- Removed: {$result['removed_count']} payment methods");

        if (! empty($result['errors'])) {
            $this->warn('Errors encountered:');
            foreach ($result['errors'] as $error) {
                $this->error("- {$error}");
            }
        }
    }
}
