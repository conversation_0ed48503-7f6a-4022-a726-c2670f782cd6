<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Exception;
use RuntimeException;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use App\Services\PerformanceMetricsService;

final class AuthorizationPerformanceReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'authorization:performance-report 
                            {--period=today : Report period (today, week, month)}
                            {--format=table : Output format (table, json, csv)}
                            {--export= : Export to file path}
                            {--detailed : Include detailed metrics}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate authorization performance report with metrics and statistics';

    /**
     * Execute the console command.
     */
    public function handle(PerformanceMetricsService $metricsService): int
    {
        $period = $this->option('period');
        $format = $this->option('format');
        $exportPath = $this->option('export');
        $detailed = $this->option('detailed');

        // Validate period
        if (! in_array($period, ['today', 'week', 'month'])) {
            $this->error('Invalid period. Use: today, week, or month');

            return self::FAILURE;
        }

        // Validate format
        if (! in_array($format, ['table', 'json', 'csv'])) {
            $this->error('Invalid format. Use: table, json, or csv');

            return self::FAILURE;
        }

        $this->info("Generating authorization performance report for period: {$period}");

        try {
            // Get dashboard metrics
            $period = (string) $this->option('period');
            $metrics = $metricsService->getDashboardMetrics($period);

            // Generate report data
            $reportData = $this->generateReportData($metrics, $detailed);

            // Display or export report
            if ($exportPath) {
                $this->exportReport($reportData, $exportPath, (string) $format);
                $this->info("Report exported to: {$exportPath}");
            } else {
                $this->displayReport($reportData, (string) $format);
            }

            return self::SUCCESS;
        } catch (Exception $exception) {
            $this->error("Failed to generate performance report: {$exception->getMessage()}");

            return self::FAILURE;
        }
    }

    /**
     * Generate structured report data.
     *
     * @param  array<string, array<string, mixed>|string>  $metrics
     * @return array<string, mixed>
     */
    private function generateReportData(array $metrics, bool $detailed): array
    {
        $requestMetrics = $metrics['request_metrics'] ?? [];
        $authMetrics = $metrics['authorization_metrics'] ?? [];
        $errorMetrics = $metrics['error_metrics'] ?? [];
        $securityMetrics = $metrics['security_metrics'] ?? [];

        assert(is_array($requestMetrics));
        assert(is_array($authMetrics));
        assert(is_array($errorMetrics));
        assert(is_array($securityMetrics));

        $report = [
            'generated_at' => now()->toISOString(),
            'period' => $this->option('period'),
            'summary' => [
                'total_requests' => $requestMetrics['total_requests'] ?? 0,
                'avg_response_time' => round(is_numeric($requestMetrics['avg_response_time'] ?? 0) ? (float) ($requestMetrics['avg_response_time'] ?? 0) : 0.0, 2),
                'success_rate' => round(is_numeric($requestMetrics['success_rate'] ?? 0) ? (float) ($requestMetrics['success_rate'] ?? 0) : 0.0, 2),
                'total_auth_actions' => $authMetrics['total_auth_actions'] ?? 0,
                'auth_success_rate' => round(is_numeric($authMetrics['success_rate'] ?? 0) ? (float) ($authMetrics['success_rate'] ?? 0) : 0.0, 2),
                'total_errors' => $errorMetrics['total_errors'] ?? 0,
                'security_events' => $securityMetrics['total_events'] ?? 0,
            ],
        ];

        if ($detailed) {
            $report['detailed'] = [
                'request_metrics' => $metrics['request_metrics'] ?? [],
                'authorization_metrics' => $metrics['authorization_metrics'] ?? [],
                'error_metrics' => $metrics['error_metrics'] ?? [],
                'security_metrics' => $metrics['security_metrics'] ?? [],
                'performance_trends' => $metrics['performance_trends'] ?? [],
                'slow_routes' => $metrics['top_slow_routes'] ?? [],
                'user_activity' => $metrics['user_activity'] ?? [],
            ];
        }

        return $report;
    }

    /**
     * Display report in specified format.
     *
     * @param  array<string, mixed>  $reportData
     */
    private function displayReport(array $reportData, string $format): void
    {
        $result = match ($format) {
            'json' => json_encode($reportData, JSON_PRETTY_PRINT),
            'csv' => $this->displayCsvReport($reportData),
            default => $this->displayTableReport($reportData),
        };

        throw_if($result === false, new RuntimeException('Failed to generate report output'));

        $this->line((string) $result);
    }

    /**
     * Display report as table.
     *
     * @param  array<string, mixed>  $reportData
     */
    private function displayTableReport(array $reportData): void
    {
        $this->info('Authorization Performance Report');
        $this->info('Generated: '.(data_get($reportData, 'generated_at', '')));
        $this->info('Period: '.(data_get($reportData, 'period', '')));
        $this->newLine();

        // Summary table
        $summary = is_array(data_get($reportData, 'summary')) ? data_get($reportData, 'summary') : [];
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Requests', number_format((float) data_get($summary, 'total_requests', 0))],
                ['Avg Response Time (ms)', (float) data_get($summary, 'avg_response_time', 0)],
                ['Success Rate (%)', (float) data_get($summary, 'success_rate', 0)],
                ['Total Auth Actions', number_format((float) data_get($summary, 'total_auth_actions', 0))],
                ['Auth Success Rate (%)', (float) data_get($summary, 'auth_success_rate', 0)],
                ['Total Errors', number_format((float) data_get($summary, 'total_errors', 0))],
                ['Security Events', number_format((float) data_get($summary, 'security_events', 0))],
            ]
        );

        if (filled($reportData['detailed'])) {
            $this->newLine();
            $this->info('Detailed metrics available in JSON/CSV export format.');
        }
    }

    /**
     * Display report as CSV.
     *
     * @param  array<string, mixed>  $reportData
     */
    private function displayCsvReport(array $reportData): string
    {
        $csv = "Metric,Value\n";
        $summary = $reportData['summary'] ?? [];
        if (is_array($summary)) {
            foreach ($summary as $key => $value) {
                $csv .= ucfirst(str_replace('_', ' ', (string) $key)).','.$value."\n";
            }
        }

        return $csv;
    }

    /**
     * Export report to file.
     *
     * @param  array<string, mixed>  $reportData
     */
    private function exportReport(array $reportData, string $path, string $format): void
    {
        // Validate and sanitize the file path
        $path = str_replace(['../', '..\\'], '', $path);
        throw_if(str_contains($path, '..'), new \InvalidArgumentException('Invalid file path provided'));

        $content = match ($format) {
            'json' => json_encode($reportData, JSON_PRETTY_PRINT),
            'csv' => $this->generateCsvContent($reportData),
            default => $this->generateTextContent($reportData),
        };

        throw_if($content === false, new RuntimeException('Failed to generate report content'));

        Storage::disk('local')->put((string) $path, $content);
    }

    /**
     * Generate CSV content.
     *
     * @param  array<string, mixed>  $reportData
     */
    private function generateCsvContent(array $reportData): string
    {
        $csv = "Metric,Value\n";
        $summary = $reportData['summary'] ?? [];
        if (is_array($summary)) {
            foreach ($summary as $key => $value) {
                $csv .= ucfirst(str_replace('_', ' ', (string) $key)).','.$value."\n";
            }
        }

        return $csv;
    }

    /**
     * Generate text content.
     *
     * @param  array<string, mixed>  $reportData
     */
    private function generateTextContent(array $reportData): string
    {
        $text = "Authorization Performance Report\n";
        $text .= 'Generated: '.$reportData['generated_at'] ?? ''."\n";
        $text .= 'Period: '.$reportData['period'] ?? ''."\n\n";

        $text .= "Summary:\n";
        $summary = $reportData['summary'] ?? [];
        if (is_array($summary)) {
            foreach ($summary as $key => $value) {
                $text .= ucfirst(str_replace('_', ' ', (string) $key)).': '.$value."\n";
            }
        }

        return $text;
    }
}
