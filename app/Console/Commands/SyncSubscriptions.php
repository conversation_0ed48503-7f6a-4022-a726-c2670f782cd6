<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Exception;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Services\PaymentProcessor\PaymentProcessorFactory;
use App\Contracts\PaymentProcessor\SubscriptionSyncServiceInterface;

final class SyncSubscriptions extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sync:subscriptions
                            {--processor= : Specific processor to sync (paddle, stripe)}
                            {--user= : Specific user ID to sync}
                            {--dry-run : Run without making changes}
                            {--status : Show sync status for all processors}
                            {--batch-size=100 : Number of records to process in each batch}
                            {--force : Force sync even if recently synced}';

    /**
     * The console command description.
     */
    protected $description = 'Synchronize active subscriptions from payment processors to local database for existing customers';

    public function __construct(
        private readonly PaymentProcessorFactory $paymentProcessorFactory
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔄 Payment Processor Subscription Sync');
        $this->newLine();

        if ($this->option('status')) {
            return $this->showSyncStatus();
        }

        $dryRun = $this->option('dry-run');
        if ($dryRun) {
            $this->warn('🔍 Running in dry-run mode - no changes will be made');
            $this->newLine();
        }

        $processorsToSync = $this->getProcessorsToSync();
        if ($processorsToSync === []) {
            return 1;
        }

        $user = $this->getUserToSync();
        $overallResults = $this->initializeResults();

        $this->syncProcessors($processorsToSync, $user, $dryRun, $overallResults);
        $this->displaySummary($overallResults, $dryRun);

        return $overallResults['failed_processors'] > 0 ? 1 : 0;
    }

    /**
     * Get the list of processors to sync.
     */
    private function getProcessorsToSync(): array
    {
        $enabledProcessors = $this->paymentProcessorFactory->getEnabledProcessors();

        if ($enabledProcessors === []) {
            $this->error('❌ No payment processors are enabled');

            return [];
        }

        $processor = $this->option('processor');
        if ($processor) {
            if (! in_array($processor, $enabledProcessors)) {
                $this->error("❌ Processor '{$processor}' is not enabled or does not exist");
                $this->line('Available processors: '.implode(', ', $enabledProcessors));

                return [];
            }

            return [$processor];
        }

        return $enabledProcessors;
    }

    /**
     * Get the user to sync if specified.
     */
    private function getUserToSync(): ?User
    {
        $userId = $this->option('user');
        if (! $userId) {
            return null;
        }

        $user = User::query()->find($userId);
        if (! $user) {
            $this->error("❌ User with ID {$userId} not found");

            return null;
        }

        $this->info("🎯 Syncing for user: {$user->name} (ID: {$user->id})");
        $this->newLine();

        return $user;
    }

    /**
     * Initialize the results array.
     */
    private function initializeResults(): array
    {
        return [
            'total_processors' => 0,
            'successful_processors' => 0,
            'failed_processors' => 0,
            'total_synced' => 0,
            'total_errors' => 0,
        ];
    }

    /**
     * Sync multiple processors.
     */
    private function syncProcessors(array $processorsToSync, ?User $user, bool $dryRun, array &$overallResults): void
    {
        $overallResults['total_processors'] = count($processorsToSync);

        foreach ($processorsToSync as $processorKey) {
            $this->info("🔄 Syncing {$processorKey} subscriptions...");

            try {
                $result = $this->syncProcessor($processorKey, $user, $dryRun);
                $this->processSyncResult($result, $processorKey, $overallResults);
            } catch (Exception $e) {
                $this->handleSyncException($e, $processorKey, $overallResults);
            }

            $this->newLine();
        }
    }

    /**
     * Process the result of a sync operation.
     */
    private function processSyncResult(array $result, string $processorKey, array &$overallResults): void
    {
        $synced = (int) data_get($result, 'synced', 0);
        $errors = (int) data_get($result, 'errors', 0);

        if ($result['success']) {
            $overallResults['successful_processors']++;
            $overallResults['total_synced'] += $synced;
            $overallResults['total_errors'] += $errors;

            $this->line(sprintf('  ✅ Synced: %d, Errors: %d', $synced, $errors));
        } else {
            $overallResults['failed_processors']++;
            $this->error('  ❌ Failed: '.data_get($result, 'message', 'Unknown error'));
        }
    }

    /**
     * Handle exceptions during sync operations.
     */
    private function handleSyncException(Exception $e, string $processorKey, array &$overallResults): void
    {
        $overallResults['failed_processors']++;
        $this->error("  ❌ Exception: {$e->getMessage()}");

        Log::error('Subscription sync failed for processor', [
            'processor' => $processorKey,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
        ]);
    }

    /**
     * Sync subscriptions for a specific processor.
     */
    private function syncProcessor(string $processorKey, ?User $user, bool $dryRun): array
    {
        $startTime = microtime(true);

        try {
            $processor = $this->paymentProcessorFactory->create($processorKey);
            $syncService = $this->getValidatedSyncService($processor, $processorKey);

            if (! $syncService) {
                return $this->createFailureResult("Sync service validation failed for {$processorKey}");
            }

            $result = $this->performSync($syncService, $user, $dryRun, $processorKey);
            $this->syncCanceledSubscriptions($syncService, $result, $processorKey, $dryRun);

            $duration = round(microtime(true) - $startTime, 2);
            $this->line("  ⏱️  Completed in {$duration}s");

            return $result;

        } catch (Exception $exception) {
            Log::error('Failed to sync processor subscriptions', [
                'processor' => $processorKey,
                'user_id' => $user?->id,
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);

            return $this->createFailureResult($exception->getMessage());
        }
    }

    /**
     * Get and validate the sync service for a processor.
     */
    private function getValidatedSyncService($processor, string $processorKey): ?SubscriptionSyncServiceInterface
    {
        $syncService = $processor->getSubscriptionSyncService();

        if (! $syncService instanceof SubscriptionSyncServiceInterface) {
            $this->line("  ❌ No subscription sync service available for {$processorKey}");

            return null;
        }

        if (! $processor->isConfigured()) {
            $this->line("  ❌ {$processorKey} processor is not properly configured");

            return null;
        }

        return $syncService;
    }

    /**
     * Perform the main sync operation.
     */
    private function performSync(SubscriptionSyncServiceInterface $syncService, ?User $user, bool $dryRun, string $processorKey): array
    {
        $this->line($user
            ? "  👤 Syncing user: {$user->name} (ID: {$user->id})"
            : '  👥 Syncing all users'
        );

        $batchSize = (int) $this->option('batch-size');
        $force = $this->option('force');

        // if ($this->option('verbose')) {
        //     $this->line("  📊 Batch size: {$batchSize}");
        //     $this->line('  💪 Force sync: '.($force ? 'Yes' : 'No'));
        // }

        return $user instanceof User
            ? $syncService->syncUserSubscriptions($user, $dryRun, ['batch_size' => $batchSize, 'force' => $force])
            : $syncService->syncSubscriptions($dryRun, ['batch_size' => $batchSize, 'force' => $force]);
    }

    /**
     * Sync canceled subscriptions if supported.
     */
    private function syncCanceledSubscriptions(SubscriptionSyncServiceInterface $syncService, array &$result, string $processorKey, bool $dryRun): void
    {
        if (! method_exists($syncService, 'syncCanceledSubscriptions')) {
            return;
        }

        $this->line('  🔄 Syncing canceled subscriptions...');

        try {
            $canceledResult = $syncService->syncCanceledSubscriptions($dryRun);

            if (empty($canceledResult['errors'])) {
                $result['canceled_synced'] = data_get($canceledResult, 'detected', 0);
                $result['deleted'] = data_get($canceledResult, 'synchronized', 0);

                $this->line("  ✅ Detected: {$result['canceled_synced']}, Synchronized: {$result['deleted']}");
            } else {
                $this->warn('  ⚠️ Canceled subscription sync had issues');
                foreach ($canceledResult['errors'] as $error) {
                    $this->error("    Error: {$error}");
                }
                $result['errors'] += count($canceledResult['errors']);
            }
        } catch (Exception $e) {
            $this->warn("  ⚠️ Canceled sync failed: {$e->getMessage()}");
            Log::warning('Canceled subscription sync failed', [
                'processor' => $processorKey,
                'error' => $e->getMessage(),
            ]);
            $result['errors']++;
        }
    }

    /**
     * Create a standardized failure result.
     */
    private function createFailureResult(string $message): array
    {
        return [
            'success' => false,
            'message' => $message,
            'synced' => 0,
            'errors' => 1,
        ];
    }

    /**
     * Show sync status for all processors.
     */
    private function showSyncStatus(): int
    {
        $this->info('📊 Subscription Sync Status');
        $this->newLine();

        $enabledProcessors = $this->paymentProcessorFactory->getEnabledProcessors();

        if ($enabledProcessors === []) {
            $this->error('❌ No payment processors are enabled');

            return 1;
        }

        foreach ($enabledProcessors as $processorKey) {
            try {
                $processor = $this->paymentProcessorFactory->create($processorKey);
                $syncService = $processor->getSubscriptionSyncService();

                $this->line("🔧 <fg=cyan>{$processorKey}</> Processor:");

                if (! $processor->isConfigured()) {
                    $this->line('  ❌ Not configured');
                    $this->newLine();

                    continue;
                }

                if (! $syncService instanceof SubscriptionSyncServiceInterface) {
                    $this->line('  ❌ No sync service available');
                    $this->newLine();

                    continue;
                }

                $status = $syncService->getSyncStatus();
                $this->line("  📈 Total Subscriptions: {$status['total_subscriptions']}");
                $this->line("  ✅ Synced Subscriptions: {$status['synced_subscriptions']}");
                $this->line('  📅 Last Sync: '.($status['last_sync'] ?? 'Never'));
                $this->line("  🔄 Status: {$status['status']}");

            } catch (Exception $e) {
                $this->line("🔧 <fg=cyan>{$processorKey}</> Processor:");
                $this->line("  ❌ Error: {$e->getMessage()}");
            }

            $this->newLine();
        }

        return 0;
    }

    /**
     * Display the sync summary.
     */
    private function displaySummary(array $results, bool $dryRun): void
    {
        $this->info('📋 Sync Summary');
        $this->line("Processors: {$results['total_processors']}");
        $this->line("Successful: <fg=green>{$results['successful_processors']}</>");
        $this->line("Failed: <fg=red>{$results['failed_processors']}</>");
        $this->line("Total Synced: <fg=green>{$results['total_synced']}</>");
        $this->line("Total Errors: <fg=red>{$results['total_errors']}</>");

        if ($dryRun) {
            $this->newLine();
            $this->warn('🔍 This was a dry run - no actual changes were made');
        }

        if ($results['failed_processors'] === 0 && $results['total_errors'] === 0) {
            $this->newLine();
            $this->info('✅ All subscription synchronization completed successfully!');
        } elseif ($results['failed_processors'] > 0) {
            $this->newLine();
            $this->error('❌ Some processors failed to sync. Check the logs for details.');
        } else {
            $this->newLine();
            $this->warn('⚠️  Sync completed with some errors. Check the logs for details.');
        }
    }
}
