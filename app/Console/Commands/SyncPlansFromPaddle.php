<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Exception;
use App\Models\Plan;
use Laravel\Paddle\Cashier;
use App\Services\PlanService;
use App\ValueObjects\PlanData;
use Illuminate\Console\Command;
use App\ValueObjects\ProductData;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

final class SyncPlansFromPaddle extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:plans-paddle {--dry-run : Show what would be synced without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync plans from Paddle to the local database';

    /**
     * Execute the console command.
     */
    public function handle(PlanService $planService): int
    {
        if (! config('cashier.paddle.billing_enabled')) {
            $this->error('Paddle billing is not enabled. Please check your configuration.');

            return self::FAILURE;
        }

        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('Running in dry-run mode. No changes will be made.');
        }

        Cache::flush();

        try {
            $this->info('Fetching products from Paddle...');

            // Fetch all products from Paddle API
            $response = Cashier::api('GET', 'products?include=prices');

            if (! $response->successful()) {
                $this->error('Failed to fetch products from Paddle API: '.$response->body());

                return self::FAILURE;
            }

            $responseData = $response->json();
            $productsData = is_array($responseData) && filled($responseData['data']) ? $responseData['data'] : collect();

            $productsData = collect($productsData)
                ->map(fn (array $product): ProductData => ProductData::fromArray($product));

            if ($productsData->isEmpty()) {
                $this->warn('No products found in Paddle.');

                return self::SUCCESS;
            }

            $this->info('Found '.(is_countable($productsData) ? count($productsData) : 0).' products in Paddle.');

            $synced = 0;
            $errors = 0;

            if ($productsData->isNotEmpty()) {
                foreach ($productsData as $product) {
                    if (! $product instanceof ProductData) {
                        continue;
                    }

                    try {
                        $this->syncProduct($product, $planService, $isDryRun);
                        $synced++;
                    } catch (Exception $e) {
                        $productId = $product->id;
                        $this->error("Failed to sync product {$productId}: {$e->getMessage()}");
                        Log::error('Plan sync error', [
                            'product_id' => $productId,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                        ]);
                        $errors++;
                    }
                }
            }

            if ($isDryRun) {
                $this->info("Dry run completed. Would have synced {$synced} plans.");
            } else {
                $this->info("Successfully synced {$synced} plans.");
            }

            if ($errors > 0) {
                $this->warn("Encountered {$errors} errors during sync.");
            }

            return self::SUCCESS;
        } catch (Exception $exception) {
            $this->error('Failed to sync plans from Paddle: '.$exception->getMessage());
            Log::error('Plan sync failed', [
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);

            return self::FAILURE;
        }
    }

    /**
     * Sync a single product from Paddle.
     */
    private function syncProduct(ProductData $product, PlanService $planService, bool $isDryRun): void
    {
        // Get prices for this product
        $productId = $product->id;
        $prices = $product->getPrices();

        if ($prices === []) {
            $this->warn("Product {$productId} has no prices. Skipping.");

            return;
        }

        $price = $prices !== [] ? $prices[0] : [];

        $planData = PlanData::fromArray([
            'key' => $product->key,
            'processor_plan_id' => $productId,
            'processor_price_id' => $price['id'] ?? '',
            'processor_type' => 'paddle',
            'name' => $product->name,
            'description' => $product->description,
            'price' => is_array($price) && filled($price['unit_price']) && is_array($price['unit_price']) && filled($price['unit_price']['amount']) && is_scalar($price['unit_price']['amount']) ? $price['unit_price']['amount'] : 0,
            'currency' => is_array($price) && filled($price['unit_price']) && is_array($price['unit_price']) && filled($price['unit_price']['currency_code']) && is_scalar($price['unit_price']['currency_code']) ? (string) $price['unit_price']['currency_code'] : 'USD',
            'billing_interval' => $this->mapBillingInterval(data_get($price, 'billing_cycle')),
            'active' => $product->status === 'active',
        ]);

        $productName = $product->name;
        $this->line("Product: {$productName} (ID: {$productId})");
        $price = (string) $planData->price;
        $currency = $planData->currency;
        $billingCycle = $planData->billingInterval;

        $this->line("  Price: {$price} {$currency}");
        $this->line("  Billing: {$billingCycle}");
        $this->line('  Status: '.($planData->active ? 'Active' : 'Inactive'));

        if (! $isDryRun) {
            // Check if plan already exists by Paddle product ID
            $existingPlan = Plan::query()
                ->where('processor_type', 'paddle')
                ->where('processor_plan_id', $productId)->first();

            if ($existingPlan) {
                $planKey = $existingPlan->key;
                $this->line("  → Updating existing plan: {$existingPlan->name} (Key: {$existingPlan->getKey()})");
                $this->line("  → Updating existing plan: {$planKey}");
                // Get the actual Plan model by key

                if ($existingPlan->active) {
                    // $planData['key'] = $planModel->key;
                    $planService->updatePlan($existingPlan, $planData);
                } else {
                    $this->warn("Could not find Plan model for key: {$planKey}");
                }
            } else {
                if ($planData->key === '' || $planData->key === '0') {
                    $key = $this->generatePlanKey($product);
                    $planData->setKey($key);
                }

                $this->line("→ Creating new plan with key: {$planData->key}");
                $planService->createPlan($planData);
            }
        } else {
            $this->line('  → Would sync this product');
        }

        $this->newLine();
    }

    /**
     * Map Paddle billing interval to our format.
     *
     * @param  array<mixed, mixed>|null  $billingCycle
     */
    private function mapBillingInterval(?array $billingCycle): string
    {
        if ($billingCycle === null || $billingCycle === []) {
            return 'monthly';
        }

        $interval = $billingCycle['interval'] ?? 'month';
        $frequency = $billingCycle['frequency'] ?? 1;

        return match ($interval) {
            'month' => $frequency === 1 ? 'monthly' : 'yearly',
            'year' => 'yearly',
            default => 'monthly',
        };
    }

    /**
     * Generate a unique plan key from the product name.
     */
    private function generatePlanKey(ProductData $product): string
    {
        if ($product->key !== null && $product->key !== '' && $product->key !== '0') {
            return $product->key;
        }

        // Convert to lowercase, replace spaces and special chars with hyphens
        $key = mb_strtolower((string) preg_replace('/[^a-zA-Z0-9]+/', '-', $product->name));

        // Remove leading/trailing hyphens and limit length
        $key = trim($key, '-');
        $key = mb_substr($key, 0, 50);

        // Ensure uniqueness by appending timestamp if needed
        $baseKey = $key;
        $counter = 1;

        while (Plan::query()->where('key', $key)->exists()) {
            $key = $baseKey.'-'.$counter;
            $counter++;
        }

        return $key;
    }
}
