<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Exception;
use App\Services\PlanService;
use Illuminate\Console\Command;

final class ClearPlanCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'plan:cache-clear';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all plan-related cache data';

    /**
     * Execute the console command.
     */
    public function handle(PlanService $planService): int
    {
        try {
            $planService->clearPlanCache();
            $this->info('Plan cache cleared successfully.');

            return self::SUCCESS;
        } catch (Exception $exception) {
            $this->error('Failed to clear plan cache: '.$exception->getMessage());

            return self::FAILURE;
        }
    }
}
