<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Throwable;
use Illuminate\Console\Command;
use Amp\Websocket\Client\WebsocketHandshake;

use function Amp\Websocket\Client\connect;

/**
 * <PERSON><PERSON> command to listen to market statistics from a WebSocket server.
 *
 * Usage examples:
 * php artisan market:listen wss://example.com/market-feed
 * php artisan market:listen ws://localhost:8080/stats --timeout=30
 * php artisan market:listen --timeout=10  (uses configured URL)
 *
 * The URL argument is optional. If not provided, the command will use
 * the URL configured in config/services.php under 'market_stats.url'.
 *
 * This command uses the amphp/websocket-client package to establish
 * a WebSocket connection and listen for incoming messages.
 */
final class ListenToMarketStats extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'market:listen {url?} {--timeout=0 : Connection timeout in seconds (0 for no timeout)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Listen to market statistics from a WebSocket server';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $url = $this->argument('url') ?? config('services.market_stats.url');
        $timeout = (int) $this->option('timeout');

        // Check if URL is provided or configured
        if (empty($url)) {
            $this->error('No WebSocket URL provided. Please specify a URL as an argument or configure it in services.market_stats.url');
            $this->line('Example: php artisan market:listen wss://echo.websocket.org');

            return Command::FAILURE;
        }

        $this->info("Connecting to WebSocket server: {$url}");

        try {
            // Create WebSocket handshake
            $handshake = new WebsocketHandshake($url);

            // Connect to the WebSocket server
            $connection = connect($handshake);

            $this->info('Connected successfully! Listening for market statistics...');
            $this->info('Press Ctrl+C to stop listening.');

            $messageCount = 0;
            $startTime = time();

            // Listen for messages
            foreach ($connection as $message) {
                $messageCount++;
                $payload = $message->getPayload();
                $timestamp = now()->format('Y-m-d H:i:s');

                $this->line("[{$timestamp}] Message #{$messageCount}: {$payload}");

                // Check timeout if specified
                if ($timeout > 0 && (time() - $startTime) >= $timeout) {
                    $this->info("Timeout reached ({$timeout}s). Closing connection...");
                    $connection->close();
                    break;
                }
            }

        } catch (Throwable $throwable) {
            $this->error('WebSocket connection failed: '.$throwable->getMessage());

            return Command::FAILURE;
        }

        $this->info('Connection closed. Total messages received: '.$messageCount);

        return Command::SUCCESS;
    }
}
