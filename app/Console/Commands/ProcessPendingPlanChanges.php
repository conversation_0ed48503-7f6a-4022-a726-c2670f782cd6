<?php

declare(strict_types=1);

namespace App\Console\Commands;

use DB;
use Exception;
use Throwable;
use App\Models\Plan;
use App\Models\User;
use Illuminate\Console\Command;
use App\Enums\SubscriptionStatus;
use Illuminate\Support\Facades\Log;
use App\Models\SubscriptionPlanChange;
use Illuminate\Database\Eloquent\Collection;
use App\Services\Subscription\SubscriptionService;
use App\Services\PaymentProcessor\PaymentProcessorFactory;

final class ProcessPendingPlanChanges extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'plan-changes:process
                            {--dry-run : Preview changes without executing them}
                            {--limit=100 : Maximum number of pending changes to process}
                            {--user= : Process changes for a specific user ID only}
                            {--sleep-ms=100 : Delay in milliseconds between processing each item}';

    /**
     * The console command description.
     */
    protected $description = 'Process pending subscription plan changes';

    /**
     * Statistics for tracking processing results.
     *
     * @var array<string, int>
     */
    private array $stats = [
        'total_processed' => 0,
        'successful' => 0,
        'failed' => 0,
        'skipped' => 0,
        'validation_failures' => 0,
        'payment_failures' => 0,
        'plan_change_failures' => 0,
    ];

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔄 Processing Pending Plan Changes');
        $this->newLine();

        $isDryRun = $this->option('dry-run');
        $limit = (int) $this->option('limit');
        $userId = $this->option('user');

        $sleepMs = (int) $this->option('sleep-ms');

        if ($isDryRun) {
            $this->warn('🔍 DRY RUN MODE - No changes will be made');
            $this->newLine();
        }

        try {
            $pendingChanges = $this->getPendingChanges($limit, $userId);

            if ($pendingChanges->isEmpty()) {
                $this->info('✅ No pending plan changes found.');

                return 0;
            }

            $this->info("Found {$pendingChanges->count()} pending plan changes to process.");
            $this->newLine();

            $progressBar = $this->output->createProgressBar($pendingChanges->count());
            $progressBar->setFormat('verbose');

            foreach ($pendingChanges as $planChange) {
                $this->processPlanChange($planChange, $isDryRun);
                $progressBar->advance();

                // Add a small delay to prevent overwhelming the system
                if ($sleepMs > 0) {
                    usleep($sleepMs * 1000); // Convert to microseconds
                }
            }

            $progressBar->finish();
            $this->newLine(2);

            $this->displaySummary($isDryRun);

            return $this->stats['failed'] > 0 ? 1 : 0;

        } catch (Exception $exception) {
            $this->error("❌ Fatal error occurred: {$exception->getMessage()}");
            Log::error('ProcessPendingPlanChanges command failed', [
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);

            return 1;
        }
    }

    /**
     * Get pending plan changes based on filters.
     *
     * @return Collection<int, SubscriptionPlanChange>
     */
    private function getPendingChanges(int $limit, ?string $userId): Collection
    {
        $query = SubscriptionPlanChange::query()->pending()
            ->with(['user', 'toPlan', 'fromPlan'])
            ->orderBy('created_at', 'asc')
            ->limit($limit);

        if ($userId !== null && $userId !== '' && $userId !== '0') {
            $query->byUser($userId);
        }

        return $query->get();
    }

    /**
     * Process a single plan change request.
     */
    private function processPlanChange(SubscriptionPlanChange $planChange, bool $isDryRun): void
    {
        $this->stats['total_processed']++;

        try {
            // Guard against concurrent processing
            $result = DB::transaction(function () use ($planChange, $isDryRun): array {
                // Re-check status to ensure this change hasn't been processed by another process
                $freshPlanChange = SubscriptionPlanChange::query()
                    ->where('id', $planChange->id)
                    ->where('status', SubscriptionStatus::PENDING)
                    ->lockForUpdate()
                    ->first();

                if (! $freshPlanChange) {
                    return ['status' => 'skipped', 'reason' => 'Already processed or not pending'];
                }

                // Validate the plan change request
                $validation = $this->validatePlanChange($freshPlanChange);

                if (! $validation['valid']) {
                    $this->handleValidationFailure($freshPlanChange, $validation['reason'], $isDryRun);

                    return ['status' => 'failed', 'reason' => $validation['reason']];
                }

                // For free plans, skip payment method validation
                if ($freshPlanChange->toPlan && $freshPlanChange->toPlan->price <= 0) {
                    // Process the plan change
                    if (! $isDryRun) {
                        $result = $this->executePlanChange($freshPlanChange);
                        $freshPlanChange->user->clearCache();

                        if ($result['success']) {
                            $this->handleSuccess($freshPlanChange, $result);

                            return ['status' => 'success', 'result' => $result];
                        }

                        $this->handlePlanChangeFailure($freshPlanChange, $result['error']);

                        return ['status' => 'failed', 'reason' => $result['error']];

                    }

                    $this->info("  [DRY RUN] Would process plan change for user {$freshPlanChange->user->id} from plan {$freshPlanChange->fromPlan?->key} to {$freshPlanChange->toPlan?->key}");
                    $this->stats['successful']++;

                    return ['status' => 'success', 'result' => null];

                }

                // Validate payment methods
                $paymentValidation = $this->validatePaymentMethods($freshPlanChange);

                if (! $paymentValidation['valid']) {
                    $this->handlePaymentFailure($freshPlanChange, $paymentValidation['reason'], $isDryRun);

                    return ['status' => 'failed', 'reason' => $paymentValidation['reason']];
                }

                // Process the plan change
                if (! $isDryRun) {
                    $result = $this->executePlanChange($freshPlanChange);
                    $freshPlanChange->user->clearCache();

                    if ($result['success']) {
                        $this->handleSuccess($freshPlanChange, $result);

                        return ['status' => 'success', 'result' => $result];
                    }

                    $this->handlePlanChangeFailure($freshPlanChange, $result['error']);

                    return ['status' => 'failed', 'reason' => $result['error']];

                }

                $this->info("  [DRY RUN] Would process plan change for user {$freshPlanChange->user->id} from plan {$freshPlanChange->fromPlan?->key} to {$freshPlanChange->toPlan?->key}");
                $this->stats['successful']++;

                return ['status' => 'success', 'result' => null];

            });

            if ($result['status'] === 'skipped') {
                $this->stats['skipped']++;
                $this->warn("  ⚠️  Skipped plan change for user {$planChange->user_id}: {$result['reason']}");
            }

            if ($result['status'] === 'failed') {
                $this->stats['failed']++;
            }

            if ($result['status'] === 'success') {
                $this->stats['successful']++;
            }

        } catch (Exception $exception) {
            $this->handleException($planChange, $exception, $isDryRun);
        }
    }

    /**
     * Validate a plan change request.
     *
     * @return array{valid: bool, reason: string}
     */
    private function validatePlanChange(SubscriptionPlanChange $planChange): array
    {
        $user = $planChange->user;

        $subscriptionService = resolve(SubscriptionService::class);

        if (! $user) {
            return ['valid' => false, 'reason' => 'User not found'];
        }

        $activeSubscription = $subscriptionService->getActiveSubscription($user);

        if (! $activeSubscription) {
            return ['valid' => false, 'reason' => 'No active subscription found'];
        }

        $currentPlan = $user->getCurrentPlan();
        $targetPlan = $planChange->toPlan;

        if (! $targetPlan) {
            return ['valid' => false, 'reason' => 'Target plan not found'];
        }

        if (! $targetPlan->isActive()) {
            return ['valid' => false, 'reason' => 'Target plan is not active'];
        }

        if ($currentPlan && $currentPlan->key === $targetPlan->key) {
            return ['valid' => false, 'reason' => 'User is already on the requested plan'];
        }

        // Check if fromPlan matches user's current plan to avoid stale requests
        if ($planChange->fromPlan && $currentPlan && $planChange->fromPlan->key !== $currentPlan->key) {
            return ['valid' => false, 'reason' => "User's current plan ({$currentPlan->key}) does not match the requested from-plan ({$planChange->fromPlan->key})"];
        }

        return ['valid' => true, 'reason' => null];
    }

    /**
     * Validate user's payment methods.
     *
     * @return array{valid: bool, reason: string}
     */
    private function validatePaymentMethods(SubscriptionPlanChange $planChange): array
    {
        $paymentMethods = $planChange->user->paymentMethods;
        $customer = $planChange->user->customer;

        if ($paymentMethods->isEmpty()) {
            return ['valid' => false, 'reason' => 'No payment methods found'];
        }

        $validPaymentMethods = $paymentMethods->filter(fn ($paymentMethod): bool => ! $paymentMethod->isExpired() && $paymentMethod->canBeUsedForSubscriptions());

        if ($validPaymentMethods->isEmpty()) {
            return ['valid' => false, 'reason' => 'No valid payment methods available'];
        }

        $defaultPaymentMethod = $validPaymentMethods->firstWhere('is_default', true);

        if (! $defaultPaymentMethod) {
            return ['valid' => false, 'reason' => 'No default payment method found'];
        }

        $paymentProcessor = PaymentProcessorFactory::make($defaultPaymentMethod->processor_type);

        try {
            $paymentValidation = $paymentProcessor->validatePaymentMethod($customer, $defaultPaymentMethod);
        } catch (Throwable) {
            return ['valid' => false, 'reason' => $paymentValidation->reason];
        }

        if (! $paymentValidation->isValid()) {
            return ['valid' => false, 'reason' => $paymentValidation->reason];
        }

        return ['valid' => true, 'reason' => null];
    }

    /**
     * Execute the plan change.
     *
     * @return array{success: bool, message?: string, error?: string}
     */
    private function executePlanChange(SubscriptionPlanChange $planChange): array
    {
        $user = $planChange->user;
        $targetPlan = $planChange->toPlan;

        return $user->changeSubscriptionPlan($targetPlan->key);
    }

    /**
     * Handle validation failure.
     */
    private function handleValidationFailure(SubscriptionPlanChange $planChange, string $reason, bool $isDryRun): void
    {
        $this->stats['validation_failures']++;
        $this->stats['failed']++;

        if (! $isDryRun) {
            $planChange->update([
                'status' => SubscriptionStatus::FAILED,
                'error_message' => "Validation failed: {$reason}",
                'processed_at' => now(),
            ]);
        }

        $this->warn("  ❌ Validation failed for user {$planChange->user_id}: {$reason}");

        Log::warning('Plan change validation failed', [
            'plan_change_id' => $planChange->id,
            'user_id' => $planChange->user_id,
            'reason' => $reason,
        ]);
    }

    /**
     * Handle payment validation failure.
     */
    private function handlePaymentFailure(SubscriptionPlanChange $planChange, string $reason, bool $isDryRun): void
    {
        $this->stats['payment_failures']++;
        $this->stats['failed']++;

        if (! $isDryRun) {
            $planChange->update([
                'status' => SubscriptionStatus::FAILED,
                'error_message' => "Payment validation failed: {$reason}",
                'processed_at' => now(),
            ]);
        }

        $this->warn("  ❌ Payment validation failed for user {$planChange->user_id}: {$reason}");

        Log::warning('Plan change payment validation failed', [
            'plan_change_id' => $planChange->id,
            'user_id' => $planChange->user_id,
            'reason' => $reason,
        ]);
    }

    /**
     * Handle successful plan change.
     *
     * @param  array{success: bool, message?: string, error?: string}  $result
     */
    private function handleSuccess(SubscriptionPlanChange $planChange, array $result): void
    {
        $this->stats['successful']++;

        $planChange->update([
            'status' => SubscriptionStatus::COMPLETED,
            'processor_response' => $result,
            'processed_at' => now(),
            'effective_at' => now(),
        ]);

        $this->info("  ✅ Successfully changed plan for user {$planChange->user_id}");

        Log::info('Plan change completed successfully', [
            'plan_change_id' => $planChange->id,
            'user_id' => $planChange->user_id,
            'old_plan' => $result['old_plan'] ?? null,
            'new_plan' => $result['new_plan'] ?? null,
        ]);
    }

    /**
     * Handle plan change execution failure.
     */
    private function handlePlanChangeFailure(SubscriptionPlanChange $planChange, string $error): void
    {
        $this->stats['plan_change_failures']++;
        $this->stats['failed']++;

        $planChange->update([
            'status' => SubscriptionStatus::FAILED,
            'error_message' => "Plan change failed: {$error}",
            'processed_at' => now(),
        ]);

        $this->error("  ❌ Plan change failed for user {$planChange->user_id}: {$error}");

        Log::error('Plan change execution failed', [
            'plan_change_id' => $planChange->id,
            'user_id' => $planChange->user_id,
            'error' => $error,
        ]);
    }

    /**
     * Handle unexpected exceptions.
     */
    private function handleException(SubscriptionPlanChange $planChange, Exception $e, bool $isDryRun): void
    {
        $this->stats['failed']++;

        if (! $isDryRun) {
            $planChange->update([
                'status' => SubscriptionStatus::FAILED,
                'error_message' => "Unexpected error: {$e->getMessage()}",
                'processed_at' => now(),
            ]);
        }

        $this->error("  ❌ Unexpected error for user {$planChange->user_id}: {$e->getMessage()}");

        Log::error('Unexpected error processing plan change', [
            'plan_change_id' => $planChange->id,
            'user_id' => $planChange->user_id,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
        ]);
    }

    /**
     * Display processing summary.
     */
    private function displaySummary(bool $isDryRun): void
    {
        $this->info('📊 Processing Summary:');
        $this->line("  Total Processed: {$this->stats['total_processed']}");
        $this->line("  <fg=green>Successful: {$this->stats['successful']}</>");
        $this->line("  <fg=green>Skipped: {$this->stats['skipped']}</>");
        $this->line("  <fg=red>Failed: {$this->stats['failed']}</>");

        if ($this->stats['validation_failures'] > 0) {
            $this->line("    - Validation Failures: {$this->stats['validation_failures']}");
        }

        if ($this->stats['payment_failures'] > 0) {
            $this->line("    - Payment Failures: {$this->stats['payment_failures']}");
        }

        if ($this->stats['plan_change_failures'] > 0) {
            $this->line("    - Plan Change Failures: {$this->stats['plan_change_failures']}");
        }

        $this->newLine();

        if ($isDryRun) {
            $this->info('🔍 This was a dry run - no actual changes were made.');
        } else {
            $successRate = $this->stats['total_processed'] > 0
                ? round(($this->stats['successful'] / $this->stats['total_processed']) * 100, 2)
                : 0;
            $this->info("✅ Processing completed with {$successRate}% success rate.");
        }
    }
}
