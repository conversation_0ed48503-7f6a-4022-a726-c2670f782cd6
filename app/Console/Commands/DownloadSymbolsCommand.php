<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use Radical<PERSON>oop\Eod\Facades\Eod;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

final class DownloadSymbolsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'symbols:download';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Download all symbols from EOD Historical Data and store in local file';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting symbol download...');

        try {
            // Get API token from config
            $apiToken = config('services.eod.api_token');

            if (! $apiToken) {
                $this->error('EOD Historical Data API token not configured. Please set EOD_API_TOKEN in your .env file');

                return 1;
            }

            // Download symbols from EOD Historical Data
            $this->info('Fetching symbols from EOD Historical Data...');

            $exchange = Eod::exchange();

            if (! $exchange) {
                $this->error('Failed to fetch symbols from API');

                return 1;
            }

            $jsonResponse = $exchange->symbol('US')->json();
            if (! is_string($jsonResponse)) {
                $this->error('Invalid response format from API');

                return 1;
            }

            $symbols = json_decode($jsonResponse);
            if ($symbols === null) {
                $this->error('Failed to decode JSON response from API');

                return 1;
            }

            if (empty($symbols)) {
                $this->error('No symbols received from API');

                return 1;
            }

            // Store symbols in local file
            $symbolsJson = json_encode($symbols, JSON_PRETTY_PRINT);
            if ($symbolsJson === false) {
                $this->error('Failed to encode symbols data');

                return 1;
            }

            Storage::disk('local')->put('symbols.json', $symbolsJson);

            // Clear the cache to force fresh data on next request
            Cache::forget('symbols');
            Cache::forget('symbols_count');

            $count = is_countable($symbols) ? count($symbols) : 0;
            $this->info("Successfully downloaded and cached {$count} symbols");

            return 0;

        } catch (Exception $exception) {
            $this->error('Error downloading symbols: '.$exception->getMessage());

            return 1;
        }
    }
}
