<?php

declare(strict_types=1);

namespace App\Console\Commands\Sync;

use Exception;
use Illuminate\Console\Command;
use App\Services\PaymentProcessor\PaymentProcessorFactory;

final class SyncPlansCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sync:plans
                            {--processor= : Payment processor (paddle, stripe, or all)}
                            {--dry-run : Run without making changes}';

    /**
     * The console command description.
     */
    protected $description = 'Synchronize subscription plans from payment processors';

    public function __construct(
        private readonly PaymentProcessorFactory $paymentProcessorFactory
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $processor = $this->option('processor') ?? 'all';
        $dryRun = (bool) $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🔍 Running in DRY-RUN mode - no changes will be made');
        }

        if (filled($processor)) {
            $processors = $this->paymentProcessorFactory->create($processor);
        } else {
            $enabledProcessors = $this->paymentProcessorFactory->getEnabledProcessors();
            foreach ($enabledProcessors as $processorName) {
                $processors[$name] = $this->paymentProcessorFactory->create($processorName);
            }
        }

        foreach ($processors as $name => $service) {
            $this->info("🔄 Syncing {$name} plans...");

            try {
                $result = $service->syncSubscriptionPlans($dryRun);

                $processed = data_get($result, 'processed', 0);
                $failed = data_get($result, 'failed', 0);

                if ($result['success']) {
                    $this->info("✅ {$name}: Processed {$processed} plans");
                } else {
                    $this->error("❌ {$name}: Failed with {$failed} errors");
                }
            } catch (Exception $e) {
                $this->error("❌ {$name}: {$e->getMessage()}");

                return self::FAILURE;
            }
        }

        $this->newLine();
        $this->info('✨ Plan sync complete!');

        return self::SUCCESS;
    }
}
