<?php

declare(strict_types=1);

namespace App\Console;

use Illuminate\Support\Facades\Log;
use Illuminate\Console\Scheduling\Schedule;

final class JobScheduler
{
    /**
     * Define the application's command schedule.
     */
    public function schedule(Schedule $schedule): void
    {
        // Minute Tasks
        $this->scheduleMinutelyTasks($schedule);

        // Hourly Tasks
        $this->scheduleHourlyTasks($schedule);

        // // Daily Tasks
        $this->scheduleDailyTasks($schedule);

        // // Weekly Tasks
        $this->scheduleWeeklyTasks($schedule);

        // // Monthly Tasks
        $this->scheduleMonthlyTasks($schedule);
    }

    private function scheduleMinutelyTasks(Schedule $schedule): void
    {
        $schedule->command('plan-changes:process')->everyMinute()->withoutOverlapping();
    }

    /**
     * Schedule hourly tasks.
     */
    private function scheduleHourlyTasks(Schedule $schedule): void
    {
        // Generate hourly performance reports
        $schedule->command('authorization:performance-report --period=1h --format=json')
            ->hourly()
            ->withoutOverlapping()
            ->runInBackground()
            ->onSuccess(function (): void {
                Log::info('Hourly authorization performance report generated successfully.');
            })
            ->onFailure(function (): void {
                Log::error('Failed to generate hourly authorization performance report.');
            });

        // Warm authorization cache
        $schedule->command('authorization:cache-clear --warm')
            ->hourly()
            ->everyThirtyMinutes() // Run at 30 minutes past the hour
            ->withoutOverlapping()
            ->runInBackground()
            ->onSuccess(function (): void {
                Log::info('Authorization cache warmed successfully.');
            })
            ->onFailure(function (): void {
                Log::error('Failed to warm authorization cache.');
            });

        // Sync permissions (every 6 hours)
        $schedule->command('authorization:sync-permissions')
            ->everySixHours()
            ->withoutOverlapping()
            ->runInBackground()
            ->onSuccess(function (): void {
                Log::info('Authorization permissions synced successfully.');
            })
            ->onFailure(function (): void {
                Log::error('Failed to sync authorization permissions.');
            });

        $schedule->command('sync:subscriptions')
            ->hourly()
            ->withoutOverlapping();
    }

    /**
     * Schedule daily tasks.
     */
    private function scheduleDailyTasks(Schedule $schedule): void
    {
        $healthCheckUrl = config('services.health_check.url');

        $schedule->command('symbols:download')
            ->dailyAt('00:00')
            ->onOneServer()
            ->withoutOverlapping();

        // Daily payment method synchronization (runs at 05:00 AM)
        $command = $schedule->command('payment-methods:sync --cleanup')
            ->dailyAt('05:00')
            ->withoutOverlapping()
            ->runInBackground()
            ->onSuccess(function (): void {
                Log::info('Daily payment method synchronization completed successfully.');
            })
            ->onFailure(function (): void {
                Log::error('Daily payment method synchronization failed.');
            });

        if ($healthCheckUrl) {
            $command->pingBefore($healthCheckUrl.'/payment-methods-sync/start')
                ->thenPing($healthCheckUrl.'/payment-methods-sync/success')
                ->pingOnFailure($healthCheckUrl.'/payment-methods-sync/failure');
        }

        // Daily audit cleanup (runs at 3:00 AM)
        $command = $schedule->command('authorization:audit-cleanup --days=90 --archive --keep-critical --vacuum')
            ->dailyAt('03:00')
            ->withoutOverlapping()
            ->runInBackground()
            ->onSuccess(function (): void {
                Log::info('Daily audit cleanup completed successfully.');
            })
            ->onFailure(function (): void {
                Log::error('Daily audit cleanup failed.');
            });

        if ($healthCheckUrl) {
            $command->pingBefore($healthCheckUrl.'/audit-cleanup/start')
                ->thenPing($healthCheckUrl.'/audit-cleanup/success')
                ->pingOnFailure($healthCheckUrl.'/audit-cleanup/failure');
        }

        // Daily performance benchmark (runs at 2:00 AM)
        $schedule->command('authorization:performance-benchmark --iterations=50 --users=5 --permissions=25 --cleanup')
            ->dailyAt('02:00')
            ->withoutOverlapping()
            ->runInBackground()
            ->environments(['production']) // Only run in production
            ->onSuccess(function (): void {
                Log::info('Daily performance benchmark completed successfully.');
            })
            ->onFailure(function (): void {
                Log::error('Daily performance benchmark failed.');
            });

        // Daily comprehensive performance report (runs at 1:00 AM)
        $command = $schedule->command('authorization:performance-report --period=24h --format=json --detailed --export=storage/reports/daily_auth_report.json')
            ->dailyAt('01:00')
            ->withoutOverlapping()
            ->runInBackground()
            ->onSuccess(function (): void {
                Log::info('Daily comprehensive performance report generated successfully.');
            })
            ->onFailure(function (): void {
                Log::error('Failed to generate daily comprehensive performance report.');
            });

        if ($healthCheckUrl) {
            $command->pingBefore($healthCheckUrl.'/performance-report/start')
                ->thenPing($healthCheckUrl.'/performance-report/success')
                ->pingOnFailure($healthCheckUrl.'/performance-report/failure');
        }

        // Export daily audit logs (runs at 4:00 AM)
        $schedule->command('authorization:audit-export --from=yesterday --to=yesterday --format=json --compress --split-by-date')
            ->dailyAt('04:00')
            ->withoutOverlapping()
            ->runInBackground()
            ->onSuccess(function (): void {
                Log::info('Daily audit export completed successfully.');
            })
            ->onFailure(function (): void {
                Log::error('Daily audit export failed.');
            });
    }

    /**
     * Schedule weekly tasks.
     */
    private function scheduleWeeklyTasks(Schedule $schedule): void
    {
        // Weekly comprehensive audit export (runs on Sundays at 5:00 AM)
        $schedule->command('authorization:audit-export --from="1 week ago" --format=json --compress --include-metadata')
            ->weeklyOn(0, '05:00') // Sunday at 5:00 AM
            ->withoutOverlapping()
            ->runInBackground()
            ->onSuccess(function (): void {
                Log::info('Weekly audit export completed successfully.');
            })
            ->onFailure(function (): void {
                Log::error('Weekly audit export failed.');
            });

        // Weekly performance benchmark with extended tests (runs on Saturdays at 6:00 AM)
        $schedule->command('authorization:performance-benchmark --iterations=200 --users=20 --permissions=100 --export=storage/benchmarks/weekly_benchmark.json --cleanup')
            ->weeklyOn(6, '06:00') // Saturday at 6:00 AM
            ->withoutOverlapping()
            ->runInBackground()
            ->environments(['production'])
            ->onSuccess(function (): void {
                Log::info('Weekly performance benchmark completed successfully.');
            })
            ->onFailure(function (): void {
                Log::error('Weekly performance benchmark failed.');
            });

        // Weekly performance trends report (runs on Sundays at 7:00 AM)
        $schedule->command('authorization:performance-report --period=7d --format=json --detailed --export=storage/reports/weekly_trends.json')
            ->weeklyOn(0, '07:00') // Sunday at 7:00 AM
            ->withoutOverlapping()
            ->runInBackground()
            ->onSuccess(function (): void {
                Log::info('Weekly performance trends report generated successfully.');
            })
            ->onFailure(function (): void {
                Log::error('Failed to generate weekly performance trends report.');
            });
    }

    /**
     * Schedule monthly tasks.
     */
    private function scheduleMonthlyTasks(Schedule $schedule): void
    {
        // Monthly comprehensive audit cleanup with extended retention (runs on 1st at 1:00 AM)
        $schedule->command('authorization:audit-cleanup --days=365 --archive --archive-format=json --keep-critical --vacuum')
            ->monthlyOn(1, '01:00')
            ->withoutOverlapping()
            ->runInBackground()
            ->onSuccess(function (): void {
                Log::info('Monthly comprehensive audit cleanup completed successfully.');
            })
            ->onFailure(function (): void {
                Log::error('Monthly comprehensive audit cleanup failed.');
            });

        // Monthly performance analysis report (runs on 1st at 8:00 AM)
        $schedule->command('authorization:performance-report --period=30d --format=json --detailed --export=storage/reports/monthly_analysis.json')
            ->monthlyOn(1, '08:00')
            ->withoutOverlapping()
            ->runInBackground()
            ->onSuccess(function (): void {
                Log::info('Monthly performance analysis report generated successfully.');
            })
            ->onFailure(function (): void {
                Log::error('Failed to generate monthly performance analysis report.');
            });

        // Monthly stress test benchmark (runs on 15th at 2:00 AM)
        $schedule->command('authorization:performance-benchmark --iterations=500 --users=50 --permissions=200 --export=storage/benchmarks/monthly_stress_test.json --cleanup')
            ->monthlyOn(15, '02:00')
            ->withoutOverlapping()
            ->runInBackground()
            ->environments(['production'])
            // ->withTimeout(3600) // 1 hour timeout for stress test (method not available in this Laravel version)
            ->onSuccess(function (): void {
                Log::info('Monthly stress test benchmark completed successfully.');
            })
            ->onFailure(function (): void {
                Log::error('Monthly stress test benchmark failed.');
            });
    }
}
