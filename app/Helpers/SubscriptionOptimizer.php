<?php

declare(strict_types=1);

namespace App\Helpers;

use App\Models\Plan;
use App\Models\User;
use App\Models\Subscription;
use App\Enums\SubscriptionStatus;
use App\Services\SubscriptionCacheService;
use App\Services\SubscriptionQueryOptimizer;

final class SubscriptionOptimizer
{
    /**
     * Optimized method to get user's active subscription
     */
    public static function getUserActiveSubscription(User $user): ?Subscription
    {
        // Use the cache service if available, fall back to optimized query
        if (app()->bound(SubscriptionCacheService::class)) {
            $cacheService = app(SubscriptionCacheService::class);

            return $cacheService->getUserActiveSubscription($user);
        }

        // Fallback optimized query
        return $user->subscriptions()
            ->with(['plan', 'items'])
            ->where('status', SubscriptionStatus::ACTIVE)
            ->latest()
            ->first();
    }

    /**
     * Optimized method to get active plans
     */
    public static function getActivePlans(): array
    {
        if (app()->bound(SubscriptionCacheService::class)) {
            $cacheService = app(SubscriptionCacheService::class);

            return $cacheService->getActivePlans();
        }

        return Plan::with(['features'])
            ->where('active', true)
            ->orderBy('price', 'asc')
            ->get()
            ->toArray();
    }

    /**
     * Optimized method to get subscription analytics
     */
    public static function getAnalytics(): array
    {
        if (app()->bound(SubscriptionCacheService::class)) {
            $cacheService = app(SubscriptionCacheService::class);

            return $cacheService->getSubscriptionAnalytics();
        }

        return [
            'total_active_subscriptions' => Subscription::query()->where('status', 'active')->count(),
            'total_revenue_monthly' => Subscription::query()->where('status', 'active')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->join('plans', 'subscriptions.plan_id', '=', 'plans.id')
                ->sum('plans.price'),
            'new_subscriptions_this_month' => Subscription::query()->where('status', 'active')
                ->whereMonth('created_at', now()->month)
                ->count(),
            'cancelled_subscriptions_this_month' => Subscription::query()->where('status', 'cancelled')
                ->whereMonth('updated_at', now()->month)
                ->count(),
        ];
    }

    /**
     * Clear user-related cache
     */
    public static function clearUserCache(User $user): void
    {
        if (app()->bound(SubscriptionCacheService::class)) {
            $cacheService = app(SubscriptionCacheService::class);
            $cacheService->clearUserCache($user);
        }
    }

    /**
     * Clear all subscription cache
     */
    public static function clearAllCache(): void
    {
        if (app()->bound(SubscriptionCacheService::class)) {
            $cacheService = app(SubscriptionCacheService::class);
            $cacheService->clearAllCache();
        }
    }

    /**
     * Get users with optimized subscription queries
     */
    public static function getUsersWithSubscriptions(array $userIds): array
    {
        if (app()->bound(SubscriptionQueryOptimizer::class)) {
            $optimizer = app(SubscriptionQueryOptimizer::class);

            return $optimizer->getUsersWithSubscriptions($userIds)->toArray();
        }

        return User::with([
            'subscriptions' => function ($query): void {
                $query->where('status', SubscriptionStatus::ACTIVE)
                    ->orderBy('created_at', 'desc');
            },
            'subscriptions.plan',
            'subscriptions.items',
        ])
            ->whereIn('id', $userIds)
            ->get()
            ->toArray();
    }
}
