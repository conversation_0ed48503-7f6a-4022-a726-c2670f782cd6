<?php

declare(strict_types=1);

namespace App\Filament\Pages;

use Exception;
use App\Models\User;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Actions\Action;
use App\Settings\PaymentSettings;
use Filament\Forms\Components\Radio;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Toggle;
use App\Events\PaymentSettingsUpdated;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Validator;
use Filament\Pages\Concerns\InteractsWithFormActions;

/**
 * @property Form $form
 */
final class PaymentProcessorSettings extends Page
{
    use InteractsWithFormActions;

    public ?array $data = [];

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';

    protected static ?string $navigationGroup = 'Administration';

    protected static ?string $navigationLabel = 'Payment Processors';

    protected static ?int $navigationSort = 10;

    protected static string $view = 'filament.pages.payment-processor-settings';

    public static function canAccess(): bool
    {
        /** @var User|null $user */
        $user = Auth::user();

        if (! $user) {
            return false;
        }

        if ($user->hasRole('super_admin')) {
            return true;
        }

        return $user->hasRole('admin') && $user->can('settings.modify');
    }

    public function mount(): void
    {
        $settings = PaymentSettings::fake([]);
        $this->form->fill($settings->toArray());
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Global Settings')
                    ->description('Configure default payment processor for the application.')
                    ->schema([
                        Radio::make('default_processor')
                            ->label('Default Payment Processor')
                            ->options([
                                'stripe' => 'Stripe',
                                'paypal' => 'PayPal',
                                'paddle' => 'Paddle',
                                'dummy' => 'Dummy (Testing)',
                            ])
                            ->required()
                            ->default('stripe')
                            ->descriptions([
                                'stripe' => 'Must be enabled and configured with credentials',
                                'paypal' => 'Must be enabled and configured with credentials',
                                'paddle' => 'Must be enabled and configured with credentials',
                                'dummy' => 'Available without configuration',
                            ])
                            ->helperText('Select a payment processor that is enabled and configured'),
                    ]),

                Section::make('Stripe Configuration')
                    ->description('Configure Stripe payment processing.')
                    ->schema([
                        Toggle::make('stripe_enabled')
                            ->label('Enable Stripe')
                            ->default(false),
                        TextInput::make('stripe_key')
                            ->label('Publishable Key')
                            ->password()
                            ->revealable()
                            ->requiredIf('stripe_enabled', true),
                        TextInput::make('stripe_secret')
                            ->label('Secret Key')
                            ->password()
                            ->revealable()
                            ->requiredIf('stripe_enabled', true),
                        TextInput::make('stripe_webhook_secret')
                            ->label('Webhook Secret')
                            ->password()
                            ->revealable()
                            ->requiredIf('stripe_enabled', true)
                            ->helperText('Webhook URL: '.url('/webhook/stripe')),
                    ])
                    ->columns(1),

                Section::make('PayPal Configuration')
                    ->description('Configure PayPal payment processing.')
                    ->schema([
                        Toggle::make('paypal_enabled')
                            ->label('Enable PayPal')
                            ->default(false),
                        TextInput::make('paypal_client_id')
                            ->label('Client ID')
                            ->password()
                            ->revealable()
                            ->requiredIf('paypal_enabled', true),
                        TextInput::make('paypal_client_secret')
                            ->label('Client Secret')
                            ->password()
                            ->revealable()
                            ->requiredIf('paypal_enabled', true),
                        TextInput::make('paypal_webhook_secret')
                            ->label('Webhook Secret')
                            ->password()
                            ->revealable()
                            ->requiredIf('paypal_enabled', true)
                            ->helperText('Webhook URL: '.url('/webhook/paypal')),
                        Toggle::make('paypal_sandbox')
                            ->label('Sandbox Mode')
                            ->default(true)
                            ->helperText('Use PayPal sandbox for testing'),
                    ])
                    ->columns(1),

                Section::make('Paddle Configuration')
                    ->description('Configure Paddle payment processing.')
                    ->schema([
                        Toggle::make('paddle_enabled')
                            ->label('Enable Paddle')
                            ->default(false),
                        TextInput::make('paddle_vendor_id')
                            ->label('Vendor ID')
                            ->password()
                            ->revealable()
                            ->requiredIf('paddle_enabled', true),
                        TextInput::make('paddle_api_key')
                            ->label('API Key')
                            ->password()
                            ->revealable()
                            ->requiredIf('paddle_enabled', true),
                        TextInput::make('paddle_vendor_auth_code')
                            ->label('Vendor Auth Code')
                            ->password()
                            ->revealable()
                            ->requiredIf('paddle_enabled', true),
                        TextInput::make('paddle_webhook_secret')
                            ->label('Webhook Secret')
                            ->password()
                            ->revealable()
                            ->requiredIf('paddle_enabled', true)
                            ->helperText('Webhook URL: '.url('/webhook/paddle')),
                    ])
                    ->columns(1),
            ])
            ->statePath('data');
    }

    public function save(): void
    {
        try {
            $data = $this->form->getState();

            // Build validator with cross-field validation
            $validator = Validator::make($data, PaymentSettings::rules())->after(function ($validator) use ($data): void {
                $defaultProcessor = $data['default_processor'] ?? null;

                if ($defaultProcessor) {
                    // Check if the selected default processor is enabled
                    $enabledField = $defaultProcessor.'_enabled';
                    if (empty($data[$enabledField])) {
                        $validator->errors()->add('default_processor', "The selected default processor '{$defaultProcessor}' must be enabled.");

                        return;
                    }

                    // Check if all required credential fields are filled
                    $requiredFields = match ($defaultProcessor) {
                        'stripe' => ['stripe_key', 'stripe_secret', 'stripe_webhook_secret'],
                        'paypal' => ['paypal_client_id', 'paypal_client_secret', 'paypal_webhook_secret'],
                        'paddle' => ['paddle_vendor_id', 'paddle_vendor_auth_code', 'paddle_webhook_secret'],
                        'dummy' => [],
                        default => [],
                    };

                    foreach ($requiredFields as $field) {
                        if (empty($data[$field])) {
                            $validator->errors()->add($field, "The {$field} field is required when {$defaultProcessor} is set as the default processor.");
                        }
                    }
                }
            });

            throw_if($validator->fails(), new Exception($validator->errors()->first()));

            $settings = PaymentSettings::fake([]);
            $settings->fill($data);
            $settings->save();

            event(new PaymentSettingsUpdated($data));

            Notification::make()
                ->title('Payment processor settings saved successfully')
                ->success()
                ->send();

            $this->redirect(self::getUrl());
        } catch (Exception $exception) {
            Notification::make()
                ->title('Failed to save payment processor settings')
                ->body($exception->getMessage())
                ->danger()
                ->send();
        }
    }

    public function getFormActions(): array
    {
        return [
            Action::make('save')
                ->label('Save')
                ->action('save'),
        ];
    }
}
