<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use App\Models\User;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\Models\Activity;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Filament\Widgets\TableWidget as BaseWidget;

final class SecurityAlertsWidget extends BaseWidget
{
    protected static ?string $heading = 'Recent Security Alerts';

    protected int|string|array $columnSpan = 'full';

    protected static ?int $sort = 2;

    private static ?string $pollingInterval = '30s';

    /**
     * Widget configuration.
     */
    public static function canView(): bool
    {
        $user = Auth::user();

        return $user?->can('admin.access') ?? false;
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                Tables\Columns\TextColumn::make('description')
                    ->label('Alert')
                    ->searchable()
                    ->sortable()
                    ->weight('medium')
                    ->formatStateUsing(fn (string $state): string => $this->formatAlertDescription($state))
                    ->color(fn (Activity $record): string => $this->getAlertColor($record)),

                Tables\Columns\TextColumn::make('properties.severity')
                    ->label('Severity')
                    ->badge()
                    ->color(fn (?string $state): string => match ($state) {
                        'low' => 'success',
                        'medium' => 'warning',
                        'high' => 'danger',
                        'info' => 'gray',
                        default => 'gray'
                    })
                    ->formatStateUsing(fn (?string $state): string => ucfirst($state ?? 'medium')),

                Tables\Columns\TextColumn::make('causer.name')
                    ->label('User')
                    ->searchable()
                    ->sortable()
                    ->default('System')
                    ->formatStateUsing(fn (?string $state, Activity $record): string => $state ?? ($record->causer_id ? 'Unknown User' : 'System')
                    ),

                Tables\Columns\TextColumn::make('properties.ip_address')
                    ->label('IP Address')
                    ->searchable()
                    ->toggleable()
                    ->default('N/A'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Time')
                    ->dateTime('M j, Y H:i')
                    ->sortable()
                    ->since()
                    ->tooltip(fn (Activity $record): string => $record->created_at->format('F j, Y \a\t g:i A')),

                Tables\Columns\IconColumn::make('reviewed')
                    ->label('Status')
                    ->boolean()
                    ->getStateUsing(fn (Activity $record): bool => $record->properties['reviewed'] ?? false
                    )
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-exclamation-triangle')
                    ->trueColor('success')
                    ->falseColor('warning'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('severity')
                    ->options([
                        'low' => 'Low',
                        'medium' => 'Medium',
                        'high' => 'High',
                        'critical' => 'Critical',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (filled($data['value'])) {
                            return $query->whereJsonContains('properties->severity', $data['value']);
                        }

                        return $query;
                    }),

                Tables\Filters\Filter::make('unreviewed')
                    ->label('Unreviewed Only')
                    ->query(fn (Builder $query): Builder => $query->where(function (Builder $q): void {
                        $q->whereJsonMissing('properties->reviewed')
                            ->orWhereJsonContains('properties->reviewed', false);
                    })
                    )
                    ->toggle(),

                Tables\Filters\Filter::make('last_24h')
                    ->label('Last 24 Hours')
                    ->query(fn (Builder $query): Builder => $query->where('created_at', '>=', now()->subDay())
                    )
                    ->toggle()
                    ->default(),
            ])
            ->actions([
                Tables\Actions\Action::make('view_details')
                    ->label('Details')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->modalHeading(fn (Activity $record): string => 'Security Alert Details')
                    ->modalContent(fn (Activity $record): View => view('filament.widgets.security-alert-details', ['record' => $this->prepareRecordForDetails($record)])
                    )
                    ->modalFooterActions([
                        Tables\Actions\Action::make('mark_reviewed')
                            ->label('Mark as Reviewed')
                            ->icon('heroicon-o-check')
                            ->color('success')
                            ->action(function (Activity $record): void {
                                $this->markAlertAsReviewed($record);

                                $this->dispatch('alert-reviewed');
                            })
                            ->requiresConfirmation()
                            ->visible(fn (Activity $record): bool => ! ($record->properties['reviewed'] ?? false)
                            ),
                    ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('mark_all_reviewed')
                    ->label('Mark All as Reviewed')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function (Collection $records): void {
                        foreach ($records as $record) {
                            $this->markAlertAsReviewed($record);
                        }

                        $this->dispatch('alerts-reviewed');
                    })
                    ->requiresConfirmation()
                    ->deselectRecordsAfterCompletion(),
            ])
            ->defaultSort('created_at', 'desc')
            ->poll('30s')
            ->striped()
            ->paginated([10, 25, 50])
            ->defaultPaginationPageOption(10)
            ->emptyStateHeading('No Security Alerts')
            ->emptyStateDescription('No security alerts have been recorded recently.')
            ->emptyStateIcon('heroicon-o-shield-check');
    }

    public function getDisplayName(): string
    {
        return 'Security Alerts';
    }

    /**
     * Get the table query for security alerts.
     */
    protected function getTableQuery(): Builder
    {
        return Activity::query()
            ->where('log_name', 'security')
            ->where('created_at', '>=', now()->subWeek())
            ->with(['causer'])
            ->orderBy('created_at', 'desc');
    }

    protected function getTableRecordsPerPageSelectOptions(): array
    {
        return [10, 25, 50];
    }

    /**
     * Refresh the widget when alerts are reviewed.
     */
    protected function getListeners(): array
    {
        return [
            'alert-reviewed' => '$refresh',
            'alerts-reviewed' => '$refresh',
        ];
    }

    /**
     * Get the widget's polling interval.
     */
    private function getPollingInterval(): string
    {
        return '30s';
    }

    /**
     * Prepare record for details view with eager loaded relationships.
     */
    private function prepareRecordForDetails(Activity $record): Activity
    {
        // If there's a reviewed_by user ID, eager load the reviewer
        if ($reviewedBy = $record->properties['reviewed_by'] ?? null) {
            $reviewer = User::query()->select('id', 'name')->find($reviewedBy);
            $record->setRelation('reviewer', $reviewer);
        }

        return $record;
    }

    /**
     * Mark an alert as reviewed.
     */
    private function markAlertAsReviewed(Activity $record): void
    {
        $properties = $record->properties->toArray();
        $properties['reviewed'] = true;
        $properties['reviewed_at'] = now()->toISOString();
        $properties['reviewed_by'] = Auth::id();

        $record->update(['properties' => $properties]);
    }

    /**
     * Format alert description for display.
     */
    private function formatAlertDescription(string $description): string
    {
        // Remove 'security_event: ' prefix if present
        $formatted = str_replace('security_event: ', '', $description);

        // Convert snake_case to Title Case
        $formatted = str_replace('_', ' ', $formatted);
        $formatted = ucwords($formatted);

        // Add specific formatting for common events
        $replacements = [
            'Failed Login' => '🔒 Failed Login Attempt',
            'Suspicious Activity' => '⚠️ Suspicious Activity Detected',
            'Permission Denied' => '🚫 Permission Denied',
            'Multiple Failed Attempts' => '🔴 Multiple Failed Login Attempts',
            'Unusual Access Pattern' => '🔍 Unusual Access Pattern',
            'Performance Anomaly' => '📊 Performance Anomaly',
            'Rate Limit Exceeded' => '⏱️ Rate Limit Exceeded',
        ];

        return $replacements[$formatted] ?? $formatted;
    }

    /**
     * Get alert color based on severity and type.
     */
    private function getAlertColor(Activity $record): string
    {
        $severity = $record->properties['severity'] ?? 'medium';

        return match ($severity) {
            'critical' => 'danger',
            'high' => 'danger',
            'medium' => 'warning',
            'low' => 'info',
            default => 'gray',
        };
    }
}
