<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use Exception;
use Carbon\Carbon;
use App\Services\AuditLogService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use App\Services\PerformanceMetricsService;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

final class AuthorizationMetricsWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';

    protected static bool $isLazy = false;

    protected int|string|array $columnSpan = 'full';

    protected static ?int $sort = 1;

    private AuditLogService $auditService;

    private PerformanceMetricsService $metricsService;

    /**
     * Widget configuration.
     */
    public static function canView(): bool
    {
        return Auth::user()?->can('admin.access') ?? false;
    }

    public function getDisplayName(): string
    {
        return 'Authorization Metrics';
    }

    protected function getStats(): array
    {
        try {
            $this->auditService = app(AuditLogService::class);
            $this->metricsService = app(PerformanceMetricsService::class);

            $auditStats = $this->auditService->getAuditStatistics('today');
            $dashboardMetrics = $this->metricsService->getDashboardMetrics('today');
            $securityAlerts = $this->auditService->getSecurityAlerts(5);
        } catch (Exception $exception) {
            // Log error and return empty stats to prevent widget crash
            logger()->error('Failed to load authorization metrics: '.$exception->getMessage());
            $auditStats = [];
            $dashboardMetrics = [];
            $securityAlerts = [];
        }

        return [
            // Total Activities Today
            Stat::make('Total Activities Today', $auditStats['total_activities'] ?? 0)
                ->description('All authorization activities')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color($this->getActivityColor($auditStats['total_activities'] ?? 0))
                ->chart($this->getActivityChart()),

            // Security Events
            Stat::make('Security Events', $auditStats['security_events'] ?? 0)
                ->description('Security-related activities')
                ->descriptionIcon('heroicon-m-shield-exclamation')
                ->color($this->getSecurityColor($auditStats['security_events'] ?? 0))
                ->chart($this->getSecurityChart()),

            // Permission Checks
            Stat::make('Permission Checks', $auditStats['permission_events'] ?? 0)
                ->description('Permission validation attempts')
                ->descriptionIcon('heroicon-m-key')
                ->color('success')
                ->chart($this->getPermissionChart()),

            // Role Changes
            Stat::make('Role Changes', $auditStats['role_events'] ?? 0)
                ->description('Role assignments and modifications')
                ->descriptionIcon('heroicon-m-user-group')
                ->color('info')
                ->chart($this->getRoleChart()),

            // Average Response Time
            Stat::make('Avg Response Time', $this->formatResponseTime($dashboardMetrics['request_metrics']['avg_response_time'] ?? 0))
                ->description('Authorization operations')
                ->descriptionIcon('heroicon-m-clock')
                ->color($this->getPerformanceColor($dashboardMetrics['request_metrics']['avg_response_time'] ?? 0))
                ->chart($this->getResponseTimeChart()),

            // Active Users
            Stat::make('Active Users', $auditStats['unique_users'] ?? 0)
                ->description('Users with activity today')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary')
                ->chart($this->getUserActivityChart()),

            // Success Rate
            Stat::make('Success Rate', $this->formatPercentage($dashboardMetrics['authorization_metrics']['success_rate'] ?? 100))
                ->description('Authorization success rate')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color($this->getSuccessRateColor($dashboardMetrics['authorization_metrics']['success_rate'] ?? 100))
                ->chart($this->getSuccessRateChart()),

            // Recent Alerts
            Stat::make('Recent Alerts', count($securityAlerts))
                ->description('Security alerts (7 days)')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($this->getAlertColor(count($securityAlerts)))
                ->chart($this->getAlertChart()),
        ];
    }

    protected function getColumns(): int
    {
        return 4;
    }

    /**
     * Get activity trend chart data.
     */
    private function getActivityChart(): array
    {
        return Cache::remember('widget_activity_chart', 300, function (): array {
            $data = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = now()->subDays($i)->format('Y-m-d');
                try {
                    $stats = $this->auditService->getAuditStatistics($date);
                    $data[] = $stats['total_activities'] ?? 0;
                } catch (Exception) {
                    $data[] = 0;
                }
            }

            return $data;
        });
    }

    /**
     * Get security events chart data.
     */
    private function getSecurityChart(): array
    {
        return Cache::remember('widget_security_chart', 300, function (): array {
            $data = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = now()->subDays($i)->format('Y-m-d');
                try {
                    $stats = $this->auditService->getAuditStatistics($date);
                    $data[] = $stats['security_events'] ?? 0;
                } catch (Exception) {
                    $data[] = 0;
                }
            }

            return $data;
        });
    }

    /**
     * Get permission checks chart data.
     */
    private function getPermissionChart(): array
    {
        return Cache::remember('widget_permission_chart', 300, function (): array {
            $data = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = now()->subDays($i)->format('Y-m-d');
                try {
                    $stats = $this->auditService->getAuditStatistics($date);
                    $data[] = $stats['permission_events'] ?? 0;
                } catch (Exception) {
                    $data[] = 0;
                }
            }

            return $data;
        });
    }

    /**
     * Get role changes chart data.
     */
    private function getRoleChart(): array
    {
        return Cache::remember('widget_role_chart', 300, function (): array {
            $data = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = now()->subDays($i)->format('Y-m-d');
                try {
                    $stats = $this->auditService->getAuditStatistics($date);
                    $data[] = $stats['role_events'] ?? 0;
                } catch (Exception) {
                    $data[] = 0;
                }
            }

            return $data;
        });
    }

    /**
     * Get response time chart data.
     */
    private function getResponseTimeChart(): array
    {
        return Cache::remember('widget_response_time_chart', 300, function (): array {
            $data = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = now()->subDays($i)->format('Y-m-d');
                try {
                    $metrics = $this->metricsService->getDashboardMetrics($date);
                    $data[] = $metrics['request_metrics']['avg_response_time'] ?? 0;
                } catch (Exception) {
                    $data[] = 0;
                }
            }

            return $data;
        });
    }

    /**
     * Get user activity chart data.
     */
    private function getUserActivityChart(): array
    {
        return Cache::remember('widget_user_activity_chart', 300, function (): array {
            $data = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = now()->subDays($i)->format('Y-m-d');
                try {
                    $stats = $this->auditService->getAuditStatistics($date);
                    $data[] = $stats['unique_users'] ?? 0;
                } catch (Exception) {
                    $data[] = 0;
                }
            }

            return $data;
        });
    }

    /**
     * Get success rate chart data.
     */
    private function getSuccessRateChart(): array
    {
        return Cache::remember('widget_success_rate_chart', 300, function (): array {
            $data = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = now()->subDays($i)->format('Y-m-d');
                try {
                    $metrics = $this->metricsService->getDashboardMetrics($date);
                    $data[] = $metrics['authorization_metrics']['success_rate'] ?? 100;
                } catch (Exception) {
                    $data[] = 100;
                }
            }

            return $data;
        });
    }

    /**
     * Get alert trend chart data.
     */
    private function getAlertChart(): array
    {
        return Cache::remember('widget_alert_chart', 300, function (): array {
            $data = [];
            for ($i = 6; $i >= 0; $i--) {
                $dateStart = now()->subDays($i)->startOfDay();
                $dateEnd = now()->subDays($i)->endOfDay();
                try {
                    // Get security alerts for the specific day
                    $alerts = $this->auditService->getSecurityAlerts(100); // Get more to filter by date
                    $dayAlerts = collect($alerts)->filter(function (array $alert) use ($dateStart, $dateEnd): bool {
                        $alertDate = new Carbon($alert['created_at']);

                        return $alertDate->between($dateStart, $dateEnd);
                    })->count();
                    $data[] = $dayAlerts;
                } catch (Exception) {
                    $data[] = 0;
                }
            }

            return $data;
        });
    }

    /**
     * Color determination methods.
     */
    private function getActivityColor(int $count): string
    {
        return match (true) {
            $count > 1000 => 'success',
            $count > 500 => 'warning',
            $count > 100 => 'info',
            default => 'gray',
        };
    }

    private function getSecurityColor(int $count): string
    {
        return match (true) {
            $count > 10 => 'danger',
            $count > 5 => 'warning',
            $count > 0 => 'info',
            default => 'success',
        };
    }

    private function getPerformanceColor(float $responseTime): string
    {
        return match (true) {
            $responseTime > 2000 => 'danger',
            $responseTime > 1000 => 'warning',
            $responseTime > 500 => 'info',
            default => 'success',
        };
    }

    private function getSuccessRateColor(float $rate): string
    {
        return match (true) {
            $rate >= 98 => 'success',
            $rate >= 95 => 'info',
            $rate >= 90 => 'warning',
            default => 'danger',
        };
    }

    private function getAlertColor(int $count): string
    {
        return match (true) {
            $count > 20 => 'danger',
            $count > 10 => 'warning',
            $count > 5 => 'info',
            default => 'success',
        };
    }

    /**
     * Formatting methods.
     */
    private function formatResponseTime(float $milliseconds): string
    {
        if ($milliseconds < 1000) {
            return number_format($milliseconds, 0).'ms';
        }

        return number_format($milliseconds / 1000, 2).'s';
    }

    private function formatPercentage(float $value): string
    {
        return number_format($value, 1).'%';
    }
}
