<?php

declare(strict_types=1);

namespace App\Filament\Resources\PlanResource\Pages;

use Filament\Actions;
use App\Filament\Resources\PlanResource;
use Filament\Resources\Pages\ViewRecord;

final class ViewPlan extends ViewRecord
{
    protected static string $resource = PlanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make()
                ->requiresConfirmation()
                ->before(function (Actions\DeleteAction $action): void {
                    if ($this->getRecord()->subscriptions()->exists()) {
                        $action->cancel();
                        $action->failureNotificationTitle('Cannot delete plan with active subscriptions');
                    }
                }),
        ];
    }
}
