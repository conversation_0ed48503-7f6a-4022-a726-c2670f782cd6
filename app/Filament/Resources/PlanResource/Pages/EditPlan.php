<?php

declare(strict_types=1);

namespace App\Filament\Resources\PlanResource\Pages;

use Filament\Actions;
use App\Filament\Resources\PlanResource;
use Filament\Resources\Pages\EditRecord;

final class EditPlan extends EditRecord
{
    protected static string $resource = PlanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make()
                ->requiresConfirmation()
                ->before(function (Actions\DeleteAction $action): void {
                    if ($this->getRecord()->subscriptions()->exists()) {
                        $action->cancel();
                        $action->failureNotificationTitle('Cannot delete plan with active subscriptions');
                    }
                }),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotificationTitle(): string
    {
        return 'Plan updated successfully';
    }
}
