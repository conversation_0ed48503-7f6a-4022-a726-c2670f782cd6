<?php

declare(strict_types=1);

namespace App\Filament\Resources\PlanResource\Pages;

use Filament\Actions;
use Illuminate\Support\Facades\Artisan;
use App\Filament\Resources\PlanResource;
use Filament\Resources\Pages\ListRecords;

final class ListPlans extends ListRecords
{
    protected static string $resource = PlanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Actions\Action::make('sync plans')
                ->action(function (): void {
                    $processors = config('payment.processors');
                    foreach ($processors as $processor => $config) {
                        ds($processor, $config);
                        if (data_get($config, 'enabled')) {
                            $command = 'plan:sync-'.$processor;
                            Artisan::call($command);
                        }
                    }
                }),
        ];
    }
}
