<?php

declare(strict_types=1);

namespace App\Filament\Resources\PermissionResource\Pages;

use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Spatie\Activitylog\Facades\LogActivity;
use App\Filament\Resources\PermissionResource;

final class CreatePermission extends CreateRecord
{
    protected static string $resource = PermissionResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotification(): Notification
    {
        return Notification::make()
            ->success()
            ->title('Permission created')
            ->body('The permission has been created successfully.');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ensure name is lowercase and properly formatted
        if (filled($data['name'])) {
            $data['name'] = mb_strtolower(str_replace(' ', '.', $data['name']));
        }

        return $data;
    }

    private function afterCreate(): void
    {
        // Log permission creation for audit purposes
        if (class_exists('\Spatie\Activitylog\Facades\LogActivity')) {
            LogActivity::performedOn($this->record)
                ->causedBy(auth()->user())
                ->log('Permission created');
        }
    }
}
