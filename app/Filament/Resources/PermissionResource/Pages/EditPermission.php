<?php

declare(strict_types=1);

namespace App\Filament\Resources\PermissionResource\Pages;

use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Spatie\Activitylog\Facades\LogActivity;
use App\Filament\Resources\PermissionResource;

final class EditPermission extends EditRecord
{
    protected static string $resource = PermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->icon('heroicon-o-eye'),
            Actions\DeleteAction::make()
                ->icon('heroicon-o-trash')
                ->requiresConfirmation()
                ->modalDescription('Are you sure you want to delete this permission? This action cannot be undone and will remove it from all roles.'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): Notification
    {
        return Notification::make()
            ->success()
            ->title('Permission updated')
            ->body('The permission has been updated successfully.');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Ensure name is lowercase and properly formatted
        if (filled($data['name'])) {
            $data['name'] = mb_strtolower(str_replace(' ', '.', $data['name']));
        }

        return $data;
    }

    private function afterSave(): void
    {
        // Log permission update for audit purposes
        if (class_exists('\Spatie\Activitylog\Facades\LogActivity')) {
            LogActivity::performedOn($this->record)
                ->causedBy(auth()->user())
                ->log('Permission updated');
        }
    }
}
