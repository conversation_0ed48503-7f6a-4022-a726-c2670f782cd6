<?php

declare(strict_types=1);

namespace App\Filament\Resources\PermissionResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Resources\PermissionResource;

final class ListPermissions extends ListRecords
{
    protected static string $resource = PermissionResource::class;

    public function getTitle(): string
    {
        return 'Permissions';
    }

    public function getSubheading(): string
    {
        return 'Manage system permissions and access controls';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->icon('heroicon-o-plus')
                ->label('Create Permission'),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // Add permission statistics widgets here if needed
        ];
    }
}
