<?php

declare(strict_types=1);

namespace App\Filament\Resources\PermissionResource\Pages;

use Filament\Actions;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Grid;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use App\Filament\Resources\PermissionResource;
use Filament\Infolists\Components\RepeatableEntry;

final class ViewPermission extends ViewRecord
{
    protected static string $resource = PermissionResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Permission Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Permission Name')
                                    ->copyable()
                                    ->badge()
                                    ->color('primary'),

                                TextEntry::make('display_name')
                                    ->label('Display Name'),

                                TextEntry::make('guard_name')
                                    ->label('Guard')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'web' => 'success',
                                        'api' => 'info',
                                        default => 'gray',
                                    }),

                                TextEntry::make('category')
                                    ->label('Category')
                                    ->badge()
                                    ->color(fn (?string $state): string => match ($state) {
                                        'feature' => 'primary',
                                        'resource' => 'success',
                                        'action' => 'warning',
                                        'system' => 'danger',
                                        default => 'gray',
                                    }),

                                TextEntry::make('level')
                                    ->label('Level')
                                    ->badge()
                                    ->color(fn (int $state): string => match ($state) {
                                        1 => 'primary',
                                        2 => 'success',
                                        3 => 'warning',
                                        default => 'gray',
                                    }),
                            ]),

                        TextEntry::make('description')
                            ->label('Description')
                            ->columnSpanFull(),
                    ]),

                Section::make('Assigned Roles')
                    ->schema([
                        RepeatableEntry::make('roles')
                            ->label('')
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Role')
                                    ->badge()
                                    ->color('info'),
                            ])
                            ->columns(3)
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                Section::make('Statistics')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('roles_count')
                                    ->label('Assigned to Roles')
                                    ->state(fn ($record) => $record->roles()->count())
                                    ->badge()
                                    ->color('info'),

                                TextEntry::make('created_at')
                                    ->label('Created')
                                    ->dateTime()
                                    ->since(),
                            ]),
                    ])
                    ->collapsible(),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->icon('heroicon-o-pencil'),
            Actions\DeleteAction::make()
                ->icon('heroicon-o-trash')
                ->requiresConfirmation(),
        ];
    }
}
