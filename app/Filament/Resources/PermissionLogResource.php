<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use Filament\Forms;
use App\Models\Role;
use App\Models\User;
use Filament\Tables;
use Filament\Forms\Form;
use App\Models\Permission;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Spatie\Activitylog\Models\Activity;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\PermissionLogResource\Pages;

final class PermissionLogResource extends Resource
{
    protected static ?string $model = Activity::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    protected static ?string $navigationLabel = 'Permission Logs';

    protected static ?string $modelLabel = 'Permission Log';

    protected static ?string $pluralModelLabel = 'Permission Logs';

    protected static ?string $navigationGroup = 'Security';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('log_name')
                    ->label('Log Name')
                    ->disabled(),

                Forms\Components\TextInput::make('description')
                    ->label('Description')
                    ->disabled(),

                Forms\Components\TextInput::make('subject_type')
                    ->label('Subject Type')
                    ->disabled(),

                Forms\Components\TextInput::make('subject_id')
                    ->label('Subject ID')
                    ->disabled(),

                Forms\Components\TextInput::make('causer_type')
                    ->label('Causer Type')
                    ->disabled(),

                Forms\Components\TextInput::make('causer_id')
                    ->label('Causer ID')
                    ->disabled(),

                Forms\Components\Textarea::make('properties')
                    ->label('Properties')
                    ->disabled()
                    ->rows(5),

                Forms\Components\DateTimePicker::make('created_at')
                    ->label('Created At')
                    ->disabled(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('log_name')
                    ->label('Log Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'default' => 'primary',
                        'permission' => 'success',
                        'role' => 'info',
                        'security' => 'warning',
                        default => 'gray',
                    })
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('description')
                    ->label('Action')
                    ->searchable()
                    ->sortable()
                    ->limit(50),

                Tables\Columns\TextColumn::make('subject_type')
                    ->label('Subject')
                    ->formatStateUsing(fn (?string $state): string => $state !== null && $state !== '' && $state !== '0' ? class_basename($state) : 'N/A')
                    ->badge()
                    ->color('secondary')
                    ->sortable(),

                Tables\Columns\TextColumn::make('causer.name')
                    ->label('User')
                    ->default('System')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Date')
                    ->dateTime()
                    ->sortable()
                    ->since(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('log_name')
                    ->label('Log Type')
                    ->options([
                        'default' => 'Default',
                        'permission' => 'Permission',
                        'role' => 'Role',
                        'security' => 'Security',
                    ]),

                Tables\Filters\SelectFilter::make('subject_type')
                    ->label('Subject Type')
                    ->options([
                        Role::class => 'Role',
                        Permission::class => 'Permission',
                        User::class => 'User',
                    ]),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Created from'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Created until'),
                    ])
                    ->query(fn (Builder $query, array $data): Builder => $query
                        ->when(
                            $data['created_from'],
                            fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                        )
                        ->when(
                            $data['created_until'],
                            fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                        )),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->icon('heroicon-o-eye'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([]),
            ])
            ->defaultSort('created_at', 'desc')
            ->poll('30s'); // Auto-refresh every 30 seconds
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPermissionLogs::route('/'),
            'view' => Pages\ViewPermissionLog::route('/{record}'),
        ];
    }

    public static function canCreate(): bool
    {
        return false; // Logs are created automatically, not manually
    }

    public static function canEdit($record): bool
    {
        return false; // Logs should not be editable
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereIn('log_name', ['default', 'permission', 'role', 'security'])
            ->orWhereIn('subject_type', [
                Role::class,
                Permission::class,
                User::class,
            ]);
    }
}
