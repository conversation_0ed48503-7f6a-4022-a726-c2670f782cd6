<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use Filament\Forms;
use App\Models\Plan;
use App\Models\User;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Repeater;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\PlanResource\Pages;
use Illuminate\Container\Attributes\CurrentUser;

final class PlanResource extends Resource
{
    protected static ?string $model = Plan::class;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';

    protected static ?string $navigationGroup = 'Subscription Management';

    protected static ?int $navigationSort = 1;

    public static function getNavigationBadge(): string
    {
        return (string) Plan::query()->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Plan Information')
                    ->description('Configure the basic details and pricing for this subscription plan.')
                    ->schema([
                        TextInput::make('key')
                            ->label('Plan Key')
                            ->unique(ignoreRecord: true)
                            ->helperText('Unique key for identifying this plan (used for subscription synchronization).')
                            ->columnSpan(2),

                        TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->helperText('A descriptive name for the subscription plan.')
                            ->columnSpan(2),

                        Forms\Components\Textarea::make('description')
                            ->maxLength(65535)
                            ->rows(3)
                            ->helperText('Detailed description of what this plan includes.')
                            ->columnSpanFull()
                            ->nullable(),

                        TextInput::make('price')
                            ->required()
                            ->numeric()
                            ->prefix('$')
                            ->minValue(0)
                            ->step(0.01)
                            ->helperText('The monthly subscription price.')
                            ->columnSpan(1),

                        Forms\Components\Select::make('currency')
                            ->required()
                            ->options([
                                'USD' => 'US Dollar ($)',
                                'EUR' => 'Euro (€)',
                                'GBP' => 'British Pound (£)',
                                'CAD' => 'Canadian Dollar (C$)',
                                'AUD' => 'Australian Dollar (A$)',
                            ])
                            ->default('USD')
                            ->helperText('The currency for this plan.')
                            ->columnSpan(1),

                        TextInput::make('processor_plan_id')
                            ->label('Plan ID')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->helperText('The processor billing plan ID for integration.')
                            ->columnSpan(2),
                        TextInput::make('processor_price_id')
                            ->label('Plan Product ID')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->helperText('The processor billing product ID for integration.')
                            ->columnSpan(2),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Status')
                    ->description('Control whether this plan is available for new subscriptions.')
                    ->schema([
                        Forms\Components\Toggle::make('active')
                            ->required()
                            ->default(true)
                            ->helperText('Inactive plans cannot be subscribed to but existing subscriptions remain active.'),
                    ]),

                Forms\Components\Section::make('Features')
                    ->description('Select the features included in this subscription plan.')
                    ->schema([
                        Repeater::make('features')
                            ->relationship('features')
                            ->schema([
                                TextInput::make('name')->required(),
                                TextInput::make('value')
                                    ->numeric()
                                    ->nullable(),
                            ])
                            ->columns(2),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('key')
                    ->label('Key')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('Plan key copied')
                    ->copyMessageDuration(1500)
                    ->toggleable(),

                TextColumn::make('processor_type')
                    ->label('Processor')
                    ->formatStateUsing(fn (string $state): string => str($state)->title()->toString())
                    ->badge()
                    ->searchable()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight(fn (
                        Plan $record,
                        #[CurrentUser] User $user
                    ): string => ($user && $user->getCurrentPlanKey() === $record->key) ? 'bold' : 'normal')
                    ->color(fn (
                        Plan $record,
                        #[CurrentUser] User $user
                    ): string => ($user && $user->getCurrentPlanKey() === $record->key) ? 'primary' : 'gray')
                    ->formatStateUsing(function (string $state, Plan $record, #[CurrentUser] User $user): string {
                        if ($user && $user->getCurrentPlanKey() === $record->key) {
                            return $state.' ⭐';
                        }

                        return $state;
                    }),

                TextColumn::make('description')
                    ->limit(50)
                    ->searchable()
                    ->toggleable(),

                TextColumn::make('price')
                    ->formatStateUsing(fn ($record, float $state): string => $record->getCurrencySymbol().number_format($state / 100, 2))
                    ->label('Price')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('currency')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('processor_plan_id')
                    ->label('Platform Plan ID')
                    ->searchable()
                    ->formatStateUsing(fn (string $state): string => str($state)->mask('*', 4)->toString())
                    ->copyable()
                    ->copyMessage('Plan ID copied')
                    ->copyMessageDuration(1500)
                    ->toggleable(),

                Tables\Columns\IconColumn::make('active')
                    ->boolean()
                    ->sortable()
                    ->label('Active?'),

                TextColumn::make('features_count')
                    ->label('Features')
                    ->counts('features')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('subscriptions_count')
                    ->label('Subscriptions')
                    ->counts('subscriptions')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('current_status')
                    ->badge()
                    ->label('Status')
                    ->colors([
                        'success' => 'current',
                        'secondary' => 'active',
                        'danger' => 'inactive',
                    ])
                    ->getStateUsing(function (Plan $record): string {
                        $user = Auth::user();
                        if ($user && $user->getCurrentPlanKey() === $record->key) {
                            return 'Current';
                        }

                        return $record->active ? 'Active' : 'Inactive';
                    }),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('active')
                    ->options([
                        '1' => 'Active',
                        '0' => 'Inactive',
                    ])
                    ->label('Status'),

                Tables\Filters\Filter::make('price_range')
                    ->form([
                        TextInput::make('min_price')
                            ->numeric()
                            ->prefix('$')
                            ->minValue(0)
                            ->step(0.01)
                            ->placeholder('Min price'),

                        TextInput::make('max_price')
                            ->numeric()
                            ->prefix('$')
                            ->minValue(0)
                            ->step(0.01)
                            ->placeholder('Max price'),
                    ])
                    ->query(fn (Builder $query, array $data): Builder => $query
                        ->when(
                            $data['min_price'] ?? null,
                            fn (Builder $query, $price): Builder => $query->where('price', '>=', $price),
                        )
                        ->when(
                            $data['max_price'] ?? null,
                            fn (Builder $query, $price): Builder => $query->where('price', '<=', $price),
                        )),

                Tables\Filters\SelectFilter::make('currency')
                    ->options([
                        'USD' => 'US Dollar ($)',
                        'EUR' => 'Euro (€)',
                        'GBP' => 'British Pound (£)',
                        'CAD' => 'Canadian Dollar (C$)',
                        'AUD' => 'Australian Dollar (A$)',
                    ])
                    ->label('Currency'),

                Tables\Filters\SelectFilter::make('current_plan')
                    ->label('Current Plan')
                    ->options([
                        'current' => 'My Current Plan',
                        'other' => 'Other Plans',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        /** @var User|null $user */
                        $user = Auth::user();
                        if (! $user || blank($data['value']) || ! method_exists($user, 'subscriptions')) {
                            return $query;
                        }

                        $currentPlanKey = $user->getCurrentPlanKey();
                        if (! $currentPlanKey) {
                            return $query;
                        }

                        return match ($data['value']) {
                            'current' => $query->where('key', $currentPlanKey),
                            'other' => $query->where('key', '!=', $currentPlanKey),
                            default => $query,
                        };
                    }),
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->requiresConfirmation()
                        ->before(function (Tables\Actions\DeleteAction $action, Plan $record): void {
                            if ($record->subscriptions()->exists()) {
                                $action->cancel();
                                $action->failureNotificationTitle('Cannot delete plan with active subscriptions');
                            }
                        }),
                ]),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate Plans')
                        ->icon('heroicon-o-check-circle')
                        ->action(fn ($records) => $records->each->update(['active' => true]))
                        ->deselectRecordsAfterCompletion()
                        ->successNotificationTitle('Plans activated successfully'),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate Plans')
                        ->icon('heroicon-o-x-circle')
                        ->action(fn ($records) => $records->each->update(['active' => false]))
                        ->deselectRecordsAfterCompletion()
                        ->successNotificationTitle('Plans deactivated successfully'),

                    Tables\Actions\BulkAction::make('delete')
                        ->label('Delete Plans')
                        ->icon('heroicon-o-trash')
                        ->action(function ($records): void {
                            $recordsWithSubscriptions = $records->filter(fn ($record) => $record->subscriptions()->exists());

                            if ($recordsWithSubscriptions->count() > 0) {
                                Notification::make()
                                    ->title('Cannot delete plans with active subscriptions')
                                    ->danger()
                                    ->send();

                                return;
                            }

                            $records->each->delete();
                            Notification::make()
                                ->title('Plans deleted successfully')
                                ->success()
                                ->send();
                        })
                        ->requiresConfirmation()
                        ->deselectRecordsAfterCompletion()
                        ->successNotificationTitle('Plans deleted successfully'),
                ]),
            ])
            ->defaultSort('name');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPlans::route('/'),
            'create' => Pages\CreatePlan::route('/create'),
            'view' => Pages\ViewPlan::route('/{record}'),
            'edit' => Pages\EditPlan::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['key', 'name', 'description', 'plan_id'];
    }

    public static function getGlobalSearchResultDetails($record): array
    {
        return [
            'Price' => $record->formatted_price,
            'Status' => $record->active ? 'Active' : 'Inactive',
            'Features' => $record->features_count,
        ];
    }
}
