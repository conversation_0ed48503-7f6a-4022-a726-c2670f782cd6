<?php

declare(strict_types=1);

namespace App\Filament\Resources\PermissionLogResource\Pages;

use Filament\Actions;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Grid;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\KeyValueEntry;
use App\Filament\Resources\PermissionLogResource;

final class ViewPermissionLog extends ViewRecord
{
    protected static string $resource = PermissionLogResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Log Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('log_name')
                                    ->label('Log Type')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'default' => 'primary',
                                        'permission' => 'success',
                                        'role' => 'info',
                                        'security' => 'warning',
                                        default => 'gray',
                                    }),

                                TextEntry::make('description')
                                    ->label('Action Description'),

                                TextEntry::make('created_at')
                                    ->label('Timestamp')
                                    ->dateTime()
                                    ->since(),
                            ]),
                    ]),

                Section::make('Subject Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('subject_type')
                                    ->label('Subject Type')
                                    ->formatStateUsing(fn (?string $state): string => $state !== null && $state !== '' && $state !== '0' ? class_basename($state) : 'N/A')
                                    ->badge()
                                    ->color('secondary'),

                                TextEntry::make('subject_id')
                                    ->label('Subject ID'),
                            ]),

                        TextEntry::make('subject')
                            ->label('Subject Details')
                            ->formatStateUsing(function ($record) {
                                if ($record->subject) {
                                    $subject = $record->subject;
                                    if (method_exists($subject, 'name')) {
                                        return $subject->name;
                                    }

                                    if (method_exists($subject, 'title')) {
                                        return $subject->title;
                                    }

                                    return class_basename($subject).' #'.$subject->id;
                                }

                                return 'Subject no longer exists';
                            })
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                Section::make('Causer Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('causer_type')
                                    ->label('Causer Type')
                                    ->formatStateUsing(fn (?string $state): string => $state !== null && $state !== '' && $state !== '0' ? class_basename($state) : 'System')
                                    ->badge()
                                    ->color('info'),

                                TextEntry::make('causer_id')
                                    ->label('Causer ID'),
                            ]),

                        TextEntry::make('causer')
                            ->label('Causer Details')
                            ->formatStateUsing(function ($record) {
                                if ($record->causer) {
                                    $causer = $record->causer;
                                    if (method_exists($causer, 'name')) {
                                        return $causer->name;
                                    }

                                    if (method_exists($causer, 'email')) {
                                        return $causer->email;
                                    }

                                    return class_basename($causer).' #'.$causer->id;
                                }

                                return 'System';
                            })
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                Section::make('Additional Properties')
                    ->schema([
                        KeyValueEntry::make('properties')
                            ->label('')
                            ->keyLabel('Property')
                            ->valueLabel('Value')
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->icon('heroicon-o-trash')
                ->requiresConfirmation()
                ->modalDescription('Are you sure you want to delete this log entry? This action cannot be undone.'),
        ];
    }
}
