<?php

declare(strict_types=1);

namespace App\Filament\Resources\PermissionLogResource\Pages;

use App\Models\Role;
use App\Models\User;
use Filament\Actions;
use App\Models\Permission;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\PermissionLogResource;

final class ListPermissionLogs extends ListRecords
{
    protected static string $resource = PermissionLogResource::class;

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Logs')
                ->badge(fn () => $this->getModel()::count()),

            'permissions' => Tab::make('Permissions')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('subject_type', Permission::class))
                ->badge(fn () => $this->getModel()::where('subject_type', Permission::class)->count()),

            'roles' => Tab::make('Roles')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('subject_type', Role::class))
                ->badge(fn () => $this->getModel()::where('subject_type', Role::class)->count()),

            'users' => Tab::make('Users')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('subject_type', User::class))
                ->badge(fn () => $this->getModel()::where('subject_type', User::class)->count()),

            'recent' => Tab::make('Recent (24h)')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('created_at', '>=', now()->subDay()))
                ->badge(fn () => $this->getModel()::where('created_at', '>=', now()->subDay())->count()),
        ];
    }

    public function getTitle(): string
    {
        return 'Permission Logs';
    }

    public function getSubheading(): string
    {
        return 'View authorization and security audit logs';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('refresh')
                ->label('Refresh')
                ->icon('heroicon-o-arrow-path'),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // Add audit statistics widgets here if needed
        ];
    }
}
