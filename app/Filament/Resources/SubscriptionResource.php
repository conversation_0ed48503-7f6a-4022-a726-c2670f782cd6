<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\Subscription;
use Filament\Resources\Resource;
use App\Enums\SubscriptionStatus;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\SubscriptionResource\Pages;

final class SubscriptionResource extends Resource
{
    protected static ?string $model = Subscription::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Subscription Management';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['billable', 'plan']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('user')
                    ->getStateUsing(fn (Subscription $record) => $record->billable->name),
                TextColumn::make('plan')
                    ->getStateUsing(fn (Subscription $record) => $record->plan->name),
                TextColumn::make('status')
                    ->formatStateUsing(fn (Subscription $record) => $record->status->title()),
                TextColumn::make('trial_ends_at')
                    ->hidden(fn (?Subscription $record): bool => $record?->status === SubscriptionStatus::TRIALING ?? true)
                    ->formatStateUsing(fn (Subscription $record) => $record->trial_ends_at->diffForHumans()),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubscriptions::route('/'),
            'create' => Pages\CreateSubscription::route('/create'),
            'edit' => Pages\EditSubscription::route('/{record}/edit'),
        ];
    }
}
