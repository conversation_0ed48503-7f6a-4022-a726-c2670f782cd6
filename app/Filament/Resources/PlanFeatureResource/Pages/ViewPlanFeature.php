<?php

declare(strict_types=1);

namespace App\Filament\Resources\PlanFeatureResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use App\Filament\Resources\PlanFeatureResource;

final class ViewPlanFeature extends ViewRecord
{
    protected static string $resource = PlanFeatureResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make()
                ->requiresConfirmation()
                ->before(function (Actions\DeleteAction $action): void {
                    if ($this->getRecord()->plans()->exists()) {
                        $action->cancel();
                        $action->failureNotificationTitle('Cannot delete feature that is used by plans');
                    }
                }),
        ];
    }
}
