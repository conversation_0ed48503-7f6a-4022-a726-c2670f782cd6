<?php

declare(strict_types=1);

namespace App\Filament\Resources\PlanFeatureResource\Pages;

use Filament\Resources\Pages\CreateRecord;
use App\Filament\Resources\PlanFeatureResource;

final class CreatePlanFeature extends CreateRecord
{
    protected static string $resource = PlanFeatureResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotificationTitle(): string
    {
        return 'Plan feature created successfully';
    }
}
