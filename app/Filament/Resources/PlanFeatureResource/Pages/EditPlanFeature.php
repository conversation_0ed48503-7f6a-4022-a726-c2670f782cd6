<?php

declare(strict_types=1);

namespace App\Filament\Resources\PlanFeatureResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Resources\PlanFeatureResource;

final class EditPlanFeature extends EditRecord
{
    protected static string $resource = PlanFeatureResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make()
                ->requiresConfirmation()
                ->before(function (Actions\DeleteAction $action): void {
                    if ($this->getRecord()->plans()->exists()) {
                        $action->cancel();
                        $action->failureNotificationTitle('Cannot delete feature that is used by plans');
                    }
                }),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotificationTitle(): string
    {
        return 'Plan feature updated successfully';
    }
}
