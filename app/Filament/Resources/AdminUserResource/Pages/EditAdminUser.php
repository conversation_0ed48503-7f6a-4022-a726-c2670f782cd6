<?php

declare(strict_types=1);

namespace App\Filament\Resources\AdminUserResource\Pages;

use Filament\Actions;
use Illuminate\Support\Facades\Auth;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Resources\AdminUserResource;

final class EditAdminUser extends EditRecord
{
    protected static string $resource = AdminUserResource::class;

    public function getTitle(): string
    {
        return 'Edit Admin User';
    }

    public function getHeading(): string
    {
        return 'Edit: '.($this->record->name ?? '');
    }

    public function getSubheading(): string
    {
        return 'Modify administrative user details and permissions';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make()
                ->visible(fn () => Auth::user()?->hasRole('super_admin'))
                ->requiresConfirmation()
                ->modalHeading('Delete Admin User')
                ->modalDescription('Are you sure you want to delete this administrative user? This action cannot be undone.')
                ->before(function () {
                    if ($this->record->id === Auth::id()) {
                        Notification::make()
                            ->title('Cannot delete yourself')
                            ->body('You cannot delete your own account.')
                            ->danger()
                            ->send();
                        $this->halt();

                        return false;
                    }
                }),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }

    protected function getSavedNotification(): Notification
    {
        return Notification::make()
            ->success()
            ->title('Admin user updated')
            ->body('The administrative user has been updated successfully.');
    }

    protected function authorizeAccess(): void
    {
        abort_unless(Auth::user()?->hasRole('super_admin'), 403, 'You do not have permission to edit admin users.');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Remove password field if it's empty to avoid overwriting existing password
        if (empty($data['password'])) {
            unset($data['password']);
        }

        return $data;
    }
}
