<?php

declare(strict_types=1);

namespace App\Filament\Resources\AdminUserResource\Pages;

use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Resources\AdminUserResource;

final class CreateAdminUser extends CreateRecord
{
    protected static string $resource = AdminUserResource::class;

    public function getTitle(): string
    {
        return 'Create Admin User';
    }

    public function getHeading(): string
    {
        return 'Create Administrative User';
    }

    public function getSubheading(): string
    {
        return 'Add a new administrator or super administrator to the system';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotification(): Notification
    {
        return Notification::make()
            ->success()
            ->title('Admin user created')
            ->body('The administrative user has been created successfully.');
    }
}
