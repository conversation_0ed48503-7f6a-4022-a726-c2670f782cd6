<?php

declare(strict_types=1);

namespace App\Filament\Resources\AdminUserResource\Pages;

use Filament\Actions;
use Illuminate\Support\Facades\Auth;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Resources\AdminUserResource;

final class ListAdminUsers extends ListRecords
{
    protected static string $resource = AdminUserResource::class;

    public function getTitle(): string
    {
        return 'Admin Users';
    }

    public function getHeading(): string
    {
        return 'Administrative Users';
    }

    public function getSubheading(): string
    {
        return 'Manage system administrators and super administrators';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Create Admin User')
                ->icon('heroicon-o-plus')
                ->visible(fn () => Auth::user()?->hasRole('super_admin')),
        ];
    }
}
