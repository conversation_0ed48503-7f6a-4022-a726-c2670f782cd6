<?php

declare(strict_types=1);

namespace App\Filament\Resources\AdminUserResource\Pages;

use Filament\Actions;
use Filament\Infolists\Infolist;
use Illuminate\Support\Facades\Auth;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\TextEntry;
use App\Filament\Resources\AdminUserResource;

final class ViewAdminUser extends ViewRecord
{
    protected static string $resource = AdminUserResource::class;

    public function getTitle(): string
    {
        return 'View Admin User';
    }

    public function getHeading(): string
    {
        return $this->record->name;
    }

    public function getSubheading(): string
    {
        return 'Administrative user details';
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('User Information')
                    ->schema([
                        TextEntry::make('name')
                            ->label('Full Name')
                            ->icon('heroicon-o-user'),
                        TextEntry::make('email')
                            ->label('Email Address')
                            ->icon('heroicon-o-envelope')
                            ->copyable(),
                        IconEntry::make('email_verified_at')
                            ->label('Email Verified')
                            ->boolean()
                            ->trueIcon('heroicon-o-check-circle')
                            ->falseIcon('heroicon-o-x-circle')
                            ->trueColor('success')
                            ->falseColor('danger'),
                    ])
                    ->columns(2),

                Section::make('Administrative Roles')
                    ->schema([
                        TextEntry::make('roles.name')
                            ->label('Assigned Roles')
                            ->badge()
                            ->color(fn ($state): string => match ($state) {
                                'super_admin' => 'danger',
                                'admin' => 'warning',
                                default => 'gray'
                            })
                            ->icon(fn ($state): ?string => match ($state) {
                                'super_admin' => 'heroicon-o-star',
                                'admin' => 'heroicon-o-shield-check',
                                default => null
                            })
                            ->formatStateUsing(fn ($state) => match ($state) {
                                'super_admin' => 'Super Admin',
                                'admin' => 'Admin',
                                default => $state
                            }),
                    ]),

                Section::make('Account Details')
                    ->schema([
                        TextEntry::make('created_at')
                            ->label('Account Created')
                            ->dateTime('F j, Y \\a\\t g:i A')
                            ->icon('heroicon-o-calendar'),
                        TextEntry::make('updated_at')
                            ->label('Last Updated')
                            ->dateTime('F j, Y \\a\\t g:i A')
                            ->icon('heroicon-o-clock'),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(fn () => Auth::user()?->hasRole('super_admin')),
            Actions\DeleteAction::make()
                ->visible(fn () => Auth::user()?->hasRole('super_admin'))
                ->requiresConfirmation()
                ->modalHeading('Delete Admin User')
                ->modalDescription('Are you sure you want to delete this administrative user? This action cannot be undone.')
                ->before(function () {
                    if ($this->record->id === Auth::id()) {
                        $this->halt();

                        return false;
                    }
                }),
        ];
    }
}
