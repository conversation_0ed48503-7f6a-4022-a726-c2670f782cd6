<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use Filament\Forms;
use App\Models\Role;
use App\Models\User;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Forms\Components\CheckboxList;
use App\Filament\Resources\UserResource\Pages;
use STS\FilamentImpersonate\Tables\Actions\Impersonate;

final class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'Administration';

    protected static ?int $navigationSort = 1;

    /**
     * Get the list of roles that should be excluded from user management.
     *
     * These roles are typically system-level roles that should not be
     * assigned or managed through the standard user interface.
     *
     * @return array<string> Array of role names to exclude
     */
    public static function getExcludedRoles(): array
    {
        return config('roles.protected', ['admin', 'super_admin']);
    }

    /**
     * Base query for the resource with role exclusion applied globally.
     *
     * Excludes users who have any of the protected roles to prevent
     * accidental modification of system-level accounts.
     */
    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with('currentTeam')
            ->whereDoesntHave('roles', function (Builder $query): void {
                $query->whereIn('name', self::getExcludedRoles());
            });
    }

    /**
     * Get the navigation badge count for the Users resource.
     *
     * Returns the count of users using the same filtered query as the resource listing
     * to maintain consistency and avoid duplication of filtering logic.
     *
     * @return string The count as a string
     */
    public static function getNavigationBadge(): string
    {
        return (string) self::getEloquentQuery()->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('User Information')
                    ->description('Basic user account information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Enter full name'),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->placeholder('<EMAIL>'),
                        Forms\Components\TextInput::make('password')
                            ->password()
                            ->required(fn (string $context): bool => $context === 'create')
                            ->minLength(8)
                            ->placeholder('Min. 8 characters')
                            ->dehydrated(fn ($state): bool => filled($state))
                            ->helperText('Leave blank to keep current password when editing')
                            ->same('password_confirmation')
                            ->validationAttribute('password'),
                        Forms\Components\TextInput::make('password_confirmation')
                            ->password()
                            ->required(fn (string $context): bool => $context === 'create')
                            ->minLength(8)
                            ->placeholder('Confirm password')
                            ->dehydrated(false)
                            ->helperText('Must match the password above'),
                    ])
                    ->columns(2),

                Section::make('Authorization')
                    ->description('Manage user roles and permissions')
                    ->schema([
                        Select::make('roles')
                            ->label('Roles')
                            ->multiple()
                            ->relationship('roles', 'name')
                            ->options(Role::query()->whereNotIn('name', self::getExcludedRoles())->pluck('name', 'id'))
                            ->preload()
                            ->searchable()
                            ->helperText('Select one or more roles for this user')
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                Section::make('Subscription Management')
                    ->description('View and manage user subscription details')
                    ->schema([
                        Forms\Components\Placeholder::make('current_plan')
                            ->label('Current Plan')
                            ->content(fn (User $record): string => $record?->getCurrentPlanName() ?? 'No Plan')
                            ->columnSpan(1),

                        Forms\Components\Placeholder::make('subscription_status')
                            ->label('Subscription Status')
                            ->content(fn (User $record): string => $record?->getSubscriptionStatus()?->value ?? 'Unknown')
                            ->columnSpan(1),

                        Forms\Components\Placeholder::make('trial_status')
                            ->label('Trial Status')
                            ->content(fn (User $record): string => $record?->isOnActiveTrial() ? 'Active Trial' : 'No Trial')
                            ->columnSpan(1),

                        Forms\Components\Placeholder::make('trial_end_date')
                            ->label('Trial End Date')
                            ->content(fn (?User $record): string => $record?->getTrialEndDate()?->format('M j, Y g:i A') ?? 'N/A')
                            ->columnSpan(1),

                        Forms\Components\Placeholder::make('stripe_id')
                            ->label('Stripe Customer ID')
                            ->content(fn (User $record): string => $record?->stripe_id ?? 'Not Set')
                            ->columnSpan(2)
                            ->visible(fn (User $record): bool => ! empty($record?->stripe_id)),
                    ])
                    ->columns(2)
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->with(['subscriptions', 'customer']))
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),

                TextColumn::make('email')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->icon('heroicon-m-envelope'),

                TextColumn::make('roles.name')
                    ->badge()
                    ->label('Roles')
                    ->separator(', ')
                    ->colors([
                        'primary' => 'admin',
                        'success' => 'moderator',
                        'warning' => 'editor',
                        'secondary' => 'user',
                    ])
                    ->searchable(),

                TextColumn::make('roles_count')
                    ->label('Role Count')
                    ->counts('roles')
                    ->badge()
                    ->color('info')
                    ->toggleable(),

                Tables\Columns\IconColumn::make('email_verified_at')
                    ->label('Verified')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-badge')
                    ->falseIcon('heroicon-o-x-mark')
                    ->trueColor('success')
                    ->falseColor('danger'),

                TextColumn::make('current_plan')
                    ->label('Plan')
                    ->getStateUsing(fn (User $record): ?string => $record->getCurrentPlanName())
                    ->badge()
                    ->color('primary')
                    ->sortable(false),

                TextColumn::make('subscription_status')
                    ->label('Status')
                    ->getStateUsing(fn (User $record): string => str($record->getSubscriptionStatus()?->value)->headline()->toString())
                    ->badge()
                    ->color(fn (User $record): string => $record->getSubscriptionStatusColor())
                    ->sortable(false),

                Tables\Columns\IconColumn::make('trial_status')
                    ->label('Trial')
                    ->getStateUsing(fn (User $record): bool => $record->isOnActiveTrial())
                    ->boolean()
                    ->sortable(false),

                TextColumn::make('trial_end_date')
                    ->label('Trial Ends')
                    ->getStateUsing(fn (User $record): ?string => $record->getTrialEndDate()?->format('M j, Y'))
                    ->placeholder('N/A')
                    ->sortable(false),

                TextColumn::make('user_type')
                    ->label('User Type')
                    ->getStateUsing(fn (User $record): string => $record->getUserType())
                    ->badge()
                    ->color(fn (User $record): string => match ($record->getUserType()) {
                        'admin' => 'danger',
                        'customer' => 'success',
                        'user' => 'gray',
                        default => 'gray',
                    })
                    ->sortable(false),

                TextColumn::make('watchlists_count')
                    ->counts('watchlists')
                    ->label('Watchlists'),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                SelectFilter::make('roles')
                    ->relationship('roles', 'name')
                    ->multiple()
                    ->preload(),

                TernaryFilter::make('email_verified_at')
                    ->label('Email Verified')
                    ->nullable()
                    ->trueLabel('Verified')
                    ->falseLabel('Unverified')
                    ->queries(
                        true: fn (Builder $query) => $query->whereNotNull('email_verified_at'),
                        false: fn (Builder $query) => $query->whereNull('email_verified_at'),
                        blank: fn (Builder $query): Builder => $query,
                    ),

                SelectFilter::make('subscription_status')
                    ->label('Subscription Status')
                    ->options([
                        'active' => 'Active',
                        'trial' => 'Trial',
                        'canceled' => 'Canceled',
                        'expired' => 'Expired',
                        'none' => 'No Subscription',
                    ])
                    ->query(fn (Builder $query, array $data): Builder => $query->when(
                        $data['value'] ?? null,
                        fn (Builder $query, string $status) => $query->whereSubscriptionStatus($status)
                    )),

                TernaryFilter::make('trial_status')
                    ->label('Trial Status')
                    ->nullable()
                    ->trueLabel('Active Trial')
                    ->falseLabel('No Trial')
                    ->queries(
                        true: fn (Builder $query) => $query->where('trial_ends_at', '>', now()),
                        false: fn (Builder $query) => $query->where(function (Builder $q): void {
                            $q->whereNull('trial_ends_at')
                                ->orWhere('trial_ends_at', '<=', now());
                        }),
                        blank: fn (Builder $query): Builder => $query,
                    ),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from'),
                        Forms\Components\DatePicker::make('created_until'),
                    ])
                    ->query(
                        fn (Builder $query, array $data): Builder => $query
                            ->when(
                                $data['created_from'] ?? null,
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'] ?? null,
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            )
                    ),
            ])
            ->filtersFormColumns(2)
            ->actions([
                Impersonate::make()
                    ->icon('heroicon-o-user-circle')
                    ->color('success')
                    ->visible(fn () => Auth::user()?->canImpersonate())
                    ->disabled(fn (User $record): bool => $record->getSubscriptionStatus() === 'inactive')
                    ->requiresConfirmation()
                    ->modalHeading('Impersonate User')
                    ->modalDescription(fn (User $record): string => "You are about to impersonate {$record->name}. This will log you in as them and redirect you to the dashboard.")
                    ->guard('web')
                    ->redirectTo(fn (): string => config('filament.default_redirect_after_impersonation', route('dashboard'))),
                Tables\Actions\Action::make('manage_roles')
                    ->label('Manage Roles')
                    ->icon('heroicon-o-user-group')
                    ->color('info')
                    ->visible(fn (User $record): bool => ! $record->roles()->whereIn('name', self::getExcludedRoles())->exists())
                    ->form([
                        CheckboxList::make('roles')
                            ->label('Roles')
                            ->options(Role::query()->whereNotIn('name', self::getExcludedRoles())->pluck('name', 'id'))
                            ->default(fn ($record) => $record->roles->whereNotIn('name', self::getExcludedRoles())->pluck('id')->toArray())
                            ->columns(2),
                    ])
                    ->action(function (array $data, User $record): void {
                        // Only sync roles that are not excluded
                        $allowedRoleIds = Role::query()->whereNotIn('name', self::getExcludedRoles())->pluck('id')->toArray();
                        $rolesToSync = array_intersect($data['roles'], $allowedRoleIds);

                        // Keep existing excluded roles and sync only allowed roles
                        $excludedRoleIds = $record->roles()->whereIn('name', self::getExcludedRoles())->pluck('id')->toArray();
                        $finalRoles = array_merge($excludedRoleIds, $rolesToSync);

                        // Cast all IDs to integers and remove duplicates
                        $finalRoles = array_unique(array_map('intval', $finalRoles));

                        $record->roles()->sync($finalRoles);
                    })
                    ->successNotificationTitle('Roles updated successfully'),
                ActionGroup::make([
                    Tables\Actions\ViewAction::make('view'),
                    Tables\Actions\EditAction::make('edit'),
                    // ->visible(fn (User $record): bool => ! $record->roles()->whereIn('name', self::getExcludedRoles())->exists()),
                    Tables\Actions\DeleteAction::make('delete'),
                    // ->visible(fn (User $record): bool => ! $record->roles()->whereIn('name', self::getExcludedRoles())->exists()),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
