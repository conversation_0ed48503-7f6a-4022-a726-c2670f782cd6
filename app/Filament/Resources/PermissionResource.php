<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Spatie\Permission\Models\Permission;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\PermissionResource\Pages;

final class PermissionResource extends Resource
{
    protected static ?string $model = Permission::class;

    protected static ?string $navigationIcon = 'heroicon-o-key';

    protected static ?string $navigationGroup = 'Authorization';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(255)
                    ->helperText('Unique permission identifier (e.g., users.create, posts.edit)')
                    ->placeholder('resource.action'),

                Forms\Components\Select::make('guard_name')
                    ->required()
                    ->options([
                        'web' => 'Web',
                        'api' => 'API',
                    ])
                    ->default('web')
                    ->helperText('Guard that this permission applies to'),

                // Forms\Components\Select::make('category')
                //     ->options([
                //         'feature' => 'Feature Access',
                //         'resource' => 'Resource Management',
                //         'action' => 'Specific Actions',
                //         'system' => 'System Administration',
                //     ])
                //     ->helperText('Permission category for organization'),

                // Forms\Components\TextInput::make('level')
                //     ->numeric()
                //     ->default(1)
                //     ->minValue(1)
                //     ->maxValue(3)
                //     ->helperText('Permission level (1=Feature, 2=Resource, 3=Action)'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('guard_name')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'web' => 'success',
                        'api' => 'info',
                        default => 'gray',
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('category')
                    ->badge()
                    ->color(fn (?string $state): string => match ($state) {
                        'feature' => 'primary',
                        'resource' => 'success',
                        'action' => 'warning',
                        'system' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('level')
                    ->numeric()
                    ->sortable()
                    ->badge()
                    ->color(fn (int $state): string => match ($state) {
                        1 => 'primary',
                        2 => 'success',
                        3 => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('roles_count')
                    ->counts('roles')
                    ->label('Roles')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('guard_name')
                    ->options([
                        'web' => 'Web',
                        'api' => 'API',
                    ])
                    ->multiple(),

                Tables\Filters\SelectFilter::make('category')
                    ->options([
                        'feature' => 'Feature Access',
                        'resource' => 'Resource Management',
                        'action' => 'Specific Actions',
                        'system' => 'System Administration',
                    ])
                    ->multiple(),

                Tables\Filters\SelectFilter::make('level')
                    ->options([
                        1 => 'Level 1 (Feature)',
                        2 => 'Level 2 (Resource)',
                        3 => 'Level 3 (Action)',
                    ])
                    ->multiple(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->modalDescription('Are you sure you want to delete this permission? This action cannot be undone and will remove it from all roles.'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation(),
                ]),
            ])
            ->defaultSort('name');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPermissions::route('/'),
            'create' => Pages\CreatePermission::route('/create'),
            'view' => Pages\ViewPermission::route('/{record}'),
            'edit' => Pages\EditPermission::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): string
    {
        return (string) self::getModel()::count();
    }

    public static function getGlobalSearchEloquentQuery(): Builder
    {
        return parent::getGlobalSearchEloquentQuery()->with(['roles']);
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name'];
    }

    public static function getGlobalSearchResultDetails($record): array
    {
        return [
            'Guard' => $record->guard_name,
            'Category' => $record->category ?? 'Uncategorized',
            'Roles' => $record->roles->count(),
        ];
    }
}
