<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use Filament\Forms;
use App\Models\Role;
use App\Models\User;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Filament\Clusters\Users;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Filters\TernaryFilter;
use App\Filament\Resources\AdminUserResource\Pages;
use STS\FilamentImpersonate\Tables\Actions\Impersonate;

final class AdminUserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationGroup = 'Administration';

    protected static ?string $navigationLabel = 'Admin Users';

    protected static ?string $modelLabel = 'Admin User';

    protected static ?string $pluralModelLabel = 'Admin Users';

    protected static ?int $navigationSort = 1;

    public static function canAccess(): bool
    {
        $user = Auth::user();

        return $user?->hasRole('super_admin') || ($user?->hasRole('admin') && $user?->hasPermissionTo('admin.access')) ?? false;
    }

    public static function canCreate(): bool
    {
        return Auth::user()?->hasRole('super_admin') ?? false;
    }

    public static function canEdit($record): bool
    {
        return Auth::user()?->hasRole('super_admin') ?? false;
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['currentTeam', 'roles'])
            ->whereHas('roles', function (Builder $query): void {
                $query->whereIn('name', ['admin', 'super_admin']);
            });
    }

    public static function getNavigationBadge(): string
    {
        /** @var int $count */
        $count = self::getModel()::whereHas('roles', function (Builder $query): void {
            $query->whereIn('name', ['admin', 'super_admin']);
        })->count();

        return (string) $count;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Admin User Information')
                    ->description('Administrative user account details')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Enter full name'),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->placeholder('<EMAIL>'),
                        Forms\Components\TextInput::make('password')
                            ->password()
                            ->required(fn (string $context): bool => $context === 'create')
                            ->minLength(8)
                            ->placeholder('Min. 8 characters')
                            ->dehydrateStateUsing(fn ($state) => $state ? Hash::make($state) : null)
                            ->dehydrated(fn ($state): bool => filled($state))
                            ->helperText('Leave blank to keep current password when editing'),
                    ])
                    ->columns(2),

                Section::make('Administrative Roles')
                    ->description('Assign administrative privileges')
                    ->schema([
                        Select::make('roles')
                            ->label('Administrative Role')
                            ->multiple()
                            ->relationship('roles', 'name')
                            ->options(Role::query()->whereIn('name', ['admin', 'super_admin'])->pluck('name', 'id'))
                            ->preload()
                            ->searchable()
                            ->helperText('Super Admin has full system access, Admin has limited administrative access')
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                Section::make('Account Status')
                    ->description('Administrative account management')
                    ->schema([
                        Forms\Components\Toggle::make('email_verified_at')
                            ->label('Email Verified')
                            ->helperText('Mark email as verified')
                            ->dehydrateStateUsing(fn ($state): ?\Carbon\CarbonInterface => $state ? now() : null)
                            ->formatStateUsing(fn ($state): bool => ! is_null($state)),

                        Forms\Components\DateTimePicker::make('created_at')
                            ->label('Account Created')
                            ->disabled()
                            ->visibleOn('edit'),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Name')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),

                TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->copyMessage('Email copied to clipboard'),

                TextColumn::make('roles.name')
                    ->badge()
                    ->label('Role')
                    ->colors([
                        'danger' => 'super_admin',
                        'warning' => 'admin',
                    ])
                    ->icons([
                        'heroicon-o-star' => 'super_admin',
                        'heroicon-o-shield-check' => 'admin',
                    ])
                    ->formatStateUsing(fn ($state) => match ($state) {
                        'super_admin' => 'Super Admin',
                        'admin' => 'Admin',
                        default => $state
                    }),

                TextColumn::make('email_verified_at')
                    ->label('Email Status')
                    ->badge()
                    ->color(fn ($state): string => $state ? 'success' : 'danger')
                    ->formatStateUsing(fn ($state): string => $state ? 'Verified' : 'Unverified')
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('updated_at')
                    ->label('Last Updated')
                    ->dateTime('M j, Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('roles')
                    ->label('Administrative Role')
                    ->relationship('roles', 'name')
                    ->options([
                        'super_admin' => 'Super Admin',
                        'admin' => 'Admin',
                    ])
                    ->multiple(),

                TernaryFilter::make('email_verified_at')
                    ->label('Email Verified')
                    ->nullable(),
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\ViewAction::make()
                        ->color('info'),

                    Tables\Actions\EditAction::make()
                        ->color('warning')
                        ->visible(fn () => Auth::user()?->hasRole('super_admin')),

                    Impersonate::make()
                        ->label('Impersonate')
                        ->icon('heroicon-o-user-circle')
                        ->color('success')
                        ->visible(fn () => Auth::user()?->hasRole(['admin', 'super_admin']))
                        ->requiresConfirmation()
                        ->modalHeading('Impersonate User')
                        ->modalDescription('You will be logged in as this user. Use with caution.')
                        ->guard('web')
                        ->redirectTo('/'),

                    Tables\Actions\DeleteAction::make()
                        ->visible(fn () => Auth::user()?->hasRole('super_admin'))
                        ->requiresConfirmation()
                        ->modalHeading('Delete Admin User')
                        ->modalDescription('Are you sure you want to delete this administrative user? This action cannot be undone.')
                        ->before(function (User $record) {
                            if ($record->id === Auth::id()) {
                                Notification::make()
                                    ->title('Cannot delete yourself')
                                    ->body('You cannot delete your own account.')
                                    ->danger()
                                    ->send();

                                return false;
                            }
                        }),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn () => Auth::user()?->hasRole('super_admin'))
                        ->requiresConfirmation()
                        ->modalHeading('Delete Selected Admin Users')
                        ->modalDescription('Are you sure you want to delete the selected administrative users? This action cannot be undone.')
                        ->before(function ($records) {
                            // Count selected users who are Super Admins
                            $selectedSuperAdminCount = $records->filter(fn ($user) => $user->hasRole('super_admin'))->count();

                            // Get total Super Admin count in the system
                            $totalSuperAdminCount = User::query()->whereHas('roles', function ($query): void {
                                $query->where('name', 'super_admin');
                            })->count();

                            // Check if deleting selected users would remove all Super Admins
                            if ($selectedSuperAdminCount > 0 && $selectedSuperAdminCount >= $totalSuperAdminCount) {
                                Notification::make()
                                    ->title('Cannot delete all Super Admins')
                                    ->body('You cannot delete all Super Admin users. At least one Super Admin must remain to preserve administrative access.')
                                    ->danger()
                                    ->send();

                                return false;
                            }
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->striped()
            ->paginated([10, 25, 50]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAdminUsers::route('/'),
            'create' => Pages\CreateAdminUser::route('/create'),
            'view' => Pages\ViewAdminUser::route('/{record}'),
            'edit' => Pages\EditAdminUser::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): string
    {
        return 'Administration';
    }

    public static function shouldRegisterNavigation(): bool
    {
        return Auth::user()?->hasRole(['super_admin', 'admin']) ?? false;
    }
}
