<?php

declare(strict_types=1);

namespace App\Filament\Resources\RoleResource\Pages;

use Filament\Actions;
use App\Filament\Resources\RoleResource;
use Filament\Resources\Pages\ListRecords;

final class ListRoles extends ListRecords
{
    protected static string $resource = RoleResource::class;

    public function getTitle(): string
    {
        return 'Roles & Permissions';
    }

    public function getSubheading(): string
    {
        return 'Manage system roles and their permission assignments';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->icon('heroicon-o-plus')
                ->label('Create Role'),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // Add role statistics widgets here if needed
        ];
    }
}
