<?php

declare(strict_types=1);

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Spatie\Activitylog\Facades\LogActivity;

final class CreateRole extends CreateRecord
{
    protected static string $resource = RoleResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotification(): Notification
    {
        return Notification::make()
            ->success()
            ->title('Role created')
            ->body('The role has been created successfully.');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ensure name is lowercase and has no spaces
        if (filled($data['name'])) {
            $data['name'] = mb_strtolower(str_replace(' ', '_', $data['name']));
        }

        return $data;
    }

    private function afterCreate(): void
    {
        // Log role creation for audit purposes
        if (class_exists('\Spatie\Activitylog\Facades\LogActivity')) {
            LogActivity::performedOn($this->record)
                ->causedBy(auth()->user())
                ->log('Role created');
        }
    }
}
