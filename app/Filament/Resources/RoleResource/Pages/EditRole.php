<?php

declare(strict_types=1);

namespace App\Filament\Resources\RoleResource\Pages;

use Filament\Actions;
use App\Filament\Resources\RoleResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Spatie\Activitylog\Facades\LogActivity;

final class EditRole extends EditRecord
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->icon('heroicon-o-eye'),
            Actions\DeleteAction::make()
                ->icon('heroicon-o-trash')
                ->requiresConfirmation()
                ->modalDescription('Are you sure you want to delete this role? This action cannot be undone and will remove all user assignments.'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): Notification
    {
        return Notification::make()
            ->success()
            ->title('Role updated')
            ->body('The role has been updated successfully.');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Ensure name is lowercase and has no spaces
        if (filled($data['name'])) {
            $data['name'] = mb_strtolower(str_replace(' ', '_', $data['name']));
        }

        return $data;
    }

    private function afterSave(): void
    {
        // Log role update for audit purposes
        if (class_exists('\Spatie\Activitylog\Facades\LogActivity')) {
            LogActivity::performedOn($this->record)
                ->causedBy(auth()->user())
                ->log('Role updated');
        }
    }
}
