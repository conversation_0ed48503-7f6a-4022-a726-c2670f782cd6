<?php

declare(strict_types=1);

namespace App\Filament\Resources\RoleResource\Pages;

use Filament\Actions;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Grid;
use App\Filament\Resources\RoleResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\RepeatableEntry;

final class ViewRole extends ViewRecord
{
    protected static string $resource = RoleResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Role Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Role Name')
                                    ->copyable()
                                    ->badge()
                                    ->color('primary'),

                                TextEntry::make('display_name')
                                    ->label('Display Name'),

                                TextEntry::make('level')
                                    ->label('Hierarchy Level')
                                    ->badge()
                                    ->color(fn (int $state): string => match (true) {
                                        $state >= 90 => 'danger',
                                        $state >= 70 => 'warning',
                                        $state >= 50 => 'success',
                                        default => 'gray',
                                    }),

                                IconEntry::make('is_active')
                                    ->label('Status')
                                    ->boolean()
                                    ->trueIcon('heroicon-o-check-circle')
                                    ->falseIcon('heroicon-o-x-circle')
                                    ->trueColor('success')
                                    ->falseColor('danger'),
                            ]),

                        TextEntry::make('description')
                            ->label('Description')
                            ->columnSpanFull(),
                    ]),

                Section::make('Permissions')
                    ->schema([
                        RepeatableEntry::make('permissions')
                            ->label('')
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Permission')
                                    ->badge()
                                    ->color('info'),
                            ])
                            ->columns(3)
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                Section::make('Statistics')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('permissions_count')
                                    ->label('Total Permissions')
                                    ->state(fn ($record) => $record->permissions()->count())
                                    ->badge()
                                    ->color('info'),

                                TextEntry::make('users_count')
                                    ->label('Assigned Users')
                                    ->state(fn ($record) => $record->users()->count())
                                    ->badge()
                                    ->color('primary'),

                                TextEntry::make('created_at')
                                    ->label('Created')
                                    ->dateTime()
                                    ->since(),
                            ]),
                    ])
                    ->collapsible(),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->icon('heroicon-o-pencil'),
            Actions\DeleteAction::make()
                ->icon('heroicon-o-trash')
                ->requiresConfirmation(),
        ];
    }
}
