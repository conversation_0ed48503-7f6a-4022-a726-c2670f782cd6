<?php

declare(strict_types=1);

namespace App\Filament\Resources\UserResource\Pages;

use Filament\Actions;
use App\Filament\Resources\UserResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Database\Eloquent\Builder;

final class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    /**
     * Apply role exclusion filter specifically for the index listing.
     * This prevents users with admin/super_admin roles from appearing in the list.
     */
    private function modifyQueryUsing(Builder $query): Builder
    {
        return $query->whereDoesntHave('roles', function (Builder $query): void {
            $query->whereIn('name', UserResource::getExcludedRoles());
        });
    }
}
