<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\PlanFeature;
use Illuminate\Support\Str;
use Illuminate\Support\Number;
use Filament\Resources\Resource;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\PlanFeatureResource\Pages;

final class PlanFeatureResource extends Resource
{
    protected static ?string $model = PlanFeature::class;

    protected static ?string $navigationIcon = 'heroicon-o-puzzle-piece';

    protected static ?string $navigationGroup = 'Subscription Management';

    protected static ?int $navigationSort = 2;

    public static function getNavigationBadge(): string
    {
        return (string) PlanFeature::query()->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Feature Information')
                    ->description('Define the basic details and identifier for this plan feature.')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->helperText('A user-friendly name for this feature.')
                            ->columnSpanFull(),

                        Forms\Components\TextInput::make('feature_key')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->helperText('A unique identifier (e.g., "api_calls_limit", "storage_gb"). Will be slugified automatically.')
                            ->columnSpan(1)
                            ->afterStateUpdated(fn ($state, callable $set) => $set('feature_key', Str::slug($state, '_'))),

                        Forms\Components\Textarea::make('description')
                            ->required()
                            ->maxLength(65535)
                            ->rows(3)
                            ->helperText('Detailed description of what this feature provides.')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Configuration')
                    ->description('Configure the value and availability of this feature.')
                    ->schema([
                        Forms\Components\Toggle::make('active')
                            ->required()
                            ->default(true)
                            ->helperText('Inactive features will not be available in new plans but remain assigned to existing plans.'),
                    ])
                    ->columns(1),

                Forms\Components\Section::make('Information')
                    ->description('Important notes about plan features')
                    ->schema([
                        Forms\Components\Placeholder::make('note')
                            ->content('Plan features are separate from Laravel Pennant app features. These are subscription plan capabilities that can be assigned to different pricing tiers.')
                            ->columnSpanFull(),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('feature_key')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('Feature key copied')
                    ->copyMessageDuration(1500)
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('value')
                    ->limit(30)
                    ->searchable()
                    ->toggleable()
                    ->formatStateUsing(function ($state) {
                        if (is_numeric($state)) {
                            return Number::format((float) $state, maxPrecision: 2);
                        }

                        if (is_bool($state)) {
                            return $state ? 'Yes' : 'No';
                        }

                        return Str::limit($state, 30);
                    }),

                Tables\Columns\IconColumn::make('active')
                    ->boolean()
                    ->sortable()
                    ->label('Status'),

                Tables\Columns\TextColumn::make('plans_count')
                    ->label('Used in Plans')
                    ->counts('plans')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('active')
                    ->options([
                        '1' => 'Active',
                        '0' => 'Inactive',
                    ])
                    ->label('Status'),

                // Tables\Filters\SelectFilter::make('value_type')
                //     ->options([
                //         'numeric' => 'Numeric',
                //         'boolean' => 'Boolean',
                //         'text' => 'Text',
                //     ])
                //     ->label('Value Type')
                //     ->query(function (Builder $query, array $data): Builder {
                //         if (blank($data['value_type'])) {
                //             return $query;
                //         }

                //         $driver = config('database.default');
                //         $connection = config("database.connections.{$driver}.driver");

                //         return match ($data['value_type']) {
                //             'numeric' => match ($connection) {
                //                 'sqlite' => $query->whereRaw("CASE WHEN NULLIF(value, '') IS NULL THEN 0 ELSE value END GLOB '[0-9]*'"),
                //                 'pgsql' => $query->whereRaw("value ~ '^[0-9]+(\\.[0-9]+)?$'"),
                //                 default => $query->where('value', 'REGEXP', '^[0-9]+(\.[0-9]+)?$'),
                //             },
                //             'boolean' => $query->whereIn('value', ['true', 'false', '1', '0', true, false]),
                //             'text' => match ($connection) {
                //                 'sqlite' => $query->whereRaw("value NOT GLOB '[0-9]*'"),
                //                 'pgsql' => $query->whereRaw("value !~ '^[0-9]+(\\.[0-9]+)?$'"),
                //                 default => $query->where('value', 'REGEXP', '^[^0-9].*$'),
                //             },
                //             default => $query,
                //         };
                //     }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('managePlans')
                    ->label('Manage Plans')
                    ->icon('heroicon-o-credit-card')
                    ->url(fn (PlanFeature $record): string => PlanResource::getUrl())
                    ->color('gray'),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->before(function (Tables\Actions\DeleteAction $action, PlanFeature $record): void {
                        if ($record->plans()->exists()) {
                            $action->cancel();
                            $action->failureNotificationTitle('Cannot delete feature that is used by plans');
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate Features')
                        ->icon('heroicon-o-check-circle')
                        ->action(fn ($records) => $records->each->update(['active' => true]))
                        ->deselectRecordsAfterCompletion()
                        ->successNotificationTitle('Features activated successfully'),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate Features')
                        ->icon('heroicon-o-x-circle')
                        ->action(fn ($records) => $records->each->update(['active' => false]))
                        ->deselectRecordsAfterCompletion()
                        ->successNotificationTitle('Features deactivated successfully'),

                    Tables\Actions\BulkAction::make('delete')
                        ->label('Delete Features')
                        ->icon('heroicon-o-trash')
                        ->action(function ($records): void {
                            $recordsUsedInPlans = $records->filter(fn ($record) => $record->plans()->exists());

                            if ($recordsUsedInPlans->count() > 0) {
                                Notification::make()
                                    ->title('Cannot delete features that are used by plans')
                                    ->danger()
                                    ->send();

                                return;
                            }

                            $records->each->delete();
                            Notification::make()
                                ->title('Features deleted successfully')
                                ->success()
                                ->send();
                        })
                        ->requiresConfirmation()
                        ->deselectRecordsAfterCompletion()
                        ->successNotificationTitle('Features deleted successfully'),
                ]),
            ])
            ->defaultSort('name');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPlanFeatures::route('/'),
            'create' => Pages\CreatePlanFeature::route('/create'),
            'view' => Pages\ViewPlanFeature::route('/{record}'),
            'edit' => Pages\EditPlanFeature::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name', 'feature_key', 'description'];
    }

    public static function getGlobalSearchResultDetails($record): array
    {
        return [
            'Key' => $record->feature_key,
            'Value' => $record->value,
            'Status' => $record->active ? 'Active' : 'Inactive',
            'Used in Plans' => $record->plans_count,
        ];
    }
}
