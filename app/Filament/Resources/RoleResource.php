<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use Filament\Forms;
use App\Models\Role;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\RoleResource\Pages;

final class RoleResource extends Resource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationGroup = 'Authorization';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(255)
                    ->helperText('Unique role identifier (lowercase, no spaces)'),

                // Forms\Components\TextInput::make('level')
                //     ->numeric()
                //     ->required()
                //     ->default(50)
                //     ->minValue(1)
                //     ->maxValue(100)
                //     ->helperText('Role hierarchy level (1-100, higher = more permissions)'),

                Forms\Components\CheckboxList::make('permissions')
                    // ->multiple()
                    ->relationship('permissions', 'name')
                    // ->preload()
                    ->searchable()
                    ->columns(3)
                    ->helperText('Permissions assigned to this role'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('display_name')
                    ->searchable()
                    ->sortable(),

                //     Tables\Columns\TextColumn::make('level')
                //         ->numeric()
                //         ->sortable()
                //         ->badge()
                //         ->color(fn (int $state): string => match (true) {
                //             $state >= 90 => 'danger',
                //             $state >= 70 => 'warning',
                //             $state >= 50 => 'success',
                //             default => 'gray',
                //         }),

                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('permissions_count')
                    ->counts('permissions')
                    ->label('Permissions')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('users_count')
                    ->counts('users')
                    ->label('Users')
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->boolean()
                    ->trueLabel('Active roles only')
                    ->falseLabel('Inactive roles only')
                    ->native(false),

                Tables\Filters\Filter::make('level_range')
                    ->form([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('level_from')
                                    ->numeric()
                                    ->placeholder('Min level'),
                                Forms\Components\TextInput::make('level_to')
                                    ->numeric()
                                    ->placeholder('Max level'),
                            ]),
                    ])
                    ->query(fn (Builder $query, array $data): Builder => $query
                        ->when(
                            $data['level_from'],
                            fn (Builder $query, $level): Builder => $query->where('level', '>=', $level),
                        )
                        ->when(
                            $data['level_to'],
                            fn (Builder $query, $level): Builder => $query->where('level', '<=', $level),
                        )),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->modalDescription('Are you sure you want to delete this role? This action cannot be undone and will remove all user assignments.'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation(),
                ]),
            ])
            ->defaultSort('name', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'view' => Pages\ViewRole::route('/{record}'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): string
    {
        return (string) self::getModel()::count();
    }

    public static function getGlobalSearchEloquentQuery(): Builder
    {
        return parent::getGlobalSearchEloquentQuery()->with(['permissions']);
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name', 'description'];
    }

    public static function getGlobalSearchResultDetails($record): array
    {
        return [
            'Level' => $record->level,
            'Permissions' => $record->permissions->count(),
            'Status' => $record->is_active ? 'Active' : 'Inactive',
        ];
    }
}
