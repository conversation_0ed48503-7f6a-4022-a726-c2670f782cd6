<?php

declare(strict_types=1);

namespace App\Facades;

use App\Services\AuthorizationService;
use Illuminate\Support\Facades\Facade;

/**
 * Authorization Facade
 *
 * @method static bool can(\App\Models\User $user, string $permission, mixed $resource = null)
 * @method static bool cannot(\App\Models\User $user, string $permission, mixed $resource = null)
 * @method static bool canAny(\App\Models\User $user, array $permissions, mixed $resource = null)
 * @method static bool canAll(\App\Models\User $user, array $permissions, mixed $resource = null)
 * @method static array<string> getUserPermissions(\App\Models\User $user)
 * @method static array<string> getUserRoles(\App\Models\User $user)
 * @method static array<string> getSubscriptionPermissions(\App\Models\User $user)
 * @method static array<string> getRolePermissions(\App\Models\User $user)
 * @method static bool hasRole(\App\Models\User $user, string $role)
 * @method static bool hasAnyRole(\App\Models\User $user, array $roles)
 * @method static bool hasAllRoles(\App\Models\User $user, array $roles)
 * @method static bool hasSubscriptionFeature(\App\Models\User $user, string $feature)
 * @method static bool isOnPlan(\App\Models\User $user, string $planId)
 * @method static bool isSuperAdmin(\App\Models\User $user)
 * @method static void clearUserCache(\App\Models\User $user)
 * @method static void clearAllCache()
 *
 * @see AuthorizationService
 */
final class Authorization extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'authorization';
    }
}
