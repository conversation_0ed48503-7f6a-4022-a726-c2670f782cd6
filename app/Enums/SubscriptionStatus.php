<?php

declare(strict_types=1);

namespace App\Enums;

enum SubscriptionStatus: string
{
    case INACTIVE = 'inactive';
    case ACTIVE = 'active';
    case CANCELLED = 'cancelled';
    case PAST_DUE = 'past_due';
    case UNPAID = 'unpaid';
    case INCOMPLETE = 'incomplete';
    case COMPLETED = 'completed';
    case INCOMPLETE_EXPIRED = 'incomplete_expired';
    case TRIALING = 'trialing';
    case PAUSED = 'paused';
    case PENDING = 'pending';
    case FAILED = 'failed';
    case EXPIRED = 'expired';
    case SUSPENDED = 'suspended';
    case UNKNOWN = 'unknown';

    public function title(): string
    {
        return match ($this) {
            self::INACTIVE => 'Inactive',
            self::ACTIVE => 'Active',
            self::CANCELLED => 'Cancelled',
            self::PAST_DUE => 'Past Due',
            self::UNPAID => 'Unpaid',
            self::INCOMPLETE => 'Incomplete',
            self::COMPLETED => 'Completed',
            self::INCOMPLETE_EXPIRED => 'Incomplete Expired',
            self::TRIALING => 'Trialing',
            self::PAUSED => 'Paused',
            self::PENDING => 'Pending',
            self::FAILED => 'Failed',
            self::EXPIRED => 'Expired',
            self::SUSPENDED => 'Suspended',
            self::UNKNOWN => 'Unknown',
        };
    }
}
