<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Inertia\Middleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use App\Services\PaymentProcessor\PaymentProcessorFactory;

final class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {

        $paymentProcessorFactory = resolve(PaymentProcessorFactory::class);
        $enabledProcessors = $paymentProcessorFactory->getEnabledProcessors();
        $paymentSettings = [];
        foreach ($enabledProcessors as $processor) {
            $sandbox = config("payment.processors.{$processor}.sandbox_mode", true);
            $token = config("payment.processors.{$processor}.client_token", true);

            $paymentSettings[$processor] = [
                'sandboxMode' => $sandbox,
                'clientToken' => $token,
            ];
        }

        /** @var array<string, mixed> */
        return array_merge(parent::share($request), [
            'env' => [
                // 'paddleClientToken' => config('payment.processors.paddle.client_token', ''),
                ...$paymentSettings,
            ],
            'name' => Config::get('app.name', 'Kaikyo'),
            'flash' => [
                'message' => fn () => $request->session()->get('message'),
                'success' => fn () => $request->session()->get('success'),
                'error' => fn () => $request->session()->get('error'),
            ],
            'impersonation' => $this->getImpersonationData($request),
        ]);
    }

    /**
     * Get impersonation data for the frontend
     *
     * @return array<string, mixed>
     */
    private function getImpersonationData(Request $request): array
    {
        // Check if the 'impersonate' binding exists in the app container
        if (! app()->bound('impersonate')) {
            return ['isImpersonating' => false];
        }

        $impersonateManager = app('impersonate');

        // Verify that the retrieved object has the isImpersonating() method
        if (! method_exists($impersonateManager, 'isImpersonating')) {
            return ['isImpersonating' => false];
        }

        if (! $impersonateManager->isImpersonating()) {
            return ['isImpersonating' => false];
        }

        $currentUser = $request->user();
        $originalUser = $request->session()->get('impersonate.original_user');

        return [
            'isImpersonating' => true,
            'impersonatedUser' => $currentUser ? [
                'id' => $currentUser->id,
                'name' => $currentUser->name,
                'email' => $currentUser->email,
            ] : null,
            'originalUser' => $originalUser ? [
                'id' => $originalUser->id,
                'name' => $originalUser->name,
                'email' => $originalUser->email,
            ] : null,
            'returnUrl' => config('filament-impersonate.return_to_admin', '/admin'),
        ];
    }
}
