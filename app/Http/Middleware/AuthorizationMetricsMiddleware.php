<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Services\PerformanceMetricsService;
use Symfony\Component\HttpFoundation\Response;

final readonly class AuthorizationMetricsMiddleware
{
    public function __construct(
        private PerformanceMetricsService $metricsService
    ) {}

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);

        // Track request start
        $requestId = uniqid('req_', true);
        $user = Auth::user();

        // Add request context
        $request->attributes->set('metrics_request_id', $requestId);
        $request->attributes->set('metrics_start_time', $startTime);

        try {
            $response = $next($request);

            // Calculate metrics
            $endTime = microtime(true);
            $endMemory = memory_get_usage(true);
            $responseTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
            $memoryUsage = $endMemory - $startMemory;

            // Record metrics
            $this->recordMetrics($request, $response, [
                'request_id' => $requestId,
                'response_time' => $responseTime,
                'memory_usage' => $memoryUsage,
                'user_id' => $user?->id,
                'route' => $request->route()?->getName(),
                'method' => $request->method(),
                'status_code' => $response->getStatusCode(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return $response;

        } catch (Exception $exception) {
            // Record error metrics
            $endTime = microtime(true);
            $responseTime = ($endTime - $startTime) * 1000;

            $this->recordErrorMetrics($request, $exception, [
                'request_id' => $requestId,
                'response_time' => $responseTime,
                'user_id' => $user?->id,
                'error_type' => $exception::class,
                'error_message' => $exception->getMessage(),
            ]);

            throw $exception;
        }
    }

    /**
     * Record successful request metrics.
     */
    private function recordMetrics(Request $request, Response $response, array $data): void
    {
        try {
            // Record general performance metrics
            $this->metricsService->recordRequestMetrics(
                route: $data['route'] ?? 'unknown',
                method: $data['method'],
                responseTime: $data['response_time'],
                memoryUsage: $data['memory_usage'],
                statusCode: $data['status_code'],
                userId: $data['user_id']
            );

            // Record authorization-specific metrics if applicable
            if ($this->isAuthorizationRoute($request)) {
                $this->metricsService->recordAuthorizationMetrics(
                    action: $this->getAuthorizationAction($request),
                    responseTime: $data['response_time'],
                    successful: $response->isSuccessful(),
                    userId: $data['user_id']
                );
            }

            // Check for performance anomalies
            $this->checkPerformanceAnomalies($data);

        } catch (Exception $exception) {
            Log::error('Failed to record metrics', [
                'error' => $exception->getMessage(),
                'request_data' => $data,
            ]);
        }
    }

    /**
     * Record error metrics.
     */
    private function recordErrorMetrics(Request $request, Exception $exception, array $data): void
    {
        try {
            $this->metricsService->recordErrorMetrics(
                route: $request->route()?->getName() ?? 'unknown',
                method: $data['method'] ?? $request->method(),
                errorType: $data['error_type'],
                errorMessage: $data['error_message'],
                responseTime: $data['response_time'],
                userId: $data['user_id']
            );

        } catch (Exception $e) {
            Log::error('Failed to record error metrics', [
                'original_error' => $exception->getMessage(),
                'metrics_error' => $e->getMessage(),
                'request_data' => $data,
            ]);
        }
    }

    /**
     * Check if the current route is authorization-related.
     */
    private function isAuthorizationRoute(Request $request): bool
    {
        $route = $request->route();
        if (! $route) {
            return false;
        }

        $routeName = $route->getName() ?? '';
        $uri = $request->getPathInfo();

        // Check for authorization-related routes
        $authPatterns = [
            '/login',
            '/logout',
            '/register',
            '/password',
            '/admin/roles',
            '/admin/permissions',
            '/admin/users',
            'filament.admin.auth',
            'filament.admin.resources.roles',
            'filament.admin.resources.permissions',
            'filament.admin.resources.users',
        ];

        foreach ($authPatterns as $pattern) {
            if (str_contains($routeName, $pattern) || str_contains($uri, $pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get the authorization action from the request.
     */
    private function getAuthorizationAction(Request $request): string
    {
        $request->route();
        $method = $request->method();
        $uri = $request->getPathInfo();

        // Determine action based on route and method
        if (str_contains($uri, '/login')) {
            return 'login';
        }

        if (str_contains($uri, '/logout')) {
            return 'logout';
        }

        if (str_contains($uri, '/register')) {
            return 'register';
        }

        if (str_contains($uri, '/roles')) {
            return match ($method) {
                'GET' => 'view_roles',
                'POST' => 'create_role',
                'PUT', 'PATCH' => 'update_role',
                'DELETE' => 'delete_role',
                default => 'manage_roles',
            };
        }

        if (str_contains($uri, '/permissions')) {
            return match ($method) {
                'GET' => 'view_permissions',
                'POST' => 'create_permission',
                'PUT', 'PATCH' => 'update_permission',
                'DELETE' => 'delete_permission',
                default => 'manage_permissions',
            };
        }

        if (str_contains($uri, '/users')) {
            return match ($method) {
                'GET' => 'view_users',
                'POST' => 'create_user',
                'PUT', 'PATCH' => 'update_user',
                'DELETE' => 'delete_user',
                default => 'manage_users',
            };
        }

        return 'authorization_action';
    }

    /**
     * Check for performance anomalies and log them.
     */
    private function checkPerformanceAnomalies(array $data): void
    {
        $thresholds = [
            'response_time' => 5000, // 5 seconds
            'memory_usage' => 50 * 1024 * 1024, // 50MB
        ];

        $anomalies = [];

        if ($data['response_time'] > $thresholds['response_time']) {
            $anomalies[] = "Slow response time: {$data['response_time']}ms";
        }

        if ($data['memory_usage'] > $thresholds['memory_usage']) {
            $memoryMB = round($data['memory_usage'] / 1024 / 1024, 2);
            $anomalies[] = "High memory usage: {$memoryMB}MB";
        }

        if ($anomalies !== []) {
            Log::warning('Performance anomaly detected', [
                'anomalies' => $anomalies,
                'request_data' => $data,
                'thresholds' => $thresholds,
            ]);

            // Record as security event if severe
            if ($data['response_time'] > $thresholds['response_time'] * 2) {
                $this->metricsService->recordSecurityEvent(
                    event: 'performance_anomaly',
                    severity: 'high',
                    context: [
                        'anomalies' => $anomalies,
                        'response_time' => $data['response_time'],
                        'memory_usage' => $data['memory_usage'],
                    ],
                    userId: $data['user_id']
                );
            }
        }
    }
}
