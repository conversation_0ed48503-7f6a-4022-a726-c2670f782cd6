<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Services\PerformanceMetricsService;

final readonly class PerformanceMonitoring
{
    public function __construct(
        private PerformanceMetricsService $performanceMetricsService
    ) {}

    /**
     * Handle an incoming request and monitor performance metrics.
     */
    public function handle(Request $request, Closure $next): mixed
    {
        // Capture start time and memory usage
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);

        // Proceed with the request
        $response = $next($request);

        // Calculate performance metrics
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);

        $durationMs = ($endTime - $startTime) * 1000; // Convert to milliseconds
        $memoryDelta = $endMemory - $startMemory;

        // Get route information
        $route = $request->route()?->getName() ?? $request->path();
        $method = $request->method();
        $statusCode = $response instanceof Response ? $response->getStatusCode() : 200;
        $userId = Auth::id();

        // Record metrics with error handling to ensure minimal overhead
        try {
            $this->performanceMetricsService->recordRequestMetrics(
                route: $route,
                method: $method,
                responseTime: $durationMs,
                memoryUsage: $memoryDelta,
                statusCode: $statusCode,
                userId: $userId
            );
        } catch (Exception $exception) {
            // Log error but don't interrupt the response
            Log::warning('Performance monitoring failed', [
                'error' => $exception->getMessage(),
                'route' => $route,
                'method' => $method,
            ]);
        }

        return $response;
    }
}
