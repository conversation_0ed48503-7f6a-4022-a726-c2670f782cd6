<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Log;
use Closure;
use Exception;
use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Services\PlanService;
use Illuminate\Http\Response;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Http\RedirectResponse;
use App\Services\AuthorizationService;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;

final readonly class ShareSubscriptionData
{
    public function __construct(
        private AuthorizationService $authorizationService,
        private PlanService $planService
    ) {}

    public function handle(Request $request, Closure $next): JsonResponse|Response|RedirectResponse
    {
        $user = $request->user();

        if ($user) {
            // Get roles and permissions from AuthorizationService
            try {
                $roles = $this->authorizationService->getUserRoles($user);
                $permissions = $this->authorizationService->getUserPermissions($user);
                $subscriptionPermissions = $this->authorizationService->getSubscriptionPermissions($user);
                $rolePermissions = $this->authorizationService->getRolePermissions($user);

                // Get plan source information
                $planSource = $this->planService->shouldUseDatabasePlans() ? 'database' : 'config';
                $availablePlans = $this->planService->getAvailablePlanKeys();
            } catch (Exception $e) {
                ds($e->getMessage());
                // Log the error and provide defaults
                Log::error('Failed to fetch authorization data: '.$e->getMessage());
                $roles = [];
                $permissions = [];
                $subscriptionPermissions = [];
                $rolePermissions = [];
                $planSource = 'config';
                $availablePlans = [];
            }

            Inertia::share([
                'debugMode' => app()->isLocal(),
                'auth' => $user ? [
                    'user' => $user?->toResource(),
                    'roles' => $roles,
                    'permissions' => Inertia::defer(fn (): array => $permissions->all(), 'auth'),
                    'subscriptionPermissions' => Inertia::defer(fn (): array => $subscriptionPermissions->all(), 'auth'),
                    'rolePermissions' => Inertia::defer(fn (): array => $rolePermissions->all(), 'auth'),
                    'subscription' => Inertia::defer(fn (): array => [
                        'active' => $user->getActiveSubscription(),
                        'status' => $user->getSubscriptionStatus(),
                        'plan' => $user->getCurrentPlanKey(),
                        'planName' => $user->getCurrentPlanName(),
                        'onTrial' => $user->isOnActiveTrial(),
                        'trialEndsAt' => $user->getTrialEndDate()?->toISOString(),
                    ], 'auth'),
                    'subscriptions' => Inertia::defer(fn (): Collection|EloquentCollection|array => $user?->subscriptions()->active()->pluck('type') ?? [], 'auth'),
                ] : [],

                'planSystem' => Inertia::defer(fn (): array => [
                    'source' => $planSource,
                    'availablePlans' => $availablePlans,
                    'plans' => $this->planService->shouldUseDatabasePlans()
                        ? $this->planService->getActivePlans()->values()->all()
                        : [],
                    'features' => $this->planService->shouldUseDatabasePlans()
                        ? $this->planService->getActivePlans()->flatMap(fn ($plan) => collect($plan['features'])->pluck('feature_key'))->unique()->values()->all()
                        : [],
                ]),
            ]);
        } else {
            // For guests, share empty arrays
            Inertia::share([
                'auth' => [
                    'user' => null,
                    'roles' => [],
                    'permissions' => [],
                    'subscriptionPermissions' => [],
                    'rolePermissions' => [],
                    'subscription' => [
                        'active' => false,
                        'status' => 'none',
                        'plan' => null,
                        'planName' => 'Free',
                        'onTrial' => false,
                        'trialEndsAt' => null,
                    ],
                ],
                'permissions' => [
                    'pro' => false,
                ],
            ]);
        }

        return $next($request);
    }
}
