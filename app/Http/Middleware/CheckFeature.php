<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

final class CheckFeature
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next, string $feature)
    {
        throw_unless($request->user(), new AccessDeniedHttpException('User must be authenticated to access this feature.'));

        throw_unless($request->user()->features()->active($feature), new AccessDeniedHttpException(
            "The '{$feature}' feature is not available for your subscription level."
        ));

        return $next($request);
    }
}
