<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;

final class ImpersonationLeaveRedirect
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Check if this is the leave impersonation route
        if ($request->route()?->getName() === 'filament-impersonate.leave') {
            // After leaving impersonation, redirect to admin panel
            $redirectUrl = config('filament-impersonate.redirect_after_leave', '/admin');

            if ($response instanceof RedirectResponse) {
                return redirect($redirectUrl)->with('status', 'Impersonation ended. Welcome back!');
            }
        }

        return $response;
    }
}
