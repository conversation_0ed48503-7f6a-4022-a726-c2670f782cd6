<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Spatie\Permission\Exceptions\UnauthorizedException;

final class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next, string $permission)
    {
        ds($request->user()->can('watchlists.create'));
        throw_unless($request->user(), UnauthorizedException::notLoggedIn());

        throw_unless($request->user()->can($permission), UnauthorizedException::forPermissions([$permission]));

        return $next($request);
    }
}
