<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use App\Services\AuthorizationService;

final readonly class Subscribed
{
    public function __construct(
        private AuthorizationService $authorizationService
    ) {}

    public function handle(Request $request, Closure $next, string $plan): JsonResponse|Response|RedirectResponse
    {
        /** @var User $user */
        $user = $request->user();

        if (! $user) {
            return to_route('login')->with('error', 'You must be logged in to access this resource.');
        }

        if ($this->authorizationService->shouldRedirectToSubscriptions($user, $plan)) {
            $hasExpiredTrial = $this->authorizationService->hasExpiredTrial($user, $plan);
            $message = $hasExpiredTrial
                ? 'Your trial has expired. Please subscribe to continue.'
                : 'You must be subscribed to access this resource.';

            return redirect()->route('subscriptions.index')->with('error', $message);
        }

        return $next($request);
    }
}
