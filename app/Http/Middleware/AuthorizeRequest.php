<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\AuthorizationService;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Response as BaseResponse;

final readonly class AuthorizeRequest
{
    public function __construct(
        private AuthorizationService $authorizationService
    ) {}

    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next, string $parameters = ''): BaseResponse
    {
        $user = $request->user();

        // If no user is authenticated, deny access
        abort_unless($user, 401, 'Authentication required');

        // Parse the parameters
        $parsedParams = $this->parseParameters($parameters);

        $authorized = false;

        // Check permissions (OR logic)
        if (! empty($parsedParams['permissions'])) {
            foreach ($parsedParams['permissions'] as $permission) {
                if ($this->authorizationService->can($user, $permission)) {
                    $authorized = true;
                    break;
                }
            }
        }

        // Check roles (OR logic)
        if (! $authorized && ! empty($parsedParams['roles'])) {
            foreach ($parsedParams['roles'] as $role) {
                if ($this->authorizationService->hasRole($user, $role)) {
                    $authorized = true;
                    break;
                }
            }
        }

        // Check subscription features (OR logic)
        if (! $authorized && ! empty($parsedParams['features'])) {
            foreach ($parsedParams['features'] as $feature) {
                if ($this->authorizationService->hasSubscriptionFeature($user, $feature)) {
                    $authorized = true;
                    break;
                }
            }
        }

        // Check subscription plans (OR logic)
        if (! $authorized && ! empty($parsedParams['plans'])) {
            foreach ($parsedParams['plans'] as $plan) {
                if ($this->authorizationService->isOnPlan($user, $plan)) {
                    $authorized = true;
                    break;
                }
            }
        }

        // If no authorization checks were performed, allow access
        if (empty($parsedParams['permissions']) &&
            empty($parsedParams['roles']) &&
            empty($parsedParams['features']) &&
            empty($parsedParams['plans'])) {
            $authorized = true;
        }

        abort_unless($authorized, 403, 'Access denied');

        return $next($request);
    }

    /**
     * Check if user meets all requirements (AND logic)
     * Usage: authorize:permission1,permission2&role:admin
     */
    public function handleStrict(Request $request, Closure $next, string $parameters = ''): BaseResponse
    {
        $user = $request->user();

        abort_unless($user, 401, 'Authentication required');

        $parsedParams = $this->parseParameters($parameters);

        // All permissions must be satisfied (AND logic)
        if (! empty($parsedParams['permissions'])) {
            foreach ($parsedParams['permissions'] as $permission) {
                abort_unless($this->authorizationService->can($user, $permission), 403, "Missing required permission: {$permission}");
            }
        }

        // All roles must be satisfied (AND logic)
        if (! empty($parsedParams['roles'])) {
            foreach ($parsedParams['roles'] as $role) {
                abort_unless($this->authorizationService->hasRole($user, $role), 403, "Missing required role: {$role}");
            }
        }

        // All features must be satisfied (AND logic)
        if (! empty($parsedParams['features'])) {
            foreach ($parsedParams['features'] as $feature) {
                abort_unless($this->authorizationService->hasSubscriptionFeature($user, $feature), 403, "Missing required feature: {$feature}");
            }
        }

        // All plans must be satisfied (AND logic)
        if (! empty($parsedParams['plans'])) {
            foreach ($parsedParams['plans'] as $plan) {
                abort_unless($this->authorizationService->isOnPlan($user, $plan), 403, "Missing required plan: {$plan}");
            }
        }

        return $next($request);
    }

    /**
     * Parse middleware parameters
     *
     * Supports formats like:
     * - authorize:permission1,permission2
     * - authorize:permission1,permission2|role:admin,moderator
     * - authorize:permission1|feature:pro_feature|plan:pro
     */
    private function parseParameters(string $parameters): array
    {
        $result = [
            'permissions' => [],
            'roles' => [],
            'features' => [],
            'plans' => [],
        ];

        if ($parameters === '' || $parameters === '0') {
            return $result;
        }

        // Split by pipe to get different parameter types
        $parameterGroups = explode('|', $parameters);

        foreach ($parameterGroups as $group) {
            $group = trim($group);
            if ($group === '') {
                continue;
            }

            if ($group === '0') {
                continue;
            }

            // Check if it contains a colon (type:values format)
            if (str_contains($group, ':')) {
                [$type, $values] = explode(':', $group, 2);
                $type = trim($type);
                $valuesList = array_map('trim', explode(',', $values));

                switch ($type) {
                    case 'role':
                    case 'roles':
                        $result['roles'] = array_merge($result['roles'], $valuesList);
                        break;
                    case 'feature':
                    case 'features':
                        $result['features'] = array_merge($result['features'], $valuesList);
                        break;
                    case 'plan':
                    case 'plans':
                        $result['plans'] = array_merge($result['plans'], $valuesList);
                        break;
                    case 'permission':
                    case 'permissions':
                    default:
                        $result['permissions'] = array_merge($result['permissions'], $valuesList);
                        break;
                }
            } else {
                // No colon, treat as permissions (backward compatibility)
                $valuesList = array_map('trim', explode(',', $group));
                $result['permissions'] = array_merge($result['permissions'], $valuesList);
            }
        }

        // Remove duplicates
        $result['permissions'] = array_unique($result['permissions']);
        $result['roles'] = array_unique($result['roles']);
        $result['features'] = array_unique($result['features']);
        $result['plans'] = array_unique($result['plans']);

        return $result;
    }
}
