<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use App\Models\Plan;
use App\Models\User;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

final class ValidateCheckoutAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if this is a checkout route
        $route = $request->route();
        if ($route && $route->named('subscriptions.checkout')) {
            $user = $request->user();

            // If user is not authenticated, let the auth middleware handle it
            if (! $user) {
                return $next($request);
            }

            // Check if there's a plan parameter in the route
            $planKey = $route->parameter('plan');

            if (! $planKey) {
                // No plan specified, redirect to billing overview
                return redirect()->route('subscriptions.manage')
                    ->with('error', 'Please select a subscription plan to continue.');
            }

            // Verify the plan exists and is active
            $plan = Plan::query()->active()->where('key', $planKey)->first();

            if (! $plan) {
                // Plan not found or inactive, redirect to billing overview
                return redirect()->route('subscriptions.manage')
                    ->with('error', 'The selected subscription plan is not available.');
            }

            // Check if user already has an active subscription for this plan
            $existingSubscription = Subscription::query()
                ->where('billable_type', User::class)
                ->where('billable_id', $user->getKey())
                ->where('plan_id', $plan->getKey())
                ->where('status', 'active')
                ->first();

            if ($existingSubscription) {
                // User already has this plan, redirect to billing overview
                return redirect()->route('subscriptions.manage')
                    ->with('info', 'You already have an active subscription to this plan.');
            }

            // Set a session flag to indicate this is a valid checkout access
            // This helps prevent direct URL access without proper context
            $request->session()->put('checkout_access_valid', true);
        }

        return $next($request);
    }
}
