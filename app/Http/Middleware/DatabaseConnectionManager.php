<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Context;
use Symfony\Component\HttpFoundation\Response;

final class DatabaseConnectionManager
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip if request doesn't have a session
        if (! $request->hasSession()) {
            return $next($request);
        }

        // Check if we should use write connection
        $writeConnectionUntil = $request->session()->cache()->get('use_primary_db_until');

        $primaryConnection = config('database.default');

        // Force write connection if still within the sticky period
        if ($writeConnectionUntil && $writeConnectionUntil->isFuture()) {
            DB::connection($primaryConnection)->useWriteConnectionWhenReading();
            Context::addHidden('primary_connection_active', $writeConnectionUntil);
        }

        $response = $next($request);

        // If we performed any writes, ensure read consistency
        // by using write connection for the next 15 seconds
        $connection = data_get(DB::getConnections(), $primaryConnection, null);
        if ($connection?->hasModifiedRecords()) {
            $request->session()->cache()->put(
                'use_primary_db_until',
                now()->addSeconds(15)
            );
        }

        return $response;
    }
}
