<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\App;
use Illuminate\Http\RedirectResponse;

final class ShareTranslations
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response|RedirectResponse)  $next
     * @return Response|RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Share translations with the frontend using Inertia's share method
        Inertia::share('translations', $this->getTranslations());

        return $next($request);
    }

    /**
     * Get all translations for the frontend
     *
     * @return array<string, mixed>
     */
    private function getTranslations(): array
    {
        $locale = App::getLocale();
        $translations = [];

        // Get all translation files from the lang directory
        $langPath = lang_path();
        $vendorPath = $langPath.'/vendor';

        // Load core translations
        if (is_dir($langPath.'/'.$locale)) {
            $translations = array_merge($translations, $this->loadTranslationsFromDirectory($langPath.'/'.$locale));
        }

        // Load vendor translations (like filament-impersonate)
        if (is_dir($vendorPath)) {
            foreach (glob($vendorPath.'/*', GLOB_ONLYDIR) as $packageDir) {
                $packageName = basename($packageDir);
                $packageLocalePath = $packageDir.'/'.$locale;

                if (is_dir($packageLocalePath)) {
                    $packageTranslations = $this->loadTranslationsFromDirectory($packageLocalePath);
                    if ($packageTranslations !== []) {
                        $translations[$packageName] = $packageTranslations;
                    }
                }
            }
        }

        return $translations;
    }

    /**
     * Load translations from a directory
     *
     * @return array<string, mixed>
     */
    private function loadTranslationsFromDirectory(string $directory): array
    {
        $translations = [];

        foreach (glob($directory.'/*.php') as $file) {
            $key = basename($file, '.php');
            $content = include $file;

            if (is_array($content)) {
                $translations[$key] = $content;
            }
        }

        return $translations;
    }
}
