<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Spatie\Permission\Exceptions\UnauthorizedException;

final class CheckRole
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next, string $role)
    {
        throw_unless($request->user(), UnauthorizedException::notLoggedIn());

        throw_unless($request->user()->hasRole($role), UnauthorizedException::forRoles([$role]));

        return $next($request);
    }
}
