<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

final class EnhancedSecurity
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Session fingerprinting
        $this->validateSessionFingerprint($request);

        // Concurrent session limits
        $this->enforceConcurrentSessionLimits($request);

        // Enhanced CSRF validation
        $this->validateOriginAndReferrer($request);

        return $next($request);
    }

    /**
     * Validate session fingerprint with configurable strictness.
     */
    private function validateSessionFingerprint(Request $request): void
    {
        if (! $request->hasSession()) {
            return;
        }

        $config = config('auth.enhanced_security.fingerprinting');

        // Skip if fingerprinting is disabled
        if (! $config['enabled']) {
            return;
        }

        $session = $request->session();
        $currentFingerprint = $this->generateFingerprint($request);
        $storedFingerprint = $session->get('security_fingerprint');

        if ($storedFingerprint === null) {
            // First time - store the fingerprint
            $session->put('security_fingerprint', $currentFingerprint);
            $session->put('fingerprint_created_at', now());
            $session->put('fingerprint_changes', 0);

            return;
        }

        if ($storedFingerprint !== $currentFingerprint) {
            // Handle fingerprint mismatch with grace period and tolerance
            $this->handleFingerprintMismatch($request, $session, $currentFingerprint);
        }
    }

    /**
     * Enforce concurrent session limits per user.
     */
    private function enforceConcurrentSessionLimits(Request $request): void
    {
        if (! Auth::check()) {
            return;
        }

        $userId = Auth::id();
        $sessionId = $request->session()->getId();
        $maxSessions = config('auth.enhanced_security.max_concurrent_sessions', 3);

        $cacheKey = "user_sessions:{$userId}";

        // Use atomic lock to prevent race conditions
        $lock = Cache::lock($cacheKey.':lock', 10);

        try {
            $lock->block(5);

            $activeSessions = Cache::get($cacheKey, []);

            // Add current session if not already tracked
            if (! in_array($sessionId, $activeSessions)) {
                $activeSessions[] = $sessionId;
            }

            // Remove old sessions if limit exceeded
            if (count($activeSessions) > $maxSessions) {
                $sessionsToRemove = array_slice($activeSessions, 0, -$maxSessions);
                $activeSessions = array_slice($activeSessions, -$maxSessions);

                // Actually invalidate the removed sessions
                foreach ($sessionsToRemove as $oldSessionId) {
                    Session::getHandler()->destroy($oldSessionId);
                }

                Log::info('Concurrent session limit exceeded', [
                    'user_id' => $userId,
                    'removed_sessions' => count($sessionsToRemove),
                    'max_sessions' => $maxSessions,
                ]);
            }

            // Store updated session list with shorter TTL
            Cache::put($cacheKey, $activeSessions, now()->addHours(2));
        } finally {
            $lock->release();
        }
    }

    /**
     * Validate Origin and Referrer headers for enhanced CSRF protection.
     */
    private function validateOriginAndReferrer(Request $request): void
    {
        // Skip validation for GET, HEAD, OPTIONS requests
        if (in_array($request->method(), ['GET', 'HEAD', 'OPTIONS'])) {
            return;
        }

        $allowedHosts = $this->getAllowedHosts();
        $origin = $request->header('Origin');
        $referer = $request->header('Referer');

        // Require at least one header for state-changing requests
        if (! $origin && ! $referer) {
            Log::warning('Missing Origin and Referer headers', [
                'ip' => $request->ip(),
                'method' => $request->method(),
                'user_id' => Auth::id(),
            ]);
            abort(403, 'Missing security headers');
        }

        // Check Origin header
        if ($origin && ! $this->isValidHost($origin, $allowedHosts)) {
            Log::warning('Invalid Origin header detected', [
                'origin' => $origin,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'user_id' => Auth::id(),
            ]);

            abort(403, 'Invalid origin');
        }

        // Check Referer header as fallback
        if ($referer && ! $this->isValidHost($referer, $allowedHosts)) {
            Log::warning('Invalid Referer header detected', [
                'referer' => $referer,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'user_id' => Auth::id(),
            ]);

            abort(403, 'Invalid referer');
        }
    }

    /**
     * Generate session fingerprint with configurable components.
     */
    private function generateFingerprint(Request $request): string
    {
        $config = config('auth.enhanced_security.fingerprinting.components');
        $components = [];

        // Add IP component if enabled
        if ($config['ip_address'] ?? true) {
            $components[] = $request->ip();
        }

        // Add User Agent component if enabled
        if ($config['user_agent'] ?? true) {
            $userAgent = $request->userAgent();

            // Use only major browser version if configured
            if ($config['user_agent_major_only'] ?? true) {
                $userAgent = $this->extractMajorBrowserInfo($userAgent);
            }

            $components[] = $userAgent;
        }

        // Fallback to session ID if no components are enabled
        if (empty($components)) {
            $components[] = $request->session()->getId();
        }

        return hash('sha256', implode('|', $components));
    }

    /**
     * Handle fingerprint mismatch with grace period and tolerance.
     */
    private function handleFingerprintMismatch(Request $request, $session, string $currentFingerprint): void
    {
        $config = config('auth.enhanced_security.fingerprinting');
        $gracePeriod = $config['grace_period_minutes'] ?? 15;
        $maxChanges = $config['max_fingerprint_changes'] ?? 3;

        $fingerprintCreatedAt = $session->get('fingerprint_created_at');
        $fingerprintChanges = $session->get('fingerprint_changes', 0);

        // Check if we're within grace period
        $withinGracePeriod = $fingerprintCreatedAt &&
            now()->diffInMinutes($fingerprintCreatedAt) <= $gracePeriod;

        // Check if IP change is within tolerance
        $ipChangeAcceptable = $this->isIpChangeAcceptable($request, $session);

        // Check if user agent change is acceptable (minor version changes)
        $uaChangeAcceptable = $this->isUserAgentChangeAcceptable($request, $session);

        if ($withinGracePeriod || $ipChangeAcceptable || $uaChangeAcceptable) {
            // Allow the change but track it
            $fingerprintChanges++;

            if ($fingerprintChanges <= $maxChanges) {
                Log::info('Session fingerprint updated within tolerance', [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'session_id' => $session->getId(),
                    'user_id' => Auth::id(),
                    'changes_count' => $fingerprintChanges,
                    'within_grace_period' => $withinGracePeriod,
                    'ip_change_acceptable' => $ipChangeAcceptable,
                    'ua_change_acceptable' => $uaChangeAcceptable,
                ]);

                $session->put('security_fingerprint', $currentFingerprint);
                $session->put('fingerprint_changes', $fingerprintChanges);
                $session->put('last_ip', $request->ip());
                $session->put('last_user_agent', $request->userAgent());

                return;
            }
        }

        // Strict mode or too many changes - invalidate session
        Log::warning('Session fingerprint mismatch - invalidating session', [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'session_id' => $session->getId(),
            'user_id' => Auth::id(),
            'changes_count' => $fingerprintChanges,
            'strict_mode' => $config['strict_mode'] ?? false,
        ]);

        $session->invalidate();
        $session->regenerateToken();

        abort(419, 'Session security validation failed');
    }

    /**
     * Check if IP address change is within acceptable tolerance.
     */
    private function isIpChangeAcceptable(Request $request, $session): bool
    {
        $config = config('auth.enhanced_security.ip_tolerance');

        if (! $config['enabled']) {
            return false;
        }

        $currentIp = $request->ip();
        $lastIp = $session->get('last_ip');

        if (! $lastIp) {
            return true; // No previous IP to compare
        }

        // Check if IPs are in the same subnet
        $subnetMask = $config['subnet_mask'] ?? 24;
        if ($this->areIpsInSameSubnet($currentIp, $lastIp, $subnetMask)) {
            return true;
        }

        // Check if current IP is in known proxy ranges
        $proxyRanges = array_merge(
            $config['known_proxy_ranges'] ?? [],
            $config['cdn_ranges'] ?? []
        );
        if ($this->isIpInRanges($currentIp, $proxyRanges)) {
            return true;
        }

        return $this->isIpInRanges($lastIp, $proxyRanges);
    }

    /**
     * Check if user agent change is acceptable (minor version updates).
     */
    private function isUserAgentChangeAcceptable(Request $request, $session): bool
    {
        $config = config('auth.enhanced_security.fingerprinting.components');

        if (! $config['user_agent_major_only']) {
            return false;
        }

        $currentUa = $request->userAgent();
        $lastUa = $session->get('last_user_agent');

        if (! $lastUa) {
            return true;
        }

        // Extract major browser version and name
        $currentMajor = $this->extractMajorBrowserInfo($currentUa);
        $lastMajor = $this->extractMajorBrowserInfo($lastUa);

        return $currentMajor === $lastMajor;
    }

    /**
     * Extract major browser information from user agent.
     */
    private function extractMajorBrowserInfo(string $userAgent): string
    {
        // Extract browser name and major version
        $patterns = [
            '/Chrome\/(\d+)/' => 'Chrome',
            '/Firefox\/(\d+)/' => 'Firefox',
            '/Safari\/(\d+)/' => 'Safari',
            '/Edge\/(\d+)/' => 'Edge',
            '/Opera\/(\d+)/' => 'Opera',
        ];

        foreach ($patterns as $pattern => $browser) {
            if (preg_match($pattern, $userAgent, $matches)) {
                return $browser.'/'.$matches[1];
            }
        }

        // Fallback to first 50 characters if no pattern matches
        return mb_substr($userAgent, 0, 50);
    }

    /**
     * Check if two IPs are in the same subnet.
     */
    private function areIpsInSameSubnet(string $ip1, string $ip2, int $subnetMask): bool
    {
        $ip1Long = ip2long($ip1);
        $ip2Long = ip2long($ip2);

        if ($ip1Long === false || $ip2Long === false) {
            return false;
        }

        $mask = -1 << (32 - $subnetMask);

        return ($ip1Long & $mask) === ($ip2Long & $mask);
    }

    /**
     * Check if IP is within any of the specified ranges.
     */
    private function isIpInRanges(string $ip, array $ranges): bool
    {
        $ipLong = ip2long($ip);

        if ($ipLong === false) {
            return false;
        }

        foreach ($ranges as $range) {
            if (str_contains((string) $range, '/')) {
                [$rangeIp, $mask] = explode('/', (string) $range);
                $rangeIpLong = ip2long($rangeIp);

                if ($rangeIpLong === false) {
                    continue;
                }

                $maskLong = -1 << (32 - (int) $mask);

                if (($ipLong & $maskLong) === ($rangeIpLong & $maskLong)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Get allowed hosts for Origin/Referer validation.
     */
    private function getAllowedHosts(): array
    {
        $appUrl = config('app.url');
        $allowedHosts = [parse_url((string) $appUrl, PHP_URL_HOST)];

        // Add additional allowed hosts from config
        $additionalHosts = config('auth.enhanced_security.allowed_hosts', []);

        return array_merge($allowedHosts, $additionalHosts);
    }

    /**
     * Check if the given URL's host is in the allowed hosts list.
     */
    private function isValidHost(string $url, array $allowedHosts): bool
    {
        $host = parse_url($url, PHP_URL_HOST);

        if ($host === 0 || ($host === '' || $host === '0') || $host === [] || $host === false || $host === null) {
            return false;
        }

        return in_array($host, $allowedHosts);
    }
}
