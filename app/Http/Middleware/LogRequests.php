<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Log;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

final class LogRequests
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        Log::info('Request received', [
            'url' => $request->url(),
            'method' => $request->method(),
            'route' => $request->route()?->getName(),
            'user_id' => $request->user()?->id,
            'input' => $request->input(),
        ]);

        return $next($request);
    }
}
