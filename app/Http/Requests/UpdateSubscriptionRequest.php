<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Models\Plan;
use App\Models\User;
use Illuminate\Validation\Rule;
use App\Enums\SubscriptionStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\ValidationRule;

final class UpdateSubscriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()?->can('update subscriptions') ?? false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'plan' => [
                'required',
                'string',
                'exists:plans,key',
                Rule::in(config('subscriptions.available_plans', ['basic', 'pro'])),
            ],
            'immediate' => [
                'sometimes',
                'boolean',
            ],
            'prorate' => [
                'sometimes',
                'boolean',
            ],
        ];
    }

    /**
     * Get the custom error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'plan.required' => 'Please select a subscription plan.',
            'plan.string' => 'Invalid plan format.',
            'plan.exists' => 'The selected plan does not exist.',
            'plan.in' => 'The selected plan is not available.',
            'immediate.boolean' => 'Immediate change must be true or false.',
            'prorate.boolean' => 'Proration must be true or false.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'plan' => 'subscription plan',
            'immediate' => 'immediate change',
            'prorate' => 'proration',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  Validator  $validator
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator): void {
            if ($validator->errors()->isEmpty()) {
                $this->validatePlanChangeEligibility();
                $this->validatePlanAvailability();
                $this->validateNoPendingChanges();
            }
        });
    }

    /**
     * Validate that the user can change to the selected plan.
     */
    private function validatePlanChangeEligibility(): void
    {
        /** @var User $user */
        $user = $this->user();
        $subscription = $user->getActiveSubscription();

        if (! $subscription) {
            $this->validator->errors()->add('plan', 'No active subscription found to change.');

            return;
        }

        // Check if user is trying to change to the same plan
        if ($subscription->plan_key === $this->plan) {
            $this->validator->errors()->add('plan', 'You are already subscribed to this plan.');

            return;
        }

        // Check if the plan change is allowed (e.g., downgrade restrictions)
        $currentPlan = Plan::query()->where('key', $subscription->plan_key)->first();
        $newPlan = Plan::query()->where('key', $this->plan)->first();

        if (! $currentPlan || ! $newPlan) {
            $this->validator->errors()->add('plan', 'Invalid plan configuration.');

            return;
        }

        // Prevent downgrades if subscription is in certain states
        if ($this->isDowngrade($currentPlan, $newPlan) && ! $this->canDowngrade($subscription)) {
            $this->validator->errors()->add('plan', 'Downgrades are not allowed at this time. Please contact support.');
        }
    }

    /**
     * Validate that the selected plan is available and active.
     */
    private function validatePlanAvailability(): void
    {
        $plan = Plan::query()->where('key', $this->plan)->first();

        if (! $plan || ! $plan->isActive()) {
            $this->validator->errors()->add('plan', 'The selected plan is currently not available.');

            return;
        }

        // Check if plan is configured for the default payment processor
        $defaultProcessor = config('subscriptions.default_processor', 'stripe');
        if (! $plan->isAvailableForProcessor($defaultProcessor)) {
            $this->validator->errors()->add('plan', "The selected plan is not configured for {$defaultProcessor} payments.");
        }
    }

    /**
     * Validate that user doesn't have pending plan changes.
     */
    private function validateNoPendingChanges(): void
    {
        $hasPendingChange = DB::table('subscription_plan_changes')
            ->where('user_id', $this->user()->id)
            ->where('status', SubscriptionStatus::PENDING)
            ->where('created_at', '>', now()->subMinutes(5))
            ->exists();

        if ($hasPendingChange) {
            $this->validator->errors()->add('plan', 'You have a pending subscription change. Please wait for it to complete.');
        }
    }

    /**
     * Check if this is a downgrade (new plan costs less than current).
     */
    private function isDowngrade(Plan $currentPlan, Plan $newPlan): bool
    {
        return $newPlan->price < $currentPlan->price;
    }

    /**
     * Check if user can downgrade based on subscription status.
     */
    private function canDowngrade($subscription): bool
    {
        // Allow downgrades only in certain subscription states
        return in_array($subscription->status, ['active', 'trialing']);
    }
}
