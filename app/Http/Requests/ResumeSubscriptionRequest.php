<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\ValidationRule;

final class ResumeSubscriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('update subscriptions');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // No input validation needed for resume action
            // All validation is done via custom validators
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  Validator  $validator
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator): void {
            if ($validator->errors()->isEmpty()) {
                $this->validateResumeEligibility();
            }
        });
    }

    /**
     * Validate that the user has a cancelled subscription that can be resumed.
     */
    private function validateResumeEligibility(): void
    {
        $user = $this->user();

        // Get cancelled subscription
        $subscription = $user->subscriptions()
            ->where('stripe_status', 'canceled')
            ->orWhere('stripe_status', 'cancelled')
            ->first();

        if (! $subscription) {
            $this->validator->errors()->add('subscription', 'No canceled subscription to resume.');

            return;
        }

        // Check if subscription can be resumed (within allowed timeframe)
        $resumeDeadline = config('subscriptions.resume_deadline_days', 30);
        if ($resumeDeadline > 0) {
            $cancelledAt = $subscription->getCancelledAt() ?? $subscription->updated_at;
            $daysSinceCancellation = now()->diffInDays($cancelledAt);

            if ($daysSinceCancellation > $resumeDeadline) {
                $this->validator->errors()->add('subscription', "Subscription cannot be resumed after {$resumeDeadline} days of cancellation.");
            }
        }

        // Check if user already has an active subscription
        if ($user->getActiveSubscription()) {
            $this->validator->errors()->add('subscription', 'You already have an active subscription.');
        }
    }
}
