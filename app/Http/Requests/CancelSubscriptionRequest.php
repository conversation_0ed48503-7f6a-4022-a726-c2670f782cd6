<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\ValidationRule;

final class CancelSubscriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()?->can('delete subscriptions') ?? false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'immediately' => [
                'sometimes',
                'boolean',
            ],
            'reason' => [
                'sometimes',
                'string',
                'max:500',
            ],
            'feedback' => [
                'sometimes',
                'string',
                'max:1000',
            ],
        ];
    }

    /**
     * Get the custom error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'immediately.boolean' => 'Immediate cancellation must be true or false.',
            'reason.string' => 'Cancellation reason must be text.',
            'reason.max' => 'Cancellation reason cannot exceed 500 characters.',
            'feedback.string' => 'Feedback must be text.',
            'feedback.max' => 'Feedback cannot exceed 1000 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'immediately' => 'immediate cancellation',
            'reason' => 'cancellation reason',
            'feedback' => 'feedback',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  Validator  $validator
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator): void {
            if ($validator->errors()->isEmpty()) {
                $this->validateSubscriptionEligibility();
                $this->validateCancellationPolicy();
            }
        });
    }

    /**
     * Validate that the user has an active subscription that can be cancelled.
     */
    private function validateSubscriptionEligibility(): void
    {
        /** @var User $user */
        $user = $this->user();
        $subscription = $user->getActiveSubscription();

        if (! $subscription) {
            $this->validator->errors()->add('subscription', 'No active subscription found to cancel.');

            return;
        }

        // Check if subscription is already cancelled or expired
        if (in_array($subscription->status, ['cancelled', 'expired'])) {
            $this->validator->errors()->add('subscription', 'Subscription is already cancelled or expired.');

            return;
        }

        // Check if subscription is in a state that cannot be cancelled
        if (in_array($subscription->status, ['past_due', 'unpaid'])) {
            $this->validator->errors()->add('subscription', 'Cannot cancel subscription with past due payments. Please update your payment method first.');
        }
    }

    /**
     * Validate cancellation policy restrictions.
     */
    private function validateCancellationPolicy(): void
    {
        /** @var User $user */
        $user = $this->user();
        $subscription = $user->getActiveSubscription();
        if (! $subscription) {
            return;
        }

        // Check if immediate cancellation is allowed
        if ($this->boolean('immediately') && ! config('subscriptions.management.cancel_immediately', false)) {
            $this->validator->errors()->add('immediately', 'Immediate cancellation is not allowed. Your subscription will be cancelled at the end of the current billing period.');
        }

        // Check minimum subscription period if applicable
        $minimumDays = config('subscriptions.minimum_cancellation_days', 0);
        if ($minimumDays > 0) {
            $subscriptionAge = now()->diffInDays($subscription->created_at);
            if ($subscriptionAge < $minimumDays) {
                $this->validator->errors()->add('subscription', "Subscriptions cannot be cancelled within the first {$minimumDays} days.");
            }
        }

        // Check for pending refunds or credits
        if ($this->hasPendingRefunds()) {
            $this->validator->errors()->add('subscription', 'Cannot cancel subscription while pending refunds or credits are being processed.');
        }
    }

    /**
     * Check if subscription has pending refunds or credits.
     */
    private function hasPendingRefunds(): bool
    {
        // This would typically check against a payment/refund system
        // For now, we'll implement a basic check
        return false;
    }
}
