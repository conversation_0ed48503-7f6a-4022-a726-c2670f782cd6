<?php

declare(strict_types=1);

namespace App\Http\Requests\Subscription;

use App\Models\Plan;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\ValidationRule;

final class PlanSelectionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // $this->user()->can('select_plan');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'plan' => ['required', 'string', 'min:3', 'max:50', 'regex:/^[a-zA-Z0-9_-]+$/'],
            'success_url' => ['nullable', 'url', 'max:2048'],
            'cancel_url' => ['nullable', 'url', 'max:2048'],
        ];
    }

    /**
     * Get the custom error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'plan.required' => 'Please select a subscription plan.',
            'plan.string' => 'Invalid plan format.',
            'plan.min' => 'Plan name must be at least 3 characters.',
            'plan.max' => 'Plan name must not exceed 50 characters.',
            'plan.regex' => 'Plan name contains invalid characters. Only letters, numbers, hyphens, and underscores are allowed.',
            'success_url.url' => 'Success URL must be a valid URL.',
            'success_url.max' => 'Success URL must not exceed 2048 characters.',
            'cancel_url.url' => 'Cancel URL must be a valid URL.',
            'cancel_url.max' => 'Cancel URL must not exceed 2048 characters.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator): void {
            if ($validator->errors()->isEmpty()) {
                $this->validatePlanAvailability($validator);
                $this->validateUserEligibility($validator);
                $this->validateSecurityConstraints($validator);
            }
        });
    }

    /**
     * Get the validated plan model.
     */
    public function getPlanModel(): ?Plan
    {
        return $this->validated()['plan_model'] ?? null;
    }

    /**
     * Validate that the selected plan is available and accessible.
     */
    private function validatePlanAvailability($validator): void
    {
        $plan = Plan::query()->where('key', $this->plan)->first();

        if (! $plan) {
            $validator->errors()->add('plan', 'The selected plan does not exist.');

            return;
        }

        if (! $plan->active) {
            $validator->errors()->add('plan', 'The selected plan is currently unavailable.');

            return;
        }

        // Add plan to validated data for controller use
        $this->merge(['plan_model' => $plan]);
    }

    /**
     * Validate that the user is eligible for plan selection.
     */
    private function validateUserEligibility($validator): void
    {
        $user = $this->user();

        // Check if user is authenticated
        if (! $user) {
            $validator->errors()->add('auth', 'You must be logged in to select a plan.');

            return;
        }

        // Check if user is already on this plan
        $currentSubscription = $user->subscriptions()
            ->whereIn('status', ['active', 'trialing'])
            ->first();

        if ($currentSubscription && $currentSubscription->getPlan()->key === $this->plan) {
            $validator->errors()->add('plan', 'You are already subscribed to this plan.');

            return;
        }

        // Check if user has reached subscription limit
        $activeSubscriptions = $user->subscriptions()
            ->whereIn('status', ['active', 'trialing'])
            ->count();

        if ($activeSubscriptions >= 5) {
            $validator->errors()->add('plan', 'You have reached the maximum number of active subscriptions.');

            return;
        }
    }

    /**
     * Validate security constraints to prevent abuse.
     */
    private function validateSecurityConstraints($validator): void
    {
        // Check for rapid plan selection attempts
        $user = $this->user();
        if ($user) {
            $recentAttempts = cache()->get("plan_selection_attempts_{$user->id}", 0);

            if ($recentAttempts >= 10) {
                $validator->errors()->add('plan', 'Too many plan selection attempts. Please wait a few minutes before trying again.');

                return;
            }
        }

        // Validate URLs for security
        if ($this->success_url) {
            $this->validateUrlSecurity($this->success_url, 'success_url', $validator);
        }

        if ($this->cancel_url) {
            $this->validateUrlSecurity($this->cancel_url, 'cancel_url', $validator);
        }
    }

    /**
     * Validate URL security to prevent open redirects.
     */
    private function validateUrlSecurity(string $url, string $field, $validator): void
    {
        $parsedUrl = parse_url($url);

        // Check for potentially dangerous schemes
        $allowedSchemes = ['http', 'https'];
        if (! in_array($parsedUrl['scheme'] ?? '', $allowedSchemes)) {
            $validator->errors()->add($field, 'Invalid URL scheme detected.');

            return;
        }

        // Check if URL is external and potentially malicious
        $appUrl = config('app.url');
        if ($appUrl && ! str_starts_with($url, (string) $appUrl)) {
            // Allow common external URLs for payment processors
            $allowedHosts = config('subscription.allowed_redirect_hosts', []);
            $host = $parsedUrl['host'] ?? '';

            if (! in_array($host, $allowedHosts)) {
                $validator->errors()->add($field, 'External URLs are not allowed for this field.');
            }
        }
    }
}
