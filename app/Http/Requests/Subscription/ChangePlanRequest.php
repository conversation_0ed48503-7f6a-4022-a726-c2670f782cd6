<?php

declare(strict_types=1);

namespace App\Http\Requests\Subscription;

use Exception;
use App\Models\Plan;
use App\Models\User;
use App\Enums\SubscriptionStatus;
use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Contracts\Subscription\SubscriptionInterface;

final class ChangePlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = $this->user();

        // Only check authentication - this is true authorization
        return $user instanceof User;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'plan' => [
                'required',
                'numeric',
                'exists:plans,id',
                'min:1',
                'max:999999',
            ],
        ];
    }

    /**
     * Get the custom error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'plan.required' => 'Please select a subscription plan.',
            'plan.numeric' => 'Invalid plan selection.',
            'plan.exists' => 'The selected plan does not exist.',
            'plan.min' => 'Invalid plan selection.',
            'plan.max' => 'Invalid plan selection.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator): void {
            $user = $this->user();
            if (! $user instanceof User) {
                $validator->errors()->add('user', 'Authentication required.');

                return;
            }

            $planId = $this->input('plan');
            $targetPlan = Plan::query()->active()->find($planId);

            if (! $targetPlan) {
                $validator->errors()->add('plan', 'The selected plan is not available.');

                return;
            }

            // Validate subscription status allows plan changes
            if (! $this->validateSubscriptionStatus($user)) {
                $validator->errors()->add('plan', 'Your current subscription does not allow plan changes.');

                return;
            }

            // Validate rate limiting
            // if (! $this->validateRateLimit($user)) {
            //     $validator->errors()->add('plan', 'Too many plan change attempts. Please wait before trying again.');

            //     return;
            // }

            // Validate plan eligibility
            if (! $this->validatePlanEligibility($user, $targetPlan)) {
                $validator->errors()->add('plan', 'You cannot switch to this plan from your current subscription.');
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Sanitize the plan input
        if ($this->has('plan')) {
            $planInput = $this->input('plan');

            // Remove any non-numeric characters for safety
            // Ensure it's a reasonable integer
            if (is_numeric($planInput)) {
                $planId = (int) $planInput;
                if ($planId > 0 && $planId < 1000000) {
                    $this->merge(['plan' => $planId]);
                }
            }
        }
    }

    /**
     * Validate that the user's subscription status allows plan changes.
     */
    private function validateSubscriptionStatus(User $user): bool
    {
        try {
            $activeSubscription = $user->getActiveSubscription();

            // Users without active subscription should use checkout, not change plan
            if (! $activeSubscription instanceof SubscriptionInterface) {
                Log::warning('Plan change attempt by user without active subscription', [
                    'user_id' => $user->id,
                    'ip' => $this->ip(),
                    'user_agent' => $this->userAgent(),
                ]);

                return false;
            }

            $status = $user->getSubscriptionStatus();

            // Cannot change plan if subscription is cancelled, expired, or in grace period
            if (in_array($status, [SubscriptionStatus::CANCELLED, SubscriptionStatus::EXPIRED])) {
                Log::warning('Plan change attempt by user with cancelled/expired subscription', [
                    'user_id' => $user->id,
                    'status' => $status->value,
                    'ip' => $this->ip(),
                ]);

                return false;
            }

            // Cannot change plan if subscription is past due
            if ($status === SubscriptionStatus::PAST_DUE) {
                Log::warning('Plan change attempt by user with past due subscription', [
                    'user_id' => $user->id,
                    'ip' => $this->ip(),
                ]);

                return false;
            }

            return true;

        } catch (Exception $exception) {
            Log::error('Error validating subscription status for plan change', [
                'user_id' => $user->id,
                'error' => $exception->getMessage(),
                'ip' => $this->ip(),
            ]);

            return false;
        }
    }

    /**
     * Validate that the user can change to the target plan.
     */
    private function validatePlanEligibility(User $user, Plan $targetPlan): bool
    {
        try {
            $currentPlan = $user->getCachedPlan();
            // Cannot change to the same plan
            if ($currentPlan && $currentPlan->getKey() === $targetPlan->getKey()) {
                Log::info('Plan change attempt to same plan', [
                    'user_id' => $user->id,
                    'plan_id' => $targetPlan->id,
                    'ip' => $this->ip(),
                ]);

                return false;
            }

            // Validate business rules for plan changes
            // You can add additional business logic here as needed

            return true;

        } catch (Exception $exception) {
            Log::error('Error validating plan eligibility', [
                'user_id' => $user->id,
                'target_plan_id' => $targetPlan->id,
                'error' => $exception->getMessage(),
                'ip' => $this->ip(),
            ]);

            return false;
        }
    }

    /**
     * Validate rate limiting for plan changes.
     */
    private function validateRateLimit(User $user): bool
    {
        try {
            $key = 'plan_change:'.$user->id.':'.$this->ip();

            // Limit to 3 plan change attempts per hour
            $executed = RateLimiter::attempt($key, 3, function (): void {
                // No callback needed, just counting attempts
            }, 3600); // 1 hour window

            if (! $executed) {
                Log::warning('Rate limit exceeded for plan changes', [
                    'user_id' => $user->id,
                    'ip' => $this->ip(),
                    'user_agent' => $this->userAgent(),
                ]);

                return false;
            }

            return true;

        } catch (Exception $exception) {
            Log::error('Error in rate limiting for plan change', [
                'user_id' => $user->id,
                'error' => $exception->getMessage(),
                'ip' => $this->ip(),
            ]);

            // Fail open for rate limiting to prevent blocking legitimate users
            return true;
        }
    }
}
