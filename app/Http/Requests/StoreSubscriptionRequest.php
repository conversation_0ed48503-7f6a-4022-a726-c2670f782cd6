<?php

declare(strict_types=1);

namespace App\Http\Requests;

use DB;
use App\Models\Plan;
use Illuminate\Validation\Rule;
use App\Enums\SubscriptionStatus;
use Illuminate\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\ValidationRule;

final class StoreSubscriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()?->can('create subscriptions') ?? false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'plan' => [
                'required',
                'string',
                'exists:plans,key',
                Rule::in(config('subscriptions.available_plans', ['basic', 'pro'])),
            ],
            'payment_method_id' => [
                'nullable',
                'string',
                'exists:payment_methods,id',
            ],
            'trial' => [
                'sometimes',
                'boolean',
            ],
        ];
    }

    /**
     * Get the custom error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'plan.required' => 'Please select a subscription plan.',
            'plan.string' => 'Invalid plan format.',
            'plan.exists' => 'The selected plan does not exist.',
            'plan.in' => 'The selected plan is not available.',
            'payment_method_id.exists' => 'The selected payment method is invalid.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'plan' => 'subscription plan',
            'payment_method_id' => 'payment method',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  Validator  $validator
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator): void {
            if ($validator->errors()->isEmpty()) {
                $this->validatePlanAvailability();
                $this->validateUserEligibility();
            }
        });
    }

    /**
     * Validate that the selected plan is available and active.
     */
    private function validatePlanAvailability(): void
    {
        $plan = Plan::query()->where('key', $this->plan)->first();

        if (! $plan || ! $plan->isActive()) {
            $this->validator->errors()->add('plan', 'The selected plan is currently not available.');

            return;
        }

        // Check if plan is configured for the default payment processor
        $defaultProcessor = config('subscriptions.default_processor', 'stripe');
        if (! $plan->isAvailableForProcessor($defaultProcessor)) {
            $this->validator->errors()->add('plan', "The selected plan is not configured for {$defaultProcessor} payments.");
        }
    }

    /**
     * Validate user eligibility for subscription creation.
     */
    private function validateUserEligibility(): void
    {
        $user = $this->user();
        if (! $user) {
            $this->validator->errors()->add('user', 'User not authenticated.');

            return;
        }

        // Check if user already has an active subscription
        if ($user->getActiveSubscription()) {
            $this->validator->errors()->add('plan', 'You already have an active subscription. Please cancel or change your current plan first.');
        }

        // Check if user has a pending subscription change
        $hasPendingChange = DB::table('subscription_plan_changes')
            ->where('user_id', $user->id)
            ->where('status', SubscriptionStatus::PENDING)
            ->where('created_at', '>', now()->subMinutes(5))
            ->exists();

        if ($hasPendingChange) {
            $this->validator->errors()->add('plan', 'You have a pending subscription change. Please wait for it to complete.');
        }
    }
}
