<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use Exception;
use App\Models\User;
use Illuminate\Http\Request;
use App\Enums\PaymentMethods;
use App\Models\PaymentMethod;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\ValidationException;
use Illuminate\Auth\Access\AuthorizationException;
use App\Services\PaymentProcessor\PaymentProcessorFactory;

final class PaymentMethodController extends Controller
{
    /**
     * Display a listing of the user's payment methods.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = $request->user();

            $paymentMethods = $user->paymentMethods()
                ->orderBy('is_default', 'desc')
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $paymentMethods->map(fn ($paymentMethod): array => [
                    'id' => $paymentMethod->id,
                    'type' => $paymentMethod->payment_method_type,
                    'processor' => $paymentMethod->processor_type,
                    'displayName' => $paymentMethod->getDisplayName(),
                    'lastFour' => $paymentMethod->last_four,
                    'brand' => $paymentMethod->brand,
                    'expiryDate' => $paymentMethod->getExpiryDate(),
                    'isDefault' => $paymentMethod->is_default,
                    'isExpired' => $paymentMethod->isExpired(),
                    'canBeUsedForSubscriptions' => $paymentMethod->canBeUsedForSubscriptions(),
                    'createdAt' => $paymentMethod->created_at,
                    'updatedAt' => $paymentMethod->updated_at,
                ]),
            ]);
        } catch (Exception $exception) {
            Log::error('Failed to get payment methods', [
                'user_id' => $request->user()->id,
                'error' => $exception->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment methods',
            ], 500);
        }
    }

    /**
     * Store a newly created payment method.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $user = $request->user();

            $validated = $request->validate([
                'payment_method_type' => ['required', 'string', Rule::in(PaymentMethods::cases())],
                'payment_data' => 'required|array',
                'set_as_default' => 'sometimes|boolean',
            ]);

            // Get the configured payment processor (the system determines which one to use)
            $processor = PaymentProcessorFactory::make();

            throw_unless($processor->isConfigured(), ValidationException::withMessages([
                'payment_method_type' => 'Payment processor is not properly configured',
            ]));

            // Get processor type from the processor instance
            $processorType = $processor->getName();

            // Get or create customer ID for the processor
            $customerId = $user->createOrGetCustomer($processorType);

            // Note: Payment method creation will be handled through a separate service
            // as the current interface doesn't include payment method management
            throw new Exception('Payment method creation not yet implemented with new flexible system');
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (Exception $e) {
            Log::error('Failed to add payment method', [
                'user_id' => $request->user()->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to add payment method: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update the specified payment method.
     */
    public function update(Request $request, PaymentMethod $paymentMethod): JsonResponse
    {
        try {
            $this->authorize('update', $paymentMethod);

            $validated = $request->validate([
                'set_as_default' => 'sometimes|boolean',
                'billing_details' => 'sometimes|array',
            ]);

            $processor = PaymentProcessorFactory::make($paymentMethod->processor_type);

            // TODO: Update payment method in processor if billing details provided
            // This functionality needs to be implemented in the payment processor interface
            if (filled($validated['billing_details'])) {
                // $processor->updatePaymentMethod($paymentMethod->processor_id, $validated['billing_details']);
                Log::info('Payment method billing details update skipped - not yet implemented in flexible system');
            }

            // Set as default if requested
            if ($validated['set_as_default'] ?? false) {
                $paymentMethod->setAsDefault();
            }

            Log::info('Payment method updated successfully', [
                'user_id' => $request->user()->id,
                'payment_method_id' => $paymentMethod->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment method updated successfully',
                'data' => [
                    'id' => $paymentMethod->id,
                    'is_default' => $paymentMethod->is_default,
                ],
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (AuthorizationException) {
            return response()->json([
                'success' => false,
                'message' => 'This action is unauthorized.',
            ], 403);
        } catch (Exception $e) {
            Log::error('Failed to update payment method', [
                'user_id' => $request->user()->id,
                'payment_method_id' => $paymentMethod->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment method: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified payment method.
     */
    public function destroy(Request $request, PaymentMethod $paymentMethod): JsonResponse
    {
        try {
            $this->authorize('delete', $paymentMethod);

            $processor = PaymentProcessorFactory::make($paymentMethod->processor_type);

            // TODO: Delete payment method from processor
            // This functionality needs to be implemented in the payment processor interface
            // $processor->deletePaymentMethod($paymentMethod->processor_id);
            Log::info('Payment method deletion from processor skipped - not yet implemented in flexible system');

            // Delete local record
            $paymentMethod->delete();

            Log::info('Payment method deleted successfully', [
                'user_id' => $request->user()->id,
                'payment_method_id' => $paymentMethod->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment method deleted successfully',
            ]);
        } catch (AuthorizationException) {
            return response()->json([
                'success' => false,
                'message' => 'This action is unauthorized.',
            ], 403);
        } catch (Exception $e) {
            Log::error('Failed to delete payment method', [
                'user_id' => $request->user()->id,
                'payment_method_id' => $paymentMethod->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete payment method: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Set a payment method as the default.
     */
    public function setDefault(Request $request, PaymentMethod $paymentMethod): JsonResponse
    {
        try {
            // $this->authorize('update', $paymentMethod);

            $processor = PaymentProcessorFactory::make($paymentMethod->processor_type);

            // TODO: Set as default in processor
            // This functionality needs to be implemented in the payment processor interface
            // $processor->setDefaultPaymentMethod($customerId, $paymentMethod->processor_id);
            // return response()->json([
            //     'success' => false,
            //     'message' => 'Setting default payment method in processor is not yet implemented',
            // ], 501);

            // Set as default locally
            $paymentMethod->setAsDefault();

            Log::info('Payment method set as default successfully', [
                'user_id' => $request->user()->id,
                'payment_method_id' => $paymentMethod->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment method set as default successfully',
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (AuthorizationException) {
            return response()->json([
                'success' => false,
                'message' => 'This action is unauthorized.',
            ], 403);
        } catch (Exception $e) {
            Log::error('Failed to set payment method as default', [
                'user_id' => $request->user()->id,
                'payment_method_id' => $paymentMethod->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to set payment method as default: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * Handle payment method update flow - redirect to processor or return setup URL.
     */
    public function updateFlow(Request $request): JsonResponse|RedirectResponse
    {
        try {
            /** @var User $user */
            $user = $request->user();

            // Get the user's active subscription to determine processor
            $subscription = $user->subscriptions()
                ->whereIn('status', ['active', 'trialing'])
                ->first();

            if (! $subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active subscription found',
                ], 400);
            }

            // Get the configured payment processor
            $processor = PaymentProcessorFactory::make();

            if (! $processor->isConfigured()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment processor is not properly configured',
                ], 500);
            }

            // Get or create customer ID for the processor
            $customerId = $user->getCustomerId($processor->getName());

            // Generate setup URL for updating payment method
            $setupData = $processor->createSetupIntent($customerId, [
                'return_url' => route('subscriptions.manage', ['payment_method_updated' => '1']),
            ]);

            Log::info('Payment method update initiated', [
                'user_id' => $user->id,
                'processor_type' => $subscription->processor_key,
            ]);

            // If setup URL is provided, redirect to it
            if (filled($setupData['setup_url'])) {
                return redirect($setupData['setup_url']);
            }

            // Otherwise, return the setup data for client-side handling
            return response()->json([
                'success' => true,
                'message' => 'Payment method update flow initiated successfully',
                'data' => $setupData,
            ]);
        } catch (Exception $exception) {
            Log::error('Failed to initiate payment method update', [
                'user_id' => $request->user()->id,
                'error' => $exception->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to initiate payment method update: '.$exception->getMessage(),
            ], 500);
        }
    }

    /**
     * Get available payment processors for the user.
     */
    public function getProcessors(Request $request): JsonResponse
    {
        try {
            $processors = app(PaymentProcessorFactory::class)->getEnabledProcessors();

            $processorData = [];
            foreach ($processors as $processorName) {
                $processor = PaymentProcessorFactory::make($processorName);

                if ($processor->isConfigured()) {
                    $processorData[] = [
                        'name' => $processorName,
                        'display_name' => $processor->getName(),
                        'supported_payment_methods' => [], // TODO: Implement getSupportedPaymentMethodTypes in interface
                        'is_configured' => $processor->isConfigured(),
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'data' => $processorData,
            ]);
        } catch (Exception $exception) {
            Log::error('Failed to get payment processors', [
                'user_id' => $request->user()->id,
                'error' => $exception->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment processors',
            ], 500);
        }
    }
}
