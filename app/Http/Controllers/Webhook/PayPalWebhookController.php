<?php

declare(strict_types=1);

namespace App\Http\Controllers\Webhook;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use App\Exceptions\Subscription\PaymentProcessorException;
use App\Services\PaymentProcessor\PaymentProcessorFactory;

/**
 * PayPal Webhook Controller
 *
 * Handles incoming webhooks from PayPal for various events including:
 * - Payment completion
 * - Subscription lifecycle events
 * - Billing agreements
 * - Disputes and refunds
 */
final class PayPalWebhookController
{
    /**
     * Handle PayPal webhook events
     */
    public function __invoke(Request $request): JsonResponse
    {
        try {
            // Resolve the PayPal processor
            $processor = PaymentProcessorFactory::make('paypal');

            // Verify webhook signature
            $processor->verifyWebhookSignature($request);

            // Handle the webhook event
            $processor->handleWebhook($request);

            return response()->json(['status' => 'success'], 200);

        } catch (PaymentProcessorException $e) {
            Log::error('PayPal webhook processing failed', [
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
                'processor' => $e->getProcessorName(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Return 401 for webhook verification failures, 400 for other processing failures
            $statusCode = str_contains($e->getMessage(), 'verification') ? 401 : 400;

            return response()->json([
                'error' => 'Webhook processing failed',
                'message' => $e->getMessage(),
                'processor' => $e->getProcessorName(),
            ], $statusCode);

        } catch (Exception $e) {
            Log::error('PayPal webhook handling failed', [
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'error' => 'Webhook processing failed',
                'message' => $e->getMessage(),
            ], 400);
        }
    }
}
