<?php

declare(strict_types=1);

namespace App\Http\Controllers\Webhook;

use Exception;
use App\Models\Plan;
use App\Models\User;
use DateTimeImmutable;
use App\Events\PlanCreated;
use App\Events\PlanDeleted;
use App\Events\PlanUpdated;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Laravel\Paddle\Subscription;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use App\Events\SubscriptionCreated;
use App\Events\SubscriptionUpdated;
use Illuminate\Support\Facades\Log;
use App\Events\SubscriptionCancelled;
use App\Events\PaymentFailureDetected;
use App\Exceptions\PaymentProcessorException;
use Laravel\Paddle\Http\Controllers\WebhookController as BasePaddleWebhookController;

final class PaddleWebhookController extends BasePaddleWebhookController
{
    /**
     * Handle incoming Paddle webhook requests.
     */
    public function __invoke(Request $request): JsonResponse
    {
        // Verify the webhook signature first
        ds('Received webhook message');
        $payload = $this->verifyWebhook($request);

        if ($payload === null || $payload === []) {
            return response()->json(['error' => 'Invalid signature'], 401);
        }

        // Extract event type
        $eventType = $payload['event_type'] ?? $payload['alert_name'] ?? null;

        if (! $eventType) {
            Log::error('Paddle webhook missing event type', ['payload' => $payload]);

            return response()->json(['error' => 'Missing event type'], 400);
        }

        // Log the webhook event for debugging
        if (config('cashier.paddle.integration.log_webhooks', true)) {
            Log::info('Paddle webhook received', [
                'event_type' => $eventType,
                'event_id' => $payload['event_id'] ?? $payload['alert_id'] ?? 'unknown',
                'timestamp' => now()->toISOString(),
            ]);
        }

        try {
            // Route the event to the appropriate handler
            $handled = $this->handleWebhookEvent($eventType, $payload);

            if (! $handled) {
                // If we didn't handle it, let the parent handle it (for subscription events)
                return parent::__invoke($request);
            }

            return response()->json(['message' => 'Webhook processed successfully']);
        } catch (Exception $exception) {
            Log::error('Paddle webhook processing failed', [
                'event_type' => $eventType,
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);

            $errorHandling = config('cashier.paddle.integration.error_handling', 'log');

            throw_if($errorHandling === 'throw', $exception);

            return response()->json(['message' => 'Webhook processed with errors'], 500);
        }
    }

    /**
     * Validate product data from webhook payload.
     */
    public function validateProductData(array $productData): bool
    {
        return ! empty($productData['id']) && ! empty($productData['name']);
    }

    /**
     * Extract price from product data.
     */
    public function extractPriceFromProduct(array $productData): ?float
    {
        // Paddle Classic pricing structure
        if (filled($productData['list_price'])) {
            return (float) $productData['list_price'];
        }

        // Paddle Billing (V2) pricing structure
        if (filled($productData['prices']) && is_array($productData['prices'])) {
            $defaultPrice = collect($productData['prices'])->first(fn ($price): bool => ($price['status'] ?? null) === 'active'
            );

            if ($defaultPrice) {
                return (float) ($defaultPrice['unit_price']['amount'] ?? 0);
            }
        }

        return null;
    }

    /**
     * Extract currency from product data.
     */
    public function extractCurrencyFromProduct(array $productData): ?string
    {
        // Paddle Classic
        if (filled($productData['currency'])) {
            return $productData['currency'];
        }

        // Paddle Billing (V2)
        if (filled($productData['prices']) && is_array($productData['prices'])) {
            $defaultPrice = collect($productData['prices'])->first(fn ($price): bool => ($price['status'] ?? null) === 'active'
            );

            if ($defaultPrice) {
                return $defaultPrice['unit_price']['currency_code'] ?? 'USD';
            }
        }

        return config('cashier.paddle.currency', 'USD');
    }

    /**
     * Get product status.
     */
    public function getProductStatus(array $productData): bool
    {
        // Paddle Classic
        if (filled($productData['status'])) {
            return $productData['status'] === 'active';
        }

        // Paddle Billing (V2)
        if (filled($productData['state'])) {
            return $productData['state'] === 'active';
        }

        // Default to active for new products
        return true;
    }

    /**
     * Handle subscription.created event.
     */
    protected function handleSubscriptionCreated(array $payload): bool
    {
        $subscriptionData = $payload['data'] ?? [];
        $subscriptionId = $subscriptionData['id'] ?? null;

        if (! $subscriptionId) {
            Log::warning('Missing subscription ID in created webhook', ['payload' => $payload]);

            return false;
        }

        try {
            // Find the subscription using dynamic processor fields
            $subscription = Subscription::query()
                ->where(function ($query) use ($subscriptionId): void {
                    $query->where('processor_subscription_id', $subscriptionId);
                })
                ->first();

            if (! $subscription) {
                Log::warning('Subscription not found in database', ['subscription_id' => $subscriptionId]);

                return false;
            }

            // Dispatch the subscription created event
            SubscriptionCreated::dispatch($subscription, $payload);

            Log::info('Subscription created event processed', [
                'subscription_id' => $subscription->id,
                'processor_subscription_id' => $subscription->processor_subscription_id,
                'processor_key' => $subscription->processor_key ?? 'paddle',
                'user_id' => $subscription->billable_id,
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process subscription created event', [
                'error' => $exception->getMessage(),
                'subscription_id' => $subscriptionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle subscription.updated event.
     * This processes Paddle's subscription.updated webhook to update local subscription data.
     */
    protected function handleSubscriptionUpdated(array $payload): bool
    {
        $subscriptionData = $payload['data'] ?? [];
        $subscriptionId = $subscriptionData['id'] ?? null;

        if (! $subscriptionId) {
            Log::warning('Missing subscription ID in updated webhook', ['payload' => $payload]);

            return false;
        }

        try {
            // Find the subscription using dynamic processor fields
            $subscription = Subscription::query()
                ->forProcessorWithId($subscriptionId)
                ->first();

            if (! $subscription) {
                Log::warning('Subscription not found in database', ['subscription_id' => $subscriptionId]);

                return false;
            }

            // Store previous values for comparison and event dispatching
            $previousValues = $subscription->getOriginal();

            // Update local subscription data from Paddle webhook payload
            $updateData = $this->extractSubscriptionUpdateData($subscriptionData);

            if ($updateData !== []) {
                $subscription->update($updateData);

                Log::info('Local subscription data updated from webhook', [
                    'subscription_id' => $subscription->id,
                    'updated_fields' => array_keys($updateData),
                    'event_id' => $payload['event_id'] ?? 'unknown',
                ]);
            }

            // Dispatch the subscription updated event for additional processing
            SubscriptionUpdated::dispatch($subscription, $previousValues, $payload);

            Log::info('Subscription updated event processed', [
                'subscription_id' => $subscription->id,
                'processor_subscription_id' => $subscription->processor_subscription_id,
                'processor_key' => $subscription->processor_key ?? 'paddle',
                'user_id' => $subscription->billable_id,
                'status' => $subscription->status,
                'plan_changed' => isset($updateData['paddle_plan']) || isset($updateData['plan_id']),
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process subscription updated event', [
                'error' => $exception->getMessage(),
                'subscription_id' => $subscriptionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle subscription.canceled event.
     */
    protected function handleSubscriptionCanceled(array $payload): bool
    {
        $subscriptionData = $payload['data'] ?? [];
        $subscriptionId = $subscriptionData['id'] ?? null;

        if (! $subscriptionId) {
            Log::warning('Missing subscription ID in canceled webhook', ['payload' => $payload]);

            return false;
        }

        try {
            // Find the subscription using dynamic processor fields
            $subscription = Subscription::query()
                ->forProcessorWithId($subscriptionId)
                ->first();

            if (! $subscription) {
                Log::warning('Subscription not found in database', ['subscription_id' => $subscriptionId]);

                return false;
            }

            // Dispatch the subscription cancelled event
            SubscriptionCancelled::dispatch($subscription, $payload);

            Log::info('Subscription cancelled event processed', [
                'subscription_id' => $subscription->id,
                'processor_subscription_id' => $subscription->processor_subscription_id,
                'processor_key' => $subscription->processor_key ?? 'paddle',
                'user_id' => $subscription->billable_id,
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process subscription cancelled event', [
                'error' => $exception->getMessage(),
                'subscription_id' => $subscriptionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle subscription.paused event.
     */
    protected function handleSubscriptionPaused(array $payload): bool
    {
        $subscriptionData = $payload['data'] ?? [];
        $subscriptionId = $subscriptionData['id'] ?? null;

        if (! $subscriptionId) {
            Log::warning('Missing subscription ID in paused webhook', ['payload' => $payload]);

            return false;
        }

        try {
            // Find the subscription using Laravel Paddle
            $subscription = Subscription::query()->forProcessorWithId($subscriptionId)->first();

            if (! $subscription) {
                Log::warning('Subscription not found in database', ['subscription_id' => $subscriptionId]);

                return false;
            }

            Log::info('Subscription paused event processed', [
                'subscription_id' => $subscription->id,
                'user_id' => $subscription->billable_id,
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process subscription paused event', [
                'error' => $exception->getMessage(),
                'subscription_id' => $subscriptionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle transaction.completed event.
     */
    protected function handleTransactionCompleted(array $payload): bool
    {
        $transactionData = $payload['data'] ?? [];
        $transactionId = $transactionData['id'] ?? null;

        if (! $transactionId) {
            Log::warning('Missing transaction ID in completed webhook', ['payload' => $payload]);

            return false;
        }

        try {
            Log::info('Transaction completed event processed', [
                'transaction_id' => $transactionId,
                'status' => $transactionData['status'] ?? 'unknown',
                'amount' => $transactionData['details']['totals']['total'] ?? 'unknown',
                'currency' => $transactionData['currency_code'] ?? 'unknown',
                'customer_id' => $transactionData['customer_id'] ?? 'unknown',
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process transaction completed event', [
                'error' => $exception->getMessage(),
                'transaction_id' => $transactionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle transaction.updated event.
     */
    protected function handleTransactionUpdated(array $payload): bool
    {
        $transactionData = $payload['data'] ?? [];
        $transactionId = $transactionData['id'] ?? null;

        if (! $transactionId) {
            Log::warning('Missing transaction ID in updated webhook', ['payload' => $payload]);

            return false;
        }

        try {
            Log::info('Transaction updated event processed', [
                'transaction_id' => $transactionId,
                'status' => $transactionData['status'] ?? 'unknown',
                'amount' => $transactionData['details']['totals']['total'] ?? 'unknown',
                'currency' => $transactionData['currency_code'] ?? 'unknown',
                'customer_id' => $transactionData['customer_id'] ?? 'unknown',
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process transaction updated event', [
                'error' => $exception->getMessage(),
                'transaction_id' => $transactionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle customer.updated event.
     */
    protected function handleCustomerUpdated(array $payload): bool
    {
        $customerData = $payload['data'] ?? [];
        $customerId = $customerData['id'] ?? null;

        if (! $customerId) {
            Log::warning('Missing customer ID in updated webhook', ['payload' => $payload]);

            return false;
        }

        try {
            Log::info('Customer updated event processed', [
                'customer_id' => $customerId,
                'email' => $customerData['email'] ?? 'unknown',
                'name' => $customerData['name'] ?? 'unknown',
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process customer updated event', [
                'error' => $exception->getMessage(),
                'customer_id' => $customerId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle specific webhook events.
     *
     * @return bool True if handled, false if should be passed to parent
     */
    private function handleWebhookEvent(string $eventType, array $payload): bool
    {
        return match ($eventType) {
            // Product events
            'product.created' => $this->handleProductCreated($payload),
            'product.updated' => $this->handleProductUpdated($payload),
            'product.deleted' => $this->handleProductDeleted($payload),
            // Price events might require plan updates
            'price.created', 'price.updated', 'price.deleted' => $this->handlePriceEvent($eventType, $payload),

            // Transaction events
            'transaction.billed' => $this->handleTransactionBilled($payload),
            'transaction.canceled' => $this->handleTransactionCanceled($payload),
            'transaction.completed' => $this->handleTransactionCompleted($payload),
            'transaction.created' => $this->handleTransactionCreated($payload),
            'transaction.paid' => $this->handleTransactionPaid($payload),
            'transaction.past_due' => $this->handleTransactionPastDue($payload),
            'transaction.payment_failed' => $this->handleTransactionPaymentFailed($payload),
            'transaction.ready' => $this->handleTransactionReady($payload),
            'transaction.updated' => $this->handleTransactionUpdated($payload),
            'transaction.revised' => $this->handleTransactionRevised($payload),

            // Subscription events
            'subscription.activated' => $this->handleSubscriptionActivated($payload),
            'subscription.canceled' => $this->handleSubscriptionCanceled($payload),
            'subscription.cancelled' => $this->handleSubscriptionCancelled($payload), // Legacy support
            'subscription.created' => $this->handleSubscriptionCreated($payload),
            'subscription.imported' => $this->handleSubscriptionImported($payload),
            'subscription.past_due' => $this->handleSubscriptionPastDue($payload),
            'subscription.paused' => $this->handleSubscriptionPaused($payload),
            'subscription.resumed' => $this->handleSubscriptionResumed($payload),
            'subscription.trialing' => $this->handleSubscriptionTrialing($payload),
            'subscription.updated' => $this->handleSubscriptionUpdated($payload),

            // Customer events
            'customer.created' => $this->handleCustomerCreated($payload),
            'customer.imported' => $this->handleCustomerImported($payload),
            'customer.updated' => $this->handleCustomerUpdated($payload),

            // Payment method events
            'payment_method.saved' => $this->handlePaymentMethodSaved($payload),
            'payment_method.deleted' => $this->handlePaymentMethodDeleted($payload),

            // Let parent handle any other events
            default => logger()->info('Unhandled Paddle webhook event', ['event' => $eventType, 'payload' => $payload]),
        };
    }

    /**
     * Handle product.created event.
     */
    private function handleProductCreated(array $payload): bool
    {
        $productData = $payload['data'] ?? [];

        if (! $this->validateProductData($productData)) {
            Log::warning('Invalid product data in webhook', ['payload' => $payload]);

            return false;
        }

        try {
            // Extract key from product data (use product ID as fallback)
            $key = $productData['custom_data']['key'] ?? $productData['key'] ?? $productData['id'];

            // Create or update the plan
            $plan = Plan::query()->updateOrCreate(['processor_plan_id' => $productData['id']], [
                'key' => $key,
                'name' => $productData['name'] ?? 'Unknown Plan',
                'description' => $productData['description'] ?? null,
                'price' => $this->extractPriceFromProduct($productData),
                'currency' => $this->extractCurrencyFromProduct($productData),
                'active' => $this->getProductStatus($productData),
            ]);

            // Dispatch the plan created event
            PlanCreated::dispatch($plan, $payload);

            Log::info('Plan created/updated from Paddle webhook', [
                'plan_id' => $plan->id,
                'plan_key' => $plan->key,
                'processor_plan_id' => $plan->processor_plan_id,
                'event_id' => $payload['event_id'] ?? $payload['alert_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to create/update plan from webhook', [
                'error' => $exception->getMessage(),
                'product_data' => $productData,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle product.updated event.
     */
    private function handleProductUpdated(array $payload): bool
    {
        $productData = $payload['data'] ?? [];

        if (! $this->validateProductData($productData)) {
            Log::warning('Invalid product data in webhook', ['payload' => $payload]);

            return false;
        }

        try {
            // Try to find plan by key first, then by processor_plan_id
            $key = $productData['custom_data']['key'] ?? $productData['key'] ?? null;
            $plan = $key ? Plan::query()->where('key', $key)->first() : Plan::query()->where('processor_plan_id', $productData['id'])->first();

            if (! $plan) {
                Log::warning('Plan not found for update', ['plan_id' => $productData['id'], 'key' => $key]);

                // Create it if it doesn't exist
                return $this->handleProductCreated($payload);
            }

            // Store previous values for comparison
            $previousValues = $plan->getOriginal();

            // Extract key from product data
            $newKey = $productData['custom_data']['key'] ?? $productData['key'] ?? $plan->key;

            // Update the plan
            $plan->update([
                'key' => $newKey,
                'name' => $productData['name'] ?? $plan->name,
                'description' => $productData['description'] ?? $plan->description,
                'price' => $this->extractPriceFromProduct($productData) ?? $plan->price,
                'currency' => $this->extractCurrencyFromProduct($productData) ?? $plan->currency,
                'active' => $this->getProductStatus($productData),
            ]);

            // Dispatch the plan updated event
            PlanUpdated::dispatch($plan, $previousValues, $payload);

            Log::info('Plan updated from Paddle webhook', [
                'plan_id' => $plan->id,
                'plan_key' => $plan->key,
                'processor_plan_id' => $plan->processor_plan_id,
                'event_id' => $payload['event_id'] ?? $payload['alert_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to update plan from webhook', [
                'error' => $exception->getMessage(),
                'product_data' => $productData,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle product.deleted event.
     */
    private function handleProductDeleted(array $payload): bool
    {
        $productData = $payload['data'] ?? [];
        $paddlePlanId = $productData['id'] ?? null;

        if (! $paddlePlanId) {
            Log::warning('Missing product ID in delete webhook', ['payload' => $payload]);

            return false;
        }

        try {
            // Try to find plan by key first, then by processor_plan_id
            $plan = Plan::query()->where('processor_plan_id', $paddlePlanId)->first();

            if ($plan) {
                // Deactivate the plan instead of deleting it (soft delete)
                $plan->update(['active' => false]);

                // Dispatch the plan deleted event
                PlanDeleted::dispatch($plan, $payload);

                Log::info('Plan deactivated from Paddle webhook', [
                    'plan_id' => $plan->id,
                    'plan_key' => $plan->key,
                    'processor_plan_id' => $plan->processor_plan_id,
                    'event_id' => $payload['event_id'] ?? $payload['alert_id'] ?? 'unknown',
                ]);
            } else {
                Log::warning('Plan not found for deletion', ['plan_id' => $paddlePlanId]);
            }

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to delete plan from webhook', [
                'error' => $exception->getMessage(),
                'plan_id' => $paddlePlanId,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle price events (created, updated, deleted).
     */
    private function handlePriceEvent(string $eventType, array $payload): bool
    {
        $priceData = $payload['data'] ?? [];
        $productId = $priceData['product_id'] ?? null;

        if (! $productId) {
            Log::warning('Missing product ID in price event', ['payload' => $payload]);

            return false;
        }

        try {
            // Try to find plan by key first, then by processor_plan_id
            $plan = Plan::query()->where('processor_plan_id', $productId)->first();

            if ($plan) {
                // Update plan price based on price event
                if (in_array($eventType, ['price.created', 'price.updated'])) {
                    $plan->update([
                        'price' => $this->extractPriceFromPrice($priceData),
                        'currency' => $this->extractCurrencyFromPrice($priceData),
                    ]);

                    Log::info('Plan price updated from Paddle price event', [
                        'plan_id' => $plan->id,
                        'plan_key' => $plan->key,
                        'processor_plan_id' => $plan->processor_plan_id,
                        'event_type' => $eventType,
                    ]);
                }

                return true;
            }

            return false;
        } catch (Exception $exception) {
            Log::error('Failed to handle price event', [
                'error' => $exception->getMessage(),
                'event_type' => $eventType,
                'price_data' => $priceData,
            ]);
            throw $exception;
        }
    }

    /**
     * Extract price from price data.
     */
    private function extractPriceFromPrice(array $priceData): ?float
    {
        if (filled($priceData['unit_price']['amount'])) {
            return (float) $priceData['unit_price']['amount'];
        }

        if (filled($priceData['amount'])) {
            return (float) $priceData['amount'];
        }

        return null;
    }

    /**
     * Extract currency from price data.
     */
    private function extractCurrencyFromPrice(array $priceData): ?string
    {
        if (filled($priceData['unit_price']['currency_code'])) {
            return $priceData['unit_price']['currency_code'];
        }

        return $priceData['currency'] ?? config('cashier.paddle.currency', 'USD');
    }

    /**
     * Extract amount from transaction data.
     */
    private function extractAmountFromTransaction(array $transactionData): ?float
    {
        if (filled($transactionData['details']['totals']['total'])) {
            return (float) $transactionData['details']['totals']['total'];
        }

        if (filled($transactionData['amount'])) {
            return (float) $transactionData['amount'];
        }

        if (filled($transactionData['details']['subtotal'])) {
            return (float) $transactionData['details']['subtotal'];
        }

        return null;
    }

    /**
     * Verify webhook signature for Paddle Billing v2.
     * Implements HMAC-SHA256 verification as per Paddle documentation.
     */
    private function verifyWebhook(Request $request): ?array
    {
        $signature = $request->header('Paddle-Signature');
        $secret = config('cashier.paddle.webhook.secret');
        $rawBody = $request->getContent();

        if (! $signature || ! $secret || ! $rawBody) {
            Log::warning('Missing required webhook verification data', [
                'has_signature' => ! empty($signature),
                'has_secret' => ! empty($secret),
                'has_body' => ! empty($rawBody),
            ]);

            return null;
        }

        try {
            // Parse Paddle-Signature header (format: ts=timestamp;h1=signature)
            $signatureParts = [];
            foreach (explode(';', $signature) as $part) {
                if (mb_strpos($part, '=') === false) {
                    Log::warning('Invalid Paddle-Signature header format', ['signature' => $signature]);

                    return null;
                }

                [$key, $value] = explode('=', $part, 2);
                $signatureParts[$key] = $value;
            }

            if (blank($signatureParts['ts']) || blank($signatureParts['h1'])) {
                Log::warning('Invalid Paddle-Signature header format', ['signature' => $signature]);

                return null;
            }

            $timestamp = $signatureParts['ts'];
            $providedSignature = $signatureParts['h1'];

            // Check timestamp tolerance (default 5 minutes)
            $tolerance = config('cashier.paddle.webhook.tolerance', 300);
            if (abs(time() - (int) $timestamp) > $tolerance) {
                Log::warning('Webhook timestamp outside tolerance', [
                    'timestamp' => $timestamp,
                    'current_time' => time(),
                    'tolerance' => $tolerance,
                ]);

                return null;
            }

            // Create signature payload: timestamp + ':' + raw body
            $signaturePayload = $timestamp.':'.$rawBody;

            // Generate HMAC-SHA256 signature
            $expectedSignature = hash_hmac('sha256', $signaturePayload, (string) $secret);

            // Verify signature using timing-safe comparison
            if (! hash_equals($expectedSignature, $providedSignature)) {
                Log::warning('Webhook signature verification failed', [
                    'provided_signature' => $providedSignature,
                    'expected_signature' => $expectedSignature,
                ]);

                return null;
            }

            // Parse and return payload
            $payload = json_decode($rawBody, true);
            if (! $payload) {
                Log::warning('Invalid JSON payload in webhook');

                return null;
            }

            Log::info('Webhook signature verified successfully', [
                'event_type' => $payload['event_type'] ?? 'unknown',
            ]);

            return $payload;
        } catch (Exception $exception) {
            Log::error('Webhook signature verification failed', [
                'error' => $exception->getMessage(),
                'signature' => $signature,
            ]);

            return null;
        }
    }

    /**
     * Handle subscription.cancelled event.
     */
    private function handleSubscriptionCancelled(array $payload): bool
    {
        return $this->handleSubscriptionCanceled($payload);
    }

    /**
     * Handle subscription.activated event.
     */
    private function handleSubscriptionActivated(array $payload): bool
    {
        $subscriptionData = $payload['data'] ?? [];
        $subscriptionId = $subscriptionData['id'] ?? null;

        if (! $subscriptionId) {
            Log::warning('Missing subscription ID in activated webhook', ['payload' => $payload]);

            return false;
        }

        try {
            // Find the subscription using dynamic processor fields
            $subscription = Subscription::query()->forProcessorWithId($subscriptionId)->first();

            if (! $subscription) {
                Log::warning('Subscription not found in database', ['subscription_id' => $subscriptionId]);

                return false;
            }

            Log::info('Subscription activated event processed', [
                'subscription_id' => $subscription->id,
                'processor_subscription_id' => $subscription->processor_subscription_id,
                'processor_key' => $subscription->processor_key ?? 'paddle',
                'user_id' => $subscription->billable_id,
                'status' => $subscription->status,
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process subscription activated event', [
                'error' => $exception->getMessage(),
                'subscription_id' => $subscriptionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle subscription.imported event.
     */
    private function handleSubscriptionImported(array $payload): bool
    {
        $subscriptionData = $payload['data'] ?? [];
        $subscriptionId = $subscriptionData['id'] ?? null;

        if (! $subscriptionId) {
            Log::warning('Missing subscription ID in imported webhook', ['payload' => $payload]);

            return false;
        }

        try {
            // Find the subscription using Laravel Paddle
            $subscription = Subscription::query()->forProcessorWithId($subscriptionId)->first();

            if (! $subscription) {
                Log::warning('Subscription not found in database', ['subscription_id' => $subscriptionId]);

                return false;
            }

            Log::info('Subscription imported event processed', [
                'subscription_id' => $subscription->id,
                'user_id' => $subscription->billable_id,
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process subscription imported event', [
                'error' => $exception->getMessage(),
                'subscription_id' => $subscriptionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle subscription.past_due event.
     */
    private function handleSubscriptionPastDue(array $payload): bool
    {
        $subscriptionData = $payload['data'] ?? [];
        $subscriptionId = $subscriptionData['id'] ?? null;

        if (! $subscriptionId) {
            Log::warning('Missing subscription ID in past_due webhook', ['payload' => $payload]);

            return false;
        }

        try {
            // Find the subscription using Laravel Paddle
            $subscription = Subscription::query()->forProcessorWithId($subscriptionId)->first();

            if (! $subscription) {
                Log::warning('Subscription not found in database', ['subscription_id' => $subscriptionId]);

                return false;
            }

            Log::info('Subscription past due event processed', [
                'subscription_id' => $subscription->id,
                'user_id' => $subscription->billable_id,
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process subscription past due event', [
                'error' => $exception->getMessage(),
                'subscription_id' => $subscriptionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle subscription.resumed event.
     */
    private function handleSubscriptionResumed(array $payload): bool
    {
        $subscriptionData = $payload['data'] ?? [];
        $subscriptionId = $subscriptionData['id'] ?? null;

        if (! $subscriptionId) {
            Log::warning('Missing subscription ID in resumed webhook', ['payload' => $payload]);

            return false;
        }

        try {
            // Find the subscription using Laravel Paddle
            $subscription = Subscription::query()->forProcessorWithId($subscriptionId)->first();

            if (! $subscription) {
                Log::warning('Subscription not found in database', ['subscription_id' => $subscriptionId]);

                return false;
            }

            Log::info('Subscription resumed event processed', [
                'subscription_id' => $subscription->id,
                'user_id' => $subscription->billable_id,
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process subscription resumed event', [
                'error' => $exception->getMessage(),
                'subscription_id' => $subscriptionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle subscription.trialing event.
     */
    private function handleSubscriptionTrialing(array $payload): bool
    {
        $subscriptionData = $payload['data'] ?? [];
        $subscriptionId = $subscriptionData['id'] ?? null;

        if (! $subscriptionId) {
            Log::warning('Missing subscription ID in trialing webhook', ['payload' => $payload]);

            return false;
        }

        try {
            // Find the subscription using Laravel Paddle
            $subscription = Subscription::query()
                ->where('processor_subscription_id', $subscriptionId)
                ->where('processor_key', 'paddle')->first();

            if (! $subscription) {
                Log::warning('Subscription not found in database', ['subscription_id' => $subscriptionId]);

                return false;
            }

            Log::info('Subscription trialing event processed', [
                'subscription_id' => $subscription->id,
                'user_id' => $subscription->billable_id,
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process subscription trialing event', [
                'error' => $exception->getMessage(),
                'subscription_id' => $subscriptionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    // Transaction Event Handlers

    /**
     * Handle transaction.billed event.
     */
    private function handleTransactionBilled(array $payload): bool
    {
        $transactionData = $payload['data'] ?? [];
        $transactionId = $transactionData['id'] ?? null;

        if (! $transactionId) {
            Log::warning('Missing transaction ID in billed webhook', ['payload' => $payload]);

            return false;
        }

        try {
            Log::info('Transaction billed event processed', [
                'transaction_id' => $transactionId,
                'status' => $transactionData['status'] ?? 'unknown',
                'amount' => $transactionData['details']['totals']['total'] ?? 'unknown',
                'currency' => $transactionData['currency_code'] ?? 'unknown',
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process transaction billed event', [
                'error' => $exception->getMessage(),
                'transaction_id' => $transactionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle transaction.canceled event.
     */
    private function handleTransactionCanceled(array $payload): bool
    {
        $transactionData = $payload['data'] ?? [];
        $transactionId = $transactionData['id'] ?? null;

        if (! $transactionId) {
            Log::warning('Missing transaction ID in canceled webhook', ['payload' => $payload]);

            return false;
        }

        try {
            Log::info('Transaction canceled event processed', [
                'transaction_id' => $transactionId,
                'status' => $transactionData['status'] ?? 'unknown',
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process transaction canceled event', [
                'error' => $exception->getMessage(),
                'transaction_id' => $transactionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle transaction.created event.
     */
    private function handleTransactionCreated(array $payload): bool
    {
        $transactionData = $payload['data'] ?? [];
        $transactionId = $transactionData['id'] ?? null;

        if (! $transactionId) {
            Log::warning('Missing transaction ID in created webhook', ['payload' => $payload]);

            return false;
        }

        try {
            Log::info('Transaction created event processed', [
                'transaction_id' => $transactionId,
                'status' => $transactionData['status'] ?? 'unknown',
                'amount' => $transactionData['details']['totals']['total'] ?? 'unknown',
                'currency' => $transactionData['currency_code'] ?? 'unknown',
                'customer_id' => $transactionData['customer_id'] ?? 'unknown',
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process transaction created event', [
                'error' => $exception->getMessage(),
                'transaction_id' => $transactionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle transaction.paid event.
     */
    private function handleTransactionPaid(array $payload): bool
    {
        $transactionData = $payload['data'] ?? [];
        $transactionId = $transactionData['id'] ?? null;

        if (! $transactionId) {
            Log::warning('Missing transaction ID in paid webhook', ['payload' => $payload]);

            return false;
        }

        try {
            Log::info('Transaction paid event processed', [
                'transaction_id' => $transactionId,
                'status' => $transactionData['status'] ?? 'unknown',
                'amount' => $transactionData['details']['totals']['total'] ?? 'unknown',
                'currency' => $transactionData['currency_code'] ?? 'unknown',
                'customer_id' => $transactionData['customer_id'] ?? 'unknown',
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process transaction paid event', [
                'error' => $exception->getMessage(),
                'transaction_id' => $transactionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle transaction.past_due event.
     */
    private function handleTransactionPastDue(array $payload): bool
    {
        $transactionData = $payload['data'] ?? [];
        $transactionId = $transactionData['id'] ?? null;

        if (! $transactionId) {
            Log::warning('Missing transaction ID in past_due webhook', ['payload' => $payload]);

            return false;
        }

        try {
            Log::info('Transaction past due event processed', [
                'transaction_id' => $transactionId,
                'status' => $transactionData['status'] ?? 'unknown',
                'amount' => $transactionData['details']['totals']['total'] ?? 'unknown',
                'currency' => $transactionData['currency_code'] ?? 'unknown',
                'customer_id' => $transactionData['customer_id'] ?? 'unknown',
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process transaction past due event', [
                'error' => $exception->getMessage(),
                'transaction_id' => $transactionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle transaction.payment_failed event.
     */
    private function handleTransactionPaymentFailed(array $payload): bool
    {
        $transactionData = $payload['data'] ?? [];
        $transactionId = $transactionData['id'] ?? null;

        if (! $transactionId) {
            Log::warning('Missing transaction ID in payment_failed webhook', ['payload' => $payload]);

            return false;
        }

        $correlationId = $payload['event_id'] ?? Str::uuid()->toString();

        try {
            DB::beginTransaction();

            // Find associated customer and subscription
            $customerId = $transactionData['customer_id'] ?? null;
            $subscriptionId = $transactionData['subscription_id'] ?? null;

            $user = null;
            $subscription = null;

            if ($customerId) {
                $user = User::query()->where('processor_customer_id', $customerId)->first();
            }

            if ($subscriptionId) {
                $subscription = Subscription::query()->where('processor_subscription_id', $subscriptionId)->first();
            }

            // Create Transaction record for the payment failure
            $transaction = Transaction::query()->create([
                'uuid' => (string) Str::uuid(),
                'user_id' => $user?->id,
                'subscription_id' => $subscription?->id,
                'processor_transaction_id' => $transactionId,
                'processor' => 'paddle',
                'type' => 'payment',
                'status' => 'failed',
                'amount' => $this->extractAmountFromTransaction($transactionData),
                'currency' => $transactionData['currency_code'] ?? 'USD',
                'metadata' => [
                    'paddle_event_id' => $payload['event_id'] ?? null,
                    'paddle_event_type' => 'transaction.payment_failed',
                    'failure_reason' => $transactionData['status'] ?? 'unknown',
                    'failure_details' => $transactionData['error'] ?? null,
                    'customer_email' => $transactionData['customer']['email'] ?? null,
                    'correlation_id' => $correlationId,
                ],
                'failed_at' => now(),
                'processing_status' => 'failed',
            ]);

            // Update subscription status if associated
            if ($subscription) {
                $subscription->update([
                    'status' => 'past_due',
                    'last_payment_failure_at' => now(),
                    'payment_failure_count' => ($subscription->payment_failure_count ?? 0) + 1,
                ]);
            }

            // Dispatch payment failure event for monitoring and notifications
            event(new PaymentFailureDetected(
                transactionId: $transaction->id,
                userId: $user?->id,
                subscriptionId: $subscription?->id,
                amount: $transaction->amount,
                currency: $transaction->currency,
                failureReason: $transactionData['status'] ?? 'unknown',
                failureDetails: $transactionData['error'] ?? null,
                correlationId: $correlationId,
                metadata: [
                    'processor_transaction_id' => $transactionId,
                    'paddle_event_id' => $payload['event_id'] ?? null,
                    'customer_email' => $transactionData['customer']['email'] ?? null,
                ]
            ));

            DB::commit();

            Log::info('Transaction payment failure processed and recorded', [
                'correlation_id' => $correlationId,
                'transaction_id' => $transaction->id,
                'processor_transaction_id' => $transactionId,
                'user_id' => $user?->id,
                'subscription_id' => $subscription?->id,
                'amount' => $transaction->amount,
                'currency' => $transaction->currency,
                'failure_reason' => $transactionData['status'] ?? 'unknown',
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;

        } catch (Exception $exception) {
            DB::rollBack();

            Log::error('Failed to process transaction payment failed event', [
                'correlation_id' => $correlationId,
                'error' => $exception->getMessage(),
                'transaction_id' => $transactionId,
                'payload' => $payload,
            ]);

            throw new PaymentProcessorException(
                PaymentProcessorException::create(
                    'PROCESSOR_ERROR',
                    'Payment failure processing failed: '.$exception->getMessage(),
                    'We encountered an issue processing your payment failure notification. This should resolve automatically.',
                    500,
                    array_merge($payload, [
                        'correlation_id' => $correlationId,
                        'processing_error' => $exception->getMessage(),
                    ])
                ),
                $payload,
                $exception
            );
        }
    }

    /**
     * Handle transaction.ready event.
     */
    private function handleTransactionReady(array $payload): bool
    {
        $transactionData = $payload['data'] ?? [];
        $transactionId = $transactionData['id'] ?? null;

        if (! $transactionId) {
            Log::warning('Missing transaction ID in ready webhook', ['payload' => $payload]);

            return false;
        }

        try {
            Log::info('Transaction ready event processed', [
                'transaction_id' => $transactionId,
                'status' => $transactionData['status'] ?? 'unknown',
                'amount' => $transactionData['details']['totals']['total'] ?? 'unknown',
                'currency' => $transactionData['currency_code'] ?? 'unknown',
                'customer_id' => $transactionData['customer_id'] ?? 'unknown',
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process transaction ready event', [
                'error' => $exception->getMessage(),
                'transaction_id' => $transactionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle transaction.revised event.
     */
    private function handleTransactionRevised(array $payload): bool
    {
        $transactionData = $payload['data'] ?? [];
        $transactionId = $transactionData['id'] ?? null;

        if (! $transactionId) {
            Log::warning('Missing transaction ID in revised webhook', ['payload' => $payload]);

            return false;
        }

        try {
            Log::info('Transaction revised event processed', [
                'transaction_id' => $transactionId,
                'status' => $transactionData['status'] ?? 'unknown',
                'amount' => $transactionData['details']['totals']['total'] ?? 'unknown',
                'currency' => $transactionData['currency_code'] ?? 'unknown',
                'customer_id' => $transactionData['customer_id'] ?? 'unknown',
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process transaction revised event', [
                'error' => $exception->getMessage(),
                'transaction_id' => $transactionId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    // Customer Event Handlers

    /**
     * Handle customer.created event.
     */
    private function handleCustomerCreated(array $payload): bool
    {
        $customerData = $payload['data'] ?? [];
        $customerId = $customerData['id'] ?? null;

        if (! $customerId) {
            Log::warning('Missing customer ID in created webhook', ['payload' => $payload]);

            return false;
        }

        try {
            Log::info('Customer created event processed', [
                'customer_id' => $customerId,
                'email' => $customerData['email'] ?? 'unknown',
                'name' => $customerData['name'] ?? 'unknown',
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process customer created event', [
                'error' => $exception->getMessage(),
                'customer_id' => $customerId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle customer.imported event.
     */
    private function handleCustomerImported(array $payload): bool
    {
        $customerData = $payload['data'] ?? [];
        $customerId = $customerData['id'] ?? null;

        if (! $customerId) {
            Log::warning('Missing customer ID in imported webhook', ['payload' => $payload]);

            return false;
        }

        try {
            Log::info('Customer imported event processed', [
                'customer_id' => $customerId,
                'email' => $customerData['email'] ?? 'unknown',
                'name' => $customerData['name'] ?? 'unknown',
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process customer imported event', [
                'error' => $exception->getMessage(),
                'customer_id' => $customerId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    // Payment Method Event Handlers

    /**
     * Handle payment_method.saved event.
     */
    private function handlePaymentMethodSaved(array $payload): bool
    {
        $paymentMethodData = $payload['data'] ?? [];
        $paymentMethodId = $paymentMethodData['id'] ?? null;

        if (! $paymentMethodId) {
            Log::warning('Missing payment method ID in saved webhook', ['payload' => $payload]);

            return false;
        }

        try {
            Log::info('Payment method saved event processed', [
                'payment_method_id' => $paymentMethodId,
                'customer_id' => $paymentMethodData['customer_id'] ?? 'unknown',
                'type' => $paymentMethodData['type'] ?? 'unknown',
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process payment method saved event', [
                'error' => $exception->getMessage(),
                'payment_method_id' => $paymentMethodId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Handle payment_method.deleted event.
     */
    private function handlePaymentMethodDeleted(array $payload): bool
    {
        $paymentMethodData = $payload['data'] ?? [];
        $paymentMethodId = $paymentMethodData['id'] ?? null;

        if (! $paymentMethodId) {
            Log::warning('Missing payment method ID in deleted webhook', ['payload' => $payload]);

            return false;
        }

        try {
            Log::info('Payment method deleted event processed', [
                'payment_method_id' => $paymentMethodId,
                'customer_id' => $paymentMethodData['customer_id'] ?? 'unknown',
                'type' => $paymentMethodData['type'] ?? 'unknown',
                'event_id' => $payload['event_id'] ?? 'unknown',
            ]);

            return true;
        } catch (Exception $exception) {
            Log::error('Failed to process payment method deleted event', [
                'error' => $exception->getMessage(),
                'payment_method_id' => $paymentMethodId,
                'payload' => $payload,
            ]);
            throw $exception;
        }
    }

    /**
     * Extract subscription update data from Paddle webhook payload.
     * This maps Paddle's webhook data structure to our local subscription fields.
     *
     * @param  array<string, mixed>  $subscriptionData
     * @return array<string, mixed>
     */
    private function extractSubscriptionUpdateData(array $subscriptionData): array
    {
        $updateData = [];

        // Map Paddle subscription status to our status
        if (isset($subscriptionData['status'])) {
            $updateData['status'] = $this->mapPaddleStatus($subscriptionData['status']);
        }

        // Update billing period dates
        if (isset($subscriptionData['current_period_end'])) {
            $updateData['current_period_end'] = $this->parseDateTime($subscriptionData['current_period_end']);
        }

        if (isset($subscriptionData['current_period_start'])) {
            $updateData['current_period_start'] = $this->parseDateTime($subscriptionData['current_period_start']);
        }

        // Update trial period dates
        if (isset($subscriptionData['trial_end'])) {
            $updateData['trial_ends_at'] = $this->parseDateTime($subscriptionData['trial_end']);
        }

        // Update plan information if available
        if (isset($subscriptionData['items']) && is_array($subscriptionData['items'])) {
            foreach ($subscriptionData['items'] as $item) {
                if (isset($item['price']['id'])) {
                    // Update the paddle_plan field with the new price ID
                    $updateData['paddle_plan'] = $item['price']['id'];

                    // If we have a plan mapping service, we could also update plan_id
                    // For now, just store the Paddle price/plan ID
                    break; // Assuming single item subscription
                }
            }
        }

        // Update quantity if changed
        if (isset($subscriptionData['quantity'])) {
            $updateData['quantity'] = (int) $subscriptionData['quantity'];
        }

        // Update cancellation information
        if (isset($subscriptionData['cancellation_effective_date'])) {
            $updateData['ends_at'] = $this->parseDateTime($subscriptionData['cancellation_effective_date']);
        }

        return $updateData;
    }

    /**
     * Map Paddle subscription status to our local status.
     */
    private function mapPaddleStatus(string $paddleStatus): string
    {
        return match ($paddleStatus) {
            'active' => 'active',
            'trialing' => 'trialing',
            'past_due' => 'past_due',
            'unpaid' => 'unpaid',
            'canceled', 'cancelled' => 'cancelled',
            'incomplete', 'incomplete_expired' => 'incomplete',
            'paused' => 'paused',
            default => 'unknown',
        };
    }

    /**
     * Parse datetime from Paddle webhook payload.
     */
    private function parseDateTime(mixed $datetime): ?string
    {
        if (empty($datetime)) {
            return null;
        }

        try {
            // Paddle typically provides ISO 8601 format dates
            $date = new DateTimeImmutable($datetime);

            return $date->format('Y-m-d H:i:s');
        } catch (Exception $exception) {
            Log::warning('Failed to parse datetime from Paddle webhook', [
                'datetime' => $datetime,
                'error' => $exception->getMessage(),
            ]);

            return null;
        }
    }
}
