<?php

declare(strict_types=1);

namespace App\Http\Controllers\Passages;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Http\Client\Response;
use <PERSON>rcen\Passage\PassageControllerInterface;

final class StocksMarketDataController extends Controller implements PassageControllerInterface
{
    /**
     * Transform and/or validate the request before it is sent to the service.
     */
    public function getRequest(Request $request): Request
    {
        return $request;
    }

    /**
     * Transform or validate the response before it is sent back to the client.
     */
    public function getResponse(Request $request, Response $response): Response
    {
        return $response;
    }

    /**
     * Set the route options when the service is instantiated.
     */
    public function getOptions(): array
    {
        Log::info('getOptions() called', ['timestamp' => microtime(true)]);

        $baseUri = config('services.stocks.market_data_proxy.base_uri', 'https://market_data_proxy:443/');

        Log::info('Environment check', [
            'STOCKS_MARKET_DATA_BASE_URI' => $baseUri,
            'env_function_test' => config('services.stocks.market_data_proxy.base_uri'),
            'all_env_vars_with_stocks' => array_filter($_ENV, fn ($key): bool => mb_stripos((string) $key, 'stocks') !== false || mb_stripos((string) $key, 'market') !== false, ARRAY_FILTER_USE_KEY),
        ]);

        // Base cURL options for SSL and Docker networking
        $curlOptions = [
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_VERBOSE => true,
            CURLOPT_FOLLOWLOCATION => false, // Disable redirects to see what's happening
        ];

        Log::info('Using Docker service discovery', [
            'baseUri' => $baseUri,
            'ssl_verification' => 'disabled for internal network',
        ]);

        $data = [
            'base_uri' => $baseUri,
            'verify' => false,
            'timeout' => 30,
            'connect_timeout' => 30,
            'curl' => $curlOptions,
        ];

        Log::info('Final Market Data Service Options', $data);

        return $data;
    }
}
