<?php

declare(strict_types=1);

namespace App\Http\Controllers\Passages;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Http\Client\Response;
use <PERSON>rcen\Passage\PassageControllerInterface;

final class StockServiceController extends Controller implements PassageControllerInterface
{
    /**
     * Transform and/or validate the request before it is sent to the service.
     */
    public function getRequest(Request $request): Request
    {
        return $request;
    }

    /**
     * Transform or validate the response before it is sent back to the client.
     */
    public function getResponse(Request $request, Response $response): Response
    {
        Log::info('StocksDataController getResponse() called', [
            'request_url' => $request->fullUrl(),
            'request_path' => $request->path(),
            'response_status' => $response->status(),
            'response_body' => $response->body(),
            'response_headers' => $response->headers(),
        ]);

        return $response;
    }

    /**
     * Set the route options when the service is instantiated.
     */
    public function getOptions(): array
    {
        $baseUri = config('services.passage.api-stocks-algorithm.base_uri');

        Log::info('StocksDataController getOptions() called', [
            'base_uri_from_config' => $baseUri,
            'full_config' => config('services.passage'),
            'env_var' => config('services.passage.api-stocks-algorithm.base_uri'),
            'timestamp' => microtime(true),
        ]);

        $data = [
            'base_uri' => $baseUri,
            'timeout' => 30,
            'connect_timeout' => 10,
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
        ];

        ds($baseUri, $data);
        Log::info('Final StocksData Service Options', $data);

        return $data;
    }
}
