<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\ValueObjects\Symbol;
use Illuminate\Http\Request;
use App\Services\SymbolService;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;

final class SymbolsController extends Controller
{
    public function __construct(
        private readonly SymbolService $symbolService
    ) {}

    /**
     * Search symbols
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        $limit = $request->get('limit', 50);

        // Validate limit
        $limit = min(max($limit, 1), 100);

        if (! $this->symbolService->hasSymbols()) {
            return response()->json([
                'error' => 'Symbols data not available. Please run symbols:download command first.',
                'data' => [],
                'total' => 0,
            ], 503);
        }

        $symbols = $this->symbolService->searchSymbols($query, $limit);
        $total = $this->symbolService->getSymbolsCount();

        return response()->json([
            'data' => $symbols->values(),
            'total' => $total,
            'query' => $query,
            'limit' => $limit,
            'count' => $symbols->count(),
        ]);
    }

    /**
     * Get symbol by code
     */
    public function show(string $code): JsonResponse
    {
        if (! $this->symbolService->hasSymbols()) {
            return response()->json([
                'error' => 'Symbols data not available. Please run symbols:download command first.',
            ], 503);
        }

        $symbol = $this->symbolService->getSymbolByCode($code);

        if (! $symbol instanceof Symbol) {
            return response()->json([
                'error' => 'Symbol not found',
            ], 404);
        }

        return response()->json([
            'data' => $symbol,
        ]);
    }

    /**
     * Get symbols stats
     */
    public function stats(): JsonResponse
    {
        if (! $this->symbolService->hasSymbols()) {
            return response()->json([
                'error' => 'Symbols data not available. Please run symbols:download command first.',
            ], 503);
        }

        return response()->json([
            'total_symbols' => $this->symbolService->getSymbolsCount(),
            'has_data' => $this->symbolService->hasSymbols(),
        ]);
    }
}
