<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Models\Watchlist;
use App\ValueObjects\Symbol;
use Illuminate\Http\Request;
use App\Models\WatchlistAsset;
use App\Services\SymbolService;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\ValidationException;

/**
 * @group Watchlist Asset management
 *
 * APIs for managing assets within watchlists
 */
final class WatchlistsAssetsController extends Controller
{
    public function __construct(
        private readonly SymbolService $symbolService
    ) {}

    /**
     * Add an asset to a watchlist.
     *
     * @authenticated
     */
    public function store(Request $request, Watchlist $watchlist): JsonResponse|RedirectResponse
    {
        $this->authorize('update', $watchlist);

        $validated = $request->validate([
            'symbol' => 'required|string|max:50',
            'asset_type' => 'required|string|in:stock,crypto,forex,commodity,index',
        ]);

        // Validate that the symbol exists in our database
        $symbol = $this->symbolService->getSymbolByCode($validated['symbol']);
        throw_unless($symbol instanceof Symbol, ValidationException::withMessages([
            'symbol' => 'The provided symbol does not exist in our database.',
        ]));

        // Check if asset already exists in watchlist
        $existingAsset = $watchlist->assets()
            ->where('symbol', $validated['symbol'])
            ->where('exchange', $symbol->getExchange())
            ->first();

        throw_if($existingAsset, ValidationException::withMessages([
            'symbol' => 'This asset is already in the watchlist.',
        ]));

        $asset = $watchlist->assets()->create([
            ...$validated,
            'exchange' => $symbol->getExchange(),
        ]);

        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Asset added to watchlist successfully.',
                'asset' => $asset,
            ], 201);
        }

        return to_route('watchlists.show', $watchlist);
    }

    /**
     * Remove an asset from a watchlist.
     *
     * @authenticated
     */
    public function destroy(Watchlist $watchlist, WatchlistAsset $asset): RedirectResponse
    {
        $this->authorize('update', $watchlist);

        if ($asset->watchlist_id !== $watchlist->id) {
            return to_route('watchlists.show', $watchlist)->withErrors(['error' => 'Asset not found in this watchlist']);
        }

        $asset->delete();

        return to_route('watchlists.show', $watchlist);
    }
}
