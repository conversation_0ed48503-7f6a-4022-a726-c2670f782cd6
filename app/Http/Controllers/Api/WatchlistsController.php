<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Models\Watchlist;
use App\Enums\WatchlistType;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Database\QueryException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Validation\ValidationException;
use Illuminate\Container\Attributes\CurrentUser;

/**
 * @group Watchlist management
 *
 * APIs for managing user watchlists and their assets
 */
final class WatchlistsController extends Controller
{
    /**
     * Display the authenticated user's watchlists.
     *
     * @return Collection<int, Watchlist>
     *
     * @authenticated
     */
    public function index(#[CurrentUser] $user): Collection
    {
        return $user->watchlists()->with('assets')->get();
    }

    /**
     * Create a new watchlist for the authenticated user.
     *
     * @authenticated
     */
    public function store(Request $request, #[CurrentUser] $user): RedirectResponse
    {
        $validated = $request->validate($this->getValidationRules());

        try {
            $user->watchlists()->create($validated);

            return to_route('watchlists.index')->with('success', 'Watchlist created successfully');
        } catch (QueryException $queryException) {
            // Duplicate entry
            throw_if($queryException->errorInfo[1] === 1062, ValidationException::withMessages([
                'name' => ['You already have a watchlist with this name.'],
            ]));

            throw $queryException;
        }
    }

    /**
     * Display the specified watchlist with its assets.
     */
    public function show(Watchlist $watchlist): Watchlist
    {
        $this->authorize('view', $watchlist);

        return $watchlist->load('assets');
    }

    /**
     * Update the specified watchlist.
     *
     * @authenticated
     */
    public function update(Request $request, Watchlist $watchlist): Watchlist
    {
        $this->authorize('update', $watchlist);

        $validated = $request->validate($this->getValidationRules());

        try {
            $watchlist->update($validated);

            return $watchlist;
        } catch (QueryException $queryException) {
            // Duplicate entry
            throw_if($queryException->errorInfo[1] === 1062, ValidationException::withMessages([
                'name' => ['You already have a watchlist with this name.'],
            ]));

            throw $queryException;
        }
    }

    /**
     * Remove the specified watchlist.
     *
     * @authenticated
     */
    public function destroy(Watchlist $watchlist): Response
    {
        $this->authorize('delete', $watchlist);

        $watchlist->delete();

        return response()->noContent();
    }

    /**
     * Get the validation rules for watchlist creation/update.
     *
     * @return array<string, string>
     */
    private function getValidationRules(): array
    {
        $typeValues = array_map(fn (WatchlistType $case) => $case->value, WatchlistType::cases());

        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'type' => 'required|string|in:'.implode(',', $typeValues),
        ];
    }
}
