<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Morcen\Passage\Facades\Passage;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Container\Attributes\CurrentUser;

final class StockDataController extends Controller
{
    /**
     * Retrieve stock data from the external stocks-data-api service.
     */
    public function index(Request $request, #[CurrentUser] $user): JsonResponse
    {
        ds($user);
        try {
            Log::info('=== StockDataController index method called ===', [
                'request_path' => $request->path(),
                'request_method' => $request->method(),
                'request_url' => $request->fullUrl(),
                'controller' => self::class,
                'timestamp' => microtime(true),
            ]);

            // Get the external API base URL from config
            $baseUrl = rtrim((string) config('services.stocks_data.base_url'), '/');
            $apiUrl = $baseUrl.'/api/symbols';

            $response = Passage::getService('api-stocks-algorithm');

            Log::info('Stock data service configuration', [
                'config_services_stocks_data_base_url' => config('services.stocks_data.base_url'),
                'env_STOCKS_DATA_BASE_URI' => config('services.stocks_data.api-stocks-algorithm.base_uri'),
                'all_config_services' => config('services'),
                'final_base_url' => $baseUrl,
                'final_api_url' => $apiUrl,
            ]);

            Log::info('Making request to external stocks API', [
                'api_url' => $apiUrl,
                'base_url' => $baseUrl,
            ]);

            // Make HTTP request to external stocks API
            $response = Http::timeout(30)
                ->connectTimeout(10)
                ->withHeaders([
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ])
                ->get($apiUrl);

            // Log request status
            Log::info('External API request completed', [
                'status' => $response->status(),
                'successful' => $response->successful(),
                'response_size' => mb_strlen($response->body()),
            ]);

            // Check if request was successful
            throw_unless($response->successful(), new Exception('External API returned status: '.$response->status()));

            // Get the JSON data from the response
            $data = $response->json();

            // Return the JSON response from the external API
            return response()->json($data);
        } catch (Exception $exception) {
            // Log the error for debugging
            Log::error('Failed to retrieve stock data', [
                'error' => $exception->getMessage(),
                'exception_type' => $exception::class,
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
            ]);

            // Return an error response with debug info
            return response()->json([
                'error' => 'Failed to retrieve stock data',
                'message' => 'Unable to connect to market data service',
                'debug' => $exception->getMessage(),
            ], 500);
        }
    }
}
