<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use Exception;
use App\Models\Plan;
use App\Models\User;
use Inertia\Inertia;
use Inertia\Response;
use RuntimeException;
use Illuminate\View\View;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\RedirectResponse;
use App\Services\SubscriptionQueryOptimizer;
use App\Exceptions\PaymentProcessorException;
use Illuminate\Container\Attributes\CurrentUser;
use App\Services\Subscription\SubscriptionService;
use App\ValueObjects\Subscription\SubscriptionData;
use App\Contracts\Actions\ChangePlanActionInterface;
use App\Contracts\Subscription\SubscriptionInterface;
use App\Http\Requests\Subscription\ChangePlanRequest;
use App\Actions\Subscription\CancelSubscriptionAction;
use App\Actions\Subscription\ResumeSubscriptionAction;
use Symfony\Component\HttpFoundation\StreamedResponse;
use App\Services\PaymentProcessor\PaymentProcessorFactory;
use App\Http\Requests\Subscription\CancelSubscriptionRequest;
use App\Http\Requests\Subscription\ResumeSubscriptionRequest;
use App\Services\PaymentProcessor\TransactionRollbackService;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class SubscriptionsController extends Controller
{
    /**
     * Redirect to billing portal.
     */
    public function index(): RedirectResponse
    {
        $this->handlePaymentsRedirects();

        // For now, redirect to subscriptions page since billing portal is processor-specific
        // This could be enhanced to support processor-specific billing portals
        return redirect()->route('subscriptions.manage');
    }

    /**
     * Display subscription status in a blade view with Vue component.
     */
    public function status(#[CurrentUser] User $user): Response
    {
        $subscriptionStatus = [
            'status' => $user->getSubscriptionStatus(),
            'plan_name' => $user->getCurrentPlanName(),
            'is_on_trial' => $user->isOnActiveTrial(),
            'trial_ends_at' => $user->getTrialEndDate()?->toISOString(),
        ];

        return Inertia::render('Subscriptions/Status', $subscriptionStatus);
    }

    /**
     * Display subscription management page with active subscriptions and available plans.
     */
    public function create(Request $request, #[CurrentUser] User $user, SubscriptionService $subscriptionService): Response
    {
        $this->handlePaymentsRedirects();

        $tab = $request->query('tab', 'overview');

        $subscriptionData = SubscriptionData::fromUserAndService($user, $subscriptionService);

        return Inertia::render('Subscriptions/Manage', [
            'tab' => $tab,
            ...$subscriptionData->toArray(),
        ]);
    }

    /**
     * Create checkout session for selected subscription plan.
     *
     * @throws NotFoundHttpException If subscription plan not found
     */
    public function checkout(Plan|string $plan, PaymentProcessorFactory $paymentFactory, TransactionRollbackService $rollbackService): RedirectResponse|Response
    {
        $this->handlePaymentsRedirects();

        // Find the plan by key
        $targetPlan = $plan instanceof Plan ? $plan : Plan::query()->active()->where('key', $plan)->first();

        abort_unless($targetPlan, 404, 'Plan not found');

        /** @var User $user */
        $user = request()->user();

        // Get the configured payment processor
        $processor = $paymentFactory->create();

        $checkoutContext = [
            'user_id' => $user->id,
            'plan_id' => $targetPlan->id,
            'operation_type' => 'checkout_creation',
        ];

        try {
            // Debug output
            $checkoutData = $processor->createCheckout($user, $targetPlan, [
                'successUrl' => route('subscriptions.manage'),
                'cancelUrl' => route('subscriptions.manage', ['cancelled' => '1']),
            ]);

            // Debug output

            throw_if(blank($checkoutData->checkoutUrl) || ! $this->validateCheckoutUrl($checkoutData->checkoutUrl), PaymentProcessorException::create(
                'VALIDATION_ERROR',
                'Invalid checkout URL generated',
                'We encountered an issue while setting up your checkout. Please try again or contact support.',
                500,
                array_merge($checkoutContext, ['checkout_data' => $checkoutData->toArray()])
            ));

            // Get subscription data for the Manage view
            $subscriptionService = app(SubscriptionService::class);

            $subscriptionData = SubscriptionData::fromUserAndService($user, $subscriptionService);

            $vuePage = match ($processor->getName()) {
                'paddle' => 'PaddleCheckout',
                default => 'Default',
            };

            // Return Inertia response with checkout data for inline checkout
            return Inertia::render(sprintf('Subscriptions/Checkouts/%s', $vuePage), [
                'checkoutData' => $checkoutData->toArray(),
                ...$subscriptionData->toArray(),
            ]);

        } catch (PaymentProcessorException $e) {
            return $this->handlePaymentError($e, $checkoutContext);
        } catch (\InvalidArgumentException $e) {
            // Handle validation errors more gracefully
            return redirect()->route('subscriptions.manage')
                ->with('error', $e->getMessage())
                ->with('error_type', 'VALIDATION_ERROR');
        } catch (Exception $e) {
            $paymentException = PaymentProcessorException::create(
                'PROCESSOR_ERROR',
                'Checkout creation failed: '.$e->getMessage(),
                'We encountered an unexpected error while setting up your subscription. Please try again.',
                500,
                array_merge($checkoutContext, ['original_error' => $e->getMessage()]),
                $e
            );

            return $this->handlePaymentError($paymentException, $checkoutContext);
        }
    }

    /**
     * Handle subscription plan changes.
     */
    public function changePlan(ChangePlanRequest $request, ChangePlanActionInterface $changePlanAction, TransactionRollbackService $rollbackService): RedirectResponse
    {
        Log::info('changePlan method called', [
            'url' => request()->url(),
            'method' => request()->method(),
            'user_id' => request()->user()?->id,
            'plan_id' => request()->input('plan'),
        ]);

        $this->handlePaymentsRedirects();

        /** @var User $user */
        $user = $request->user();

        // Get validated plan ID from form request
        $planId = (int) $request->validated('plan');

        $planChangeContext = [
            'user_id' => $user->getKey(),
            'plan_id' => $planId,
            'operation_type' => 'subscription_change',
        ];

        try {
            // Execute the plan change action
            $result = $changePlanAction->handle($user, $planId);

            Log::info('ChangePlanAction result', [
                'success' => data_get($result, 'success'),
                'message' => data_get($result, 'message'),
                'plan_id' => $planId,
            ]);

            if (data_get($result, 'success') === false) {
                $message = data_get($result, 'message', 'Unable to change your subscription plan. Please try again.');
                $errorType = data_get($result, 'error_type', 'VALIDATION_ERROR');

                // Check if this is a payment method validation error
                if (str_contains((string) $message, 'No valid payment methods available') || str_contains((string) $message, 'payment method')) {
                    // Redirect to checkout for payment method validation failures
                    $plan = Plan::query()->find($planId);
                    Log::info('Payment method validation failed, attempting checkout redirect', [
                        'plan_id' => $planId,
                        'plan_found' => (bool) $plan,
                        'plan_key' => $plan?->key,
                        'message' => $message,
                    ]);
                    if ($plan) {
                        $redirectUrl = route('subscriptions.checkout', ['plan' => $plan->key]);
                        Log::info('Generated checkout redirect URL', [
                            'redirect_url' => $redirectUrl,
                            'plan_key' => $plan->key,
                        ]);

                        return redirect()->route('subscriptions.checkout', ['plan' => $plan->key])
                            ->with('error', $message);
                    }
                }

                throw PaymentProcessorException::create(
                    $errorType,
                    'Plan change validation failed',
                    is_string($message) ? $message : 'Unable to change your subscription plan. Please try again.',
                    422,
                    array_merge($planChangeContext, ['result' => $result])
                );
            }

            // Return redirect with success flash message
            return redirect()->route('subscriptions.manage')->with('success', $result['message']);

        } catch (PaymentProcessorException $e) {
            return $this->handlePaymentError($e, $planChangeContext);
        } catch (Exception $e) {
            $paymentException = PaymentProcessorException::create(
                'PROCESSOR_ERROR',
                'Plan change failed: '.$e->getMessage(),
                'We encountered an unexpected error while changing your subscription plan. Please try again.',
                500,
                array_merge($planChangeContext, ['original_error' => $e->getMessage()]),
                $e
            );

            return $this->handlePaymentError($paymentException, $planChangeContext);
        }
    }

    /**
     * Handle subscription cancellation.
     */
    public function cancel(CancelSubscriptionRequest $request, CancelSubscriptionAction $cancelSubscriptionAction, TransactionRollbackService $rollbackService): RedirectResponse
    {
        $this->handlePaymentsRedirects();

        /** @var User $user */
        $user = $request->user();

        $cancelContext = [
            'user_id' => $user->id,
            'operation_type' => 'subscription_cancellation',
        ];

        try {
            // Execute the cancellation action
            $result = $cancelSubscriptionAction->handle($user);

            if (data_get($result, 'success') === false) {
                $message = data_get($result, 'message', 'Unable to cancel your subscription. Please try again.');
                throw PaymentProcessorException::create(
                    'PROCESSOR_ERROR',
                    'Subscription cancellation failed',
                    is_string($message) ? $message : 'Unable to cancel your subscription. Please try again.',
                    422,
                    array_merge($cancelContext, ['result' => $result])
                );
            }

            return back()->with('success', $result['message']);

        } catch (PaymentProcessorException $e) {
            return $this->handlePaymentError($e, $cancelContext);
        } catch (Exception $e) {
            $paymentException = PaymentProcessorException::create(
                'PROCESSOR_ERROR',
                'Subscription cancellation failed: '.$e->getMessage(),
                'We encountered an unexpected error while cancelling your subscription. Please try again.',
                500,
                array_merge($cancelContext, ['original_error' => $e->getMessage()]),
                $e
            );

            return $this->handlePaymentError($paymentException, $cancelContext);
        }
    }

    /**
     * Handle subscription resumption.
     */
    public function resume(ResumeSubscriptionRequest $request, ResumeSubscriptionAction $resumeSubscriptionAction, TransactionRollbackService $rollbackService): RedirectResponse
    {
        $this->handlePaymentsRedirects();

        /** @var User $user */
        $user = $request->user();

        $resumeContext = [
            'user_id' => $user->id,
            'operation_type' => 'subscription_resumption',
        ];

        try {
            // Execute the resumption action
            $result = $resumeSubscriptionAction->handle($user);

            if (data_get($result, 'success') === false) {
                $message = data_get($result, 'message', 'Unable to resume your subscription. Please try again.');
                throw PaymentProcessorException::create(
                    'PROCESSOR_ERROR',
                    'Subscription resumption failed',
                    is_string($message) ? $message : 'Unable to resume your subscription. Please try again.',
                    422,
                    array_merge($resumeContext, ['result' => $result])
                );
            }

            return back()->with('success', $result['message']);

        } catch (PaymentProcessorException $e) {
            return $this->handlePaymentError($e, $resumeContext);
        } catch (Exception $e) {
            $paymentException = PaymentProcessorException::create(
                'PROCESSOR_ERROR',
                'Subscription resumption failed: '.$e->getMessage(),
                'We encountered an unexpected error while resuming your subscription. Please try again.',
                500,
                array_merge($resumeContext, ['original_error' => $e->getMessage()]),
                $e
            );

            return $this->handlePaymentError($paymentException, $resumeContext);
        }
    }

    /**
     * Download invoice PDF.
     */
    public function downloadInvoice(Request $request, string $invoiceId): StreamedResponse
    {
        $this->handlePaymentsRedirects();

        $request->user();

        // For Paddle billing, we need to handle invoices differently
        // This is a placeholder implementation - adjust based on your payment processor
        try {
            return response()->streamDownload(function (): void {
                echo 'Invoice download not implemented for current payment processor';
            }, "invoice-{$invoiceId}.pdf");
        } catch (Exception) {
            abort(404, 'Invoice not found');
        }
    }

    /**
     * Handle payment failure detection from webhooks or return URLs.
     */
    public function handlePaymentFailure(Request $request): RedirectResponse
    {
        /** @var User $user */
        $user = $request->user();

        $failureContext = [
            'user_id' => $user->id,
            'transaction_id' => $request->input('transaction_id'),
            'error_type' => $request->input('error_type', 'unknown'),
            'operation_type' => 'payment_failure',
        ];

        try {
            $errorType = $request->input('error_type', 'PROCESSOR_ERROR');
            $errorMessage = $request->input('error_message', 'Unknown error');

            $paymentException = PaymentProcessorException::create(
                is_string($errorType) ? $errorType : 'PROCESSOR_ERROR',
                'Payment failure detected: '.(is_string($errorMessage) ? $errorMessage : 'Unknown error'),
                $this->getPaymentFailureMessage(is_string($errorType) ? $errorType : 'unknown'),
                400,
                $failureContext
            );

            return $this->handlePaymentError($paymentException, $failureContext);

        } catch (Exception $exception) {
            Log::error('Failed to handle payment failure', [
                'user_id' => $user->id,
                'request_data' => $request->all(),
                'error' => $exception->getMessage(),
            ]);

            return redirect()->route('subscriptions.manage')
                ->with('error', 'We encountered an issue processing your payment failure notification. Please check your subscription status.');
        }
    }

    /**
     * Retry a failed payment operation.
     */
    public function retryPayment(Request $request, PaymentProcessorFactory $paymentFactory): RedirectResponse
    {
        /** @var User $user */
        $user = $request->user();

        $transactionIdInput = $request->input('transaction_id');
        $transactionId = is_string($transactionIdInput) ? $transactionIdInput : '';
        $operationType = $request->input('operation_type', 'checkout_creation');

        $retryContext = [
            'user_id' => $user->id,
            'transaction_id' => $transactionId,
            'operation_type' => $operationType,
            'retry_attempt' => true,
        ];

        try {
            $processor = $paymentFactory->create();

            $result = match ($operationType) {
                'checkout_creation' => $processor->retryCheckout($transactionId),
                'subscription_change' => $processor->retrySubscriptionChange($transactionId),
                'payment_method_update' => $processor->retryPaymentMethodUpdate($transactionId),
                default => throw new Exception('Unsupported operation type for retry'),
            };

            return redirect()->route('subscriptions.manage')
                ->with('success', 'Payment operation completed successfully.');

        } catch (PaymentProcessorException $e) {
            return $this->handlePaymentError($e, $retryContext);
        } catch (Exception $e) {
            $paymentException = PaymentProcessorException::create(
                'PROCESSOR_ERROR',
                'Payment retry failed: '.$e->getMessage(),
                'We encountered an error while retrying your payment. Please try again or contact support.',
                500,
                array_merge($retryContext, ['original_error' => $e->getMessage()]),
                $e
            );

            return $this->handlePaymentError($paymentException, $retryContext);
        }
    }

    /**
     * Handle checkout completion for plan switching.
     */
    public function handleCheckoutCompletion(Request $request, SubscriptionService $subscriptionService): JsonResponse
    {
        try {
            $payload = $request->validate([
                'checkout_id' => ['required', 'string'],
                'plan_id' => ['required', 'string'],
                'user_id' => ['required', 'integer'],
                'status' => ['required', 'string'],
            ]);
            /** @var User $user */
            $user = User::query()->findOrFail($payload['user_id']);
            // Find the target plan
            $targetPlan = Plan::query()->where('key', $payload['plan_id'])->first();
            throw_unless($targetPlan, new NotFoundHttpException('Plan not found'));

            // Get the user's current subscription
            $subscription = $subscriptionService->getActiveSubscription($user);
            throw_unless($subscription instanceof SubscriptionInterface, new RuntimeException('No active subscription found'));

            // Process the plan change
            if ($payload['status'] === 'completed') {
                $result = $subscriptionService->changeSubscriptionPlan($subscription, $targetPlan);
                if ($result) {
                    // Clear cached subscription data
                    $this->clearUserSubscriptionCache($user);
                    Log::info('Plan change completed successfully after checkout', [
                        'user_id' => $user->id,
                        'plan_id' => $targetPlan->id,
                        'checkout_id' => $payload['checkout_id'],
                    ]);

                    return response()->json([
                        'success' => true,
                        'message' => 'Plan changed successfully',
                    ]);
                }
            }

            throw new RuntimeException('Failed to process plan change');
        } catch (Exception $exception) {
            Log::error('Checkout completion processing failed', [
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
                'payload' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => $exception->getMessage(),
            ], 500);
        }
    }

    /**
     * Handle payment errors with user-friendly responses and rollback coordination.
     *
     * @param  array<string, mixed>  $context
     */
    private function handlePaymentError(PaymentProcessorException $e, array $context): RedirectResponse
    {
        Log::error('Payment processing error', [
            'error_type' => $e->getErrorData()->errorType,
            'user_id' => $e->getUserId(),
            'plan_id' => $e->getPlanId(),
            'transaction_id' => $e->getTransactionId(),
            'correlation_id' => $e->getCorrelationId(),
            'error_message' => $e->getMessage(),
            'severity' => $e->getSeverity(),
            'should_alert' => $e->shouldAlert(),
        ]);

        if ($e->shouldAlert()) {
            Log::critical('Critical payment processing error requiring attention', [
                'correlation_id' => $e->getCorrelationId(),
                'error_data' => $e->toArray(),
            ]);
        }

        $flashData = [
            'error' => $e->getUserMessage(),
            'error_type' => $e->getErrorData()->errorType,
            'error_details' => [
                'severity' => $e->getSeverity(),
                'retryable' => $e->isRetryable(),
                'recovery_suggestions' => $e->getRecoverySuggestionsData(),
                'correlation_id' => $e->getCorrelationId(),
            ],
        ];

        $redirectUrl = $this->getRedirectUrlForError($e, $context);

        if ($e->isRetryable() && $this->shouldAttemptRollback($context)) {
            try {
                $this->attemptRollback($e, $context);
            } catch (Exception $rollbackException) {
                Log::error('Rollback attempt failed', [
                    'original_error' => $e->getMessage(),
                    'rollback_error' => $rollbackException->getMessage(),
                    'context' => $context,
                ]);
            }
        }

        Log::info('About to return final redirect', [
            'redirect_url' => $redirectUrl,
            'flash_data' => $flashData,
            'full_redirect_object' => redirect($redirectUrl)->with($flashData),
        ]);

        return redirect($redirectUrl)->with($flashData);
    }

    /**
     * Get appropriate redirect URL based on error type and context.
     *
     * @param  array<string, mixed>  $context
     */
    private function getRedirectUrlForError(PaymentProcessorException $e, array $context): string
    {
        // Debug logging
        Log::info('getRedirectUrlForError called', [
            'error_type' => $e->getErrorData()->errorType,
            'error_message' => $e->getUserMessage(),
            'context' => $context,
            'plan_id_from_context' => $context['plan_id'] ?? null,
            'plan_id_from_exception' => $e->getPlanId(),
        ]);

        if (in_array($e->getErrorData()->errorType, ['VALIDATION_ERROR', 'INSUFFICIENT_FUNDS', 'CARD_DECLINED'])) {
            // For validation errors related to payment methods, redirect to checkout
            $errorMessage = $e->getUserMessage();
            Log::info('Checking error message for payment method keywords', [
                'error_message' => $errorMessage,
                'contains_payment_methods' => str_contains($errorMessage, 'No valid payment methods available'),
                'contains_payment_method' => str_contains($errorMessage, 'payment method'),
            ]);

            if (str_contains($errorMessage, 'No valid payment methods available') || str_contains($errorMessage, 'payment method')) {
                // Try to get plan ID from context first, then from exception
                $planId = $context['plan_id'] ?? $e->getPlanId();
                Log::info('Looking up plan', [
                    'plan_id' => $planId,
                    'plan_exists' => $planId ? Plan::query()->find($planId) : null,
                ]);

                if ($planId) {
                    $plan = Plan::query()->find($planId);
                    if ($plan) {
                        $url = route('subscriptions.checkout', ['plan' => $plan->key]);
                        Log::info('Generated checkout URL', ['url' => $url]);

                        return $url;
                    }
                }

                // Fallback to billing tab if plan not found
                $fallbackUrl = route('subscriptions.manage', ['tab' => 'billing']);
                Log::info('Using fallback billing URL', ['url' => $fallbackUrl]);

                return $fallbackUrl;
            }

            $billingUrl = route('subscriptions.manage', ['tab' => 'billing']);
            Log::info('Using billing URL for validation error', ['url' => $billingUrl]);

            return $billingUrl;
        }

        if (in_array($e->getErrorData()->errorType, ['AUTHENTICATION_ERROR', 'CONFIGURATION_ERROR'])) {
            $accountUrl = route('subscriptions.manage', ['tab' => 'account']);
            Log::info('Using account URL for auth/config error', ['url' => $accountUrl]);

            return $accountUrl;
        }

        $errorType = $e->getErrorData()->errorType;
        if ($errorType === 'RATE_LIMIT_ERROR' || str_contains($errorType, 'RATE_LIMIT') || str_contains($errorType, 'rate_limit')) {
            $billingUrl = route('subscriptions.manage', ['tab' => 'billing']);
            Log::info('Using billing URL for rate limit error', ['error_type' => $errorType, 'url' => $billingUrl]);

            return $billingUrl;
        }

        $defaultUrl = route('subscriptions.manage');
        Log::info('Using default manage URL', ['url' => $defaultUrl]);

        return $defaultUrl;
    }

    /**
     * Determine if rollback should be attempted for this error.
     *
     * @param  array<string, mixed>  $context
     */
    private function shouldAttemptRollback(array $context): bool
    {
        return in_array($context['operation_type'], [
            'checkout_creation',
            'subscription_change',
            'payment_method_update',
        ]);
    }

    /**
     * Attempt rollback for failed payment operations.
     *
     * @param  array<string, mixed>  $context
     */
    private function attemptRollback(PaymentProcessorException $e, array $context): void
    {
        $rollbackService = app(TransactionRollbackService::class);

        $rollbackContext = array_merge($context, [
            'correlation_id' => $e->getCorrelationId(),
            'error_type' => $e->getErrorData()->errorType,
        ]);

        match ($context['operation_type']) {
            'checkout_creation' => $rollbackService->rollbackCheckoutCreation($rollbackContext),
            'subscription_change' => $rollbackService->rollbackSubscriptionChange($rollbackContext),
            'payment_method_update' => $rollbackService->rollbackPaymentMethodUpdate($rollbackContext),
            default => throw new Exception('Unsupported operation type for rollback'),
        };
    }

    /**
     * Get user-friendly message for payment failure.
     */
    private function getPaymentFailureMessage(string $errorType): string
    {
        return match ($errorType) {
            'insufficient_funds' => 'Your payment method has insufficient funds. Please update your payment method or try a different one.',
            'card_declined' => 'Your card was declined. Please check your card details or use a different payment method.',
            'expired_card' => 'Your payment method has expired. Please update your payment information.',
            'processor_error' => 'The payment processor encountered an error. Please try again.',
            'network_error' => 'We had trouble connecting to the payment processor. Please try again.',
            'authentication_error' => 'There was an authentication issue. Please log out and log back in.',
            default => 'We encountered an issue processing your payment. Please try again or contact support.',
        };
    }

    /**
     * Clear user subscription cache.
     */
    private function clearUserSubscriptionCache(User $user): void
    {
        $optimizer = app(SubscriptionQueryOptimizer::class);
        $optimizer->clearUserSubscriptionCache($user);
    }

    /**
     * Validate checkout URL, supporting both standard URLs and inline checkout protocol.
     */
    private function validateCheckoutUrl(string $url): bool
    {
        // Support inline checkout protocol
        if (str_starts_with($url, 'checkout://')) {
            // Extract and validate the base64-encoded data
            $dataPart = mb_substr($url, mb_strlen('checkout://'));
            if ($dataPart === '' || $dataPart === '0') {
                return false;
            }

            // Decode the base64 data
            $decoded = base64_decode($dataPart);
            if ($decoded === false) {
                return false;
            }

            // Parse as JSON
            $data = json_decode($decoded, true);
            if (json_last_error() !== JSON_ERROR_NONE || ! is_array($data)) {
                return false;
            }

            // Validate required fields for inline checkout
            $requiredFields = ['transaction_id', 'vendor_id', 'product_id'];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    return false;
                }
            }

            // Validate transaction_id format
            $transactionId = $data['transaction_id'] ?? '';
            if (! is_string($transactionId) || ! str_starts_with($transactionId, 'txn_')) {
                return false;
            }

            // Validate vendor_id format (accept both numeric and string vendor IDs)
            return ! empty($data['vendor_id']) && ! (! is_numeric($data['vendor_id']) && ! is_string($data['vendor_id']));
        }

        // For regular URLs, use standard validation
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
}
