<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

abstract class Controller
{
    use AuthorizesRequests;

    /**
     * Handle payment-related redirects based on configuration.
     *
     * This method checks if payments are enabled in the configuration.
     * If payments are disabled, it redirects to the configured route.
     */
    protected function handlePaymentsRedirects(): ?RedirectResponse
    {
        $paymentsEnabled = config('services.payments.enabled', true);

        if (! $paymentsEnabled) {
            $redirectRoute = config('services.payments.redirect_to', 'dashboard');

            return redirect()->intended(route($redirectRoute));
        }

        return null;
    }
}
