<?php

declare(strict_types=1);

namespace App\Http\Controllers\Services;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Morcen\Passage\Facades\Passage;
use App\Http\Controllers\Controller;

final class StockDataController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $response = Passage::getService('stocks')->get('stocks');

        return response()->json($response->json());
    }
}
