<?php

declare(strict_types=1);

namespace App\Http\Controllers\Resources;

use App\Models\User;
use Inertia\Response;
use App\Models\Watchlist;
use App\Data\WatchlistData;
use App\Enums\WatchlistType;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Spatie\LaravelData\DataCollection;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Container\Attributes\CurrentUser;

final class WatchlistsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(#[CurrentUser] User $user): Response
    {
        return inertia('Watchlists/Index', [
            'watchlists' => fn (): DataCollection|\Spatie\LaravelData\PaginatedDataCollection|\Spatie\LaravelData\CursorPaginatedDataCollection|\Illuminate\Support\Enumerable|\Illuminate\Pagination\AbstractPaginator|\Illuminate\Contracts\Pagination\Paginator|\Illuminate\Pagination\AbstractCursorPaginator|\Illuminate\Contracts\Pagination\CursorPaginator|array => WatchlistData::collect($user->watchlists()->with('assets')->get()),
            'validTypes' => WatchlistType::cases(),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): void
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, #[CurrentUser] $user): RedirectResponse
    {
        $validated = $request->validate($this->getValidationRules());

        try {
            $user->watchlists()->create($validated);

            return Redirect::route('watchlists.index')
                ->with('success', 'Watchlist created successfully.');
        } catch (QueryException $queryException) {
            if ($queryException->errorInfo[1] === 1062) { // Duplicate entry
                return Redirect::back()
                    ->withInput()
                    ->withErrors(['name' => 'You already have a watchlist with this name.']);
            }

            throw $queryException;
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Watchlist $watchlist): Response
    {
        return inertia('Watchlists/Show', [
            'watchlist' => $watchlist->loadMissing('assets'),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id): void
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): void
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): void
    {
        //
    }

    /**
     * Get the validation rules for watchlist creation/update.
     *
     * @return array<string, string>
     */
    private function getValidationRules(): array
    {
        $typeValues = array_map(fn (WatchlistType $case) => $case->value, WatchlistType::cases());

        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'type' => 'required|string|in:'.implode(',', $typeValues),
        ];
    }
}
