<?php

declare(strict_types=1);

namespace App\Events;

use App\Models\Plan;
use Carbon\CarbonImmutable;
use App\Enums\SubscriptionStatus;
use Illuminate\Broadcasting\Channel;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

final class PlanDeleted implements ShouldBroadcast
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    /**
     * The event name for broadcasting and listeners.
     */
    public const string EVENT_NAME = 'plan.deleted';

    /** @var array<string, mixed> */
    public readonly array $webhookPayload;

    /**
     * The event timestamp.
     */
    public readonly CarbonImmutable $timestamp;

    /** @var array<string, mixed> */
    public readonly array $metadata;

    /**
     * The deletion type (soft_delete, hard_delete, deactivate).
     */
    public readonly string $deletionType;

    /**
     * Whether active subscriptions will be affected.
     */
    public readonly bool $affectsSubscriptions;

    /**
     * Create a new event instance.
     */
    public function __construct(
        /**
         * The plan instance (before deletion/deactivation).
         */
        public readonly Plan $plan,
        /** @param array<string, mixed> $webhookPayload */
        array $webhookPayload = [],
        /** @param array<string, mixed> $metadata */
        array $metadata = [])
    {
        $this->webhookPayload = $this->validateWebhookPayload($webhookPayload);
        $this->timestamp = CarbonImmutable::now();
        $this->deletionType = $metadata['deletion_type'] ?? 'deactivate';
        $this->affectsSubscriptions = $this->calculateSubscriptionImpact($this->plan);
        $this->metadata = array_merge([
            'trigger' => 'webhook',
            'event_id' => $webhookPayload['event_id'] ?? $webhookPayload['alert_id'] ?? 'unknown',
            'event_type' => $webhookPayload['event_type'] ?? $webhookPayload['alert_name'] ?? 'unknown',
            'source' => 'paddle',
            'deletion_type' => $this->deletionType,
            'affects_subscriptions' => $this->affectsSubscriptions,
            'subscription_count' => $this->plan->subscriptions()->count(),
            'features_count' => $this->plan->features()->count(),
        ], $metadata);
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('plans.'.$this->plan->id),
            new Channel('plans.global'),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return self::EVENT_NAME;
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        return [
            'plan' => $this->plan->toBroadcastArray(),
            'deletion_type' => $this->deletionType,
            'affects_subscriptions' => $this->affectsSubscriptions,
            'subscription_count' => $this->metadata['subscription_count'],
            'features_count' => $this->metadata['features_count'],
            'metadata' => $this->sanitizeMetadata($this->metadata),
            'timestamp' => $this->timestamp->toISOString(),
        ];
    }

    /**
     * Determine if this event should broadcast.
     */
    public function broadcastWhen(): bool
    {
        return config('broadcasting.default') !== 'log';
    }

    /**
     * Get the event name for event listeners.
     */
    public function getEventName(): string
    {
        return self::EVENT_NAME;
    }

    /**
     * Check if this was a hard deletion.
     */
    public function isHardDelete(): bool
    {
        return $this->deletionType === 'hard_delete';
    }

    /**
     * Check if this was a soft deletion.
     */
    public function isSoftDelete(): bool
    {
        return $this->deletionType === 'soft_delete';
    }

    /**
     * Check if this was a deactivation.
     */
    public function isDeactivation(): bool
    {
        return $this->deletionType === 'deactivate';
    }

    /**
     * Get the number of affected subscriptions.
     */
    public function getAffectedSubscriptionCount(): int
    {
        return $this->metadata['subscription_count'] ?? 0;
    }

    /**
     * Get the number of associated features.
     */
    public function getFeaturesCount(): int
    {
        return $this->metadata['features_count'] ?? 0;
    }

    /**
     * Calculate whether this deletion affects active subscriptions.
     */
    private function calculateSubscriptionImpact(Plan $plan): bool
    {
        if ($this->deletionType === 'hard_delete') {
            return $plan->subscriptions()->exists();
        }

        if ($this->deletionType === 'soft_delete') {
            return $plan->subscriptions()->exists();
        }

        // For deactivation, check if there are active subscriptions
        return $plan->subscriptions()
            ->where('status', SubscriptionStatus::ACTIVE)
            ->exists();
    }

    /**
     * Validate webhook payload.
     *
     * @param  array<string, mixed>  $payload
     * @return array<string, mixed>
     */
    private function validateWebhookPayload(array $payload): array
    {
        // Basic validation - ensure payload is safe
        return array_filter($payload, fn ($value, $key): bool => $key !== '' && (is_string($value) || is_numeric($value) || is_bool($value) || is_array($value)), ARRAY_FILTER_USE_BOTH);
    }

    /**
     * Sanitize metadata for broadcasting.
     *
     * @param  array<string, mixed>  $metadata
     * @return array<string, mixed>
     */
    private function sanitizeMetadata(array $metadata): array
    {
        // Remove sensitive information from broadcast
        $sensitiveKeys = ['api_key', 'secret', 'token', 'password', 'webhook_secret'];

        return array_filter($metadata, fn ($key): bool => ! in_array(mb_strtolower($key), $sensitiveKeys), ARRAY_FILTER_USE_KEY);
    }
}
