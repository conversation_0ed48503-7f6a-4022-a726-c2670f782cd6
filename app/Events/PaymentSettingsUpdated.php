<?php

declare(strict_types=1);

namespace App\Events;

use Illuminate\Support\Facades\Auth;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;

final class PaymentSettingsUpdated
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public function __construct(
        /** @var array<string, mixed> */
        public array $settings,
        public int|string|null $userId = null,
        public ?string $ipAddress = null
    ) {
        $this->userId = $userId ?? (Auth::id() ?? null);
        $this->ipAddress = $ipAddress ?? (request() ? request()->ip() : null);
    }

    /**
     * @return array<string, mixed>|null
     */
    public function getPreviousSettings(): ?array
    {
        return cache()->get('payment_settings_previous');
    }

    /**
     * @return array<int, PrivateChannel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('payment-settings'),
        ];
    }
}
