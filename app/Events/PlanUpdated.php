<?php

declare(strict_types=1);

namespace App\Events;

use App\Models\Plan;
use Carbon\CarbonImmutable;
use Illuminate\Broadcasting\Channel;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

final class PlanUpdated implements ShouldBroadcast
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    /**
     * The event name for broadcasting and listeners.
     */
    public const string EVENT_NAME = 'plan.updated';

    /**
     * The webhook payload that triggered this event.
     *
     * @var array<string, mixed>
     */
    public readonly array $webhookPayload;

    /**
     * The event timestamp.
     */
    public readonly CarbonImmutable $timestamp;

    /**
     * The processing metadata.
     *
     * @var array<string, mixed>
     */
    public readonly array $metadata;

    /**
     * The fields that were changed in this update.
     *
     * @var array<string, mixed>
     */
    public readonly array $changedFields;

    /**
     * Create a new event instance.
     */
    public function __construct(/**
     * The plan instance.
     */
        public readonly Plan $plan, /**
     * The previous plan values before the update.
     */
        /** @param array<string, mixed> $previousValues */
        public readonly array $previousValues = [],
        /** @param array<string, mixed> $webhookPayload */
        array $webhookPayload = [],
        /** @param array<string, mixed> $metadata */
        array $metadata = [])
    {
        $this->webhookPayload = $this->validateWebhookPayload($webhookPayload);
        $this->timestamp = CarbonImmutable::now();
        $this->changedFields = $this->detectChangedFields($this->previousValues);
        $this->metadata = array_merge([
            'trigger' => 'webhook',
            'event_id' => $webhookPayload['event_id'] ?? $webhookPayload['alert_id'] ?? 'unknown',
            'event_type' => $webhookPayload['event_type'] ?? $webhookPayload['alert_name'] ?? 'unknown',
            'source' => 'paddle',
            'changed_fields' => $this->changedFields,
        ], $metadata);
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('plans.'.$this->plan->id),
            new Channel('plans.global'),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return self::EVENT_NAME;
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        return [
            'plan' => $this->plan->toBroadcastArray(),
            'previous_values' => $this->sanitizePreviousValues($this->previousValues),
            'changed_fields' => $this->changedFields,
            'metadata' => $this->sanitizeMetadata($this->metadata),
            'timestamp' => $this->timestamp->toISOString(),
        ];
    }

    /**
     * Determine if this event should broadcast.
     */
    public function broadcastWhen(): bool
    {
        return config('broadcasting.default') !== 'log';
    }

    /**
     * Get the event name for event listeners.
     */
    public function getEventName(): string
    {
        return self::EVENT_NAME;
    }

    /**
     * Check if a specific field was changed in this update.
     */
    public function wasFieldChanged(string $field): bool
    {
        return in_array($field, $this->changedFields) || in_array('all', $this->changedFields);
    }

    /**
     * Check if the price was changed.
     */
    public function wasPriceChanged(): bool
    {
        return $this->wasFieldChanged('price');
    }

    /**
     * Check if the plan status (active/inactive) was changed.
     */
    public function wasStatusChanged(): bool
    {
        return $this->wasFieldChanged('active');
    }

    /**
     * Detect which fields were changed in this update.
     *
     * @param  array<string, mixed>  $previousValues
     * @return array<string>
     */
    private function detectChangedFields(array $previousValues): array
    {
        if ($previousValues === []) {
            return ['all'];
        }

        $changed = [];
        $comparableFields = ['key', 'name', 'description', 'price', 'currency', 'active'];

        foreach ($comparableFields as $field) {
            if (filled($previousValues[$field]) && $previousValues[$field] !== $this->plan->$field) {
                $changed[] = $field;
            }
        }

        return $changed;
    }

    /**
     * Validate webhook payload.
     *
     * @param  array<string, mixed>  $payload
     * @return array<string, mixed>
     */
    private function validateWebhookPayload(array $payload): array
    {
        // Basic validation - ensure payload is safe
        return array_filter($payload, fn ($value, $key): bool => is_string($key) && (is_string($value) || is_numeric($value) || is_bool($value) || is_array($value)), ARRAY_FILTER_USE_BOTH);
    }

    /**
     * Sanitize metadata for broadcasting.
     *
     * @param  array<string, mixed>  $metadata
     * @return array<string, mixed>
     */
    private function sanitizeMetadata(array $metadata): array
    {
        // Remove sensitive information from broadcast
        $sensitiveKeys = ['api_key', 'secret', 'token', 'password', 'webhook_secret'];

        return array_filter($metadata, fn ($key): bool => ! in_array(mb_strtolower($key), $sensitiveKeys), ARRAY_FILTER_USE_KEY);
    }

    /**
     * Sanitize previous values for broadcasting.
     *
     * @param  array<string, mixed>  $previousValues
     * @return array<string, mixed>
     */
    private function sanitizePreviousValues(array $previousValues): array
    {
        // Only include safe fields in broadcast
        $safeFields = ['key', 'name', 'description', 'price', 'currency', 'active'];

        return array_intersect_key($previousValues, array_flip($safeFields));
    }
}
