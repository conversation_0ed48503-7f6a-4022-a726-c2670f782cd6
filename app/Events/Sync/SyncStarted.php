<?php

declare(strict_types=1);

namespace App\Events\Sync;

use Illuminate\Queue\SerializesModels;
use Illuminate\Foundation\Events\Dispatchable;

final class SyncStarted
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public readonly string $syncType,
        public readonly ?int $userId = null,
        public readonly array $metadata = []
    ) {}
}
