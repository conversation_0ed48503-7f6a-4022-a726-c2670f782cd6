<?php

declare(strict_types=1);

namespace App\Events\Sync;

use Illuminate\Queue\SerializesModels;
use Illuminate\Foundation\Events\Dispatchable;

final class BatchProcessed
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public readonly string $syncType,
        public readonly int $batchNumber,
        public readonly int $batchSize,
        public readonly int $processedCount,
        public readonly int $failedCount,
        public readonly array $metadata = []
    ) {}
}
