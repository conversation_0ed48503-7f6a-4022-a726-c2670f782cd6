<?php

declare(strict_types=1);

namespace App\Events\Sync;

use Illuminate\Queue\SerializesModels;
use Illuminate\Foundation\Events\Dispatchable;

final class SyncCompleted
{
    use Dispatchable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public readonly string $syncType,
        public readonly ?int $userId = null,
        public readonly int $processedCount = 0,
        public readonly int $failedCount = 0,
        public readonly float $duration = 0.0,
        public readonly array $metadata = []
    ) {}
}
