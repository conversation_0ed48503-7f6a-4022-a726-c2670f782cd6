<?php

declare(strict_types=1);

namespace App\Events;

use App\Models\User;
use App\Models\Transaction;
use App\Models\Subscription;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;

final class PaymentFailureDetected
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public function __construct(
        public readonly int $transactionId,
        public readonly ?int $userId,
        public readonly ?int $subscriptionId,
        public readonly ?float $amount,
        public readonly string $currency,
        public readonly string $failureReason,
        public readonly ?array $failureDetails,
        public readonly string $correlationId,
        public readonly array $metadata = []
    ) {}

    public function transaction(): ?Transaction
    {
        return Transaction::query()->find($this->transactionId);
    }

    public function user(): ?User
    {
        return $this->userId !== null && $this->userId !== 0 ? User::query()->find($this->userId) : null;
    }

    public function subscription(): ?Subscription
    {
        return $this->subscriptionId !== null && $this->subscriptionId !== 0 ? Subscription::query()->find($this->subscriptionId) : null;
    }

    public function shouldAlert(): bool
    {
        // Alert for high-value failures or repeated failures
        return ($this->amount && $this->amount > 100) ||
               ($this->subscription() && $this->subscription()->payment_failure_count > 2) ||
               in_array($this->failureReason, ['insufficient_funds', 'card_declined', 'fraud_blocked']);
    }

    public function isRetryable(): bool
    {
        return ! in_array($this->failureReason, [
            'fraud_blocked',
            'blacklisted',
            'account_closed',
            'payment_method_disabled',
        ]);
    }

    public function getSeverity(): string
    {
        return match ($this->failureReason) {
            'insufficient_funds', 'card_declined' => 'medium',
            'fraud_blocked', 'blacklisted' => 'high',
            'network_error', 'timeout' => 'low',
            default => 'medium',
        };
    }

    public function toArray(): array
    {
        return [
            'transaction_id' => $this->transactionId,
            'user_id' => $this->userId,
            'subscription_id' => $this->subscriptionId,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'failure_reason' => $this->failureReason,
            'failure_details' => $this->failureDetails,
            'correlation_id' => $this->correlationId,
            'metadata' => $this->metadata,
            'should_alert' => $this->shouldAlert(),
            'is_retryable' => $this->isRetryable(),
            'severity' => $this->getSeverity(),
        ];
    }

    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('user.'.$this->userId),
            new PrivateChannel('admin.payment-failures'),
        ];
    }

    public function broadcastAs(): string
    {
        return 'payment.failure.detected';
    }
}
