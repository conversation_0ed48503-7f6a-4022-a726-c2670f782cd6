<?php

declare(strict_types=1);

namespace App\Events;

use Illuminate\Queue\SerializesModels;
use Illuminate\Foundation\Events\Dispatchable;

final readonly class SubscriptionPlanChanged
{
    use Dispatchable;
    use SerializesModels;

    public function __construct(
        public int $userId,
        public int $subscriptionId,
        public int $fromPlanId,
        public int $toPlanId,
        public string $processorKey,
        public ?float $proratedAmount = null,
        public ?float $creditAmount = null
    ) {}
}
