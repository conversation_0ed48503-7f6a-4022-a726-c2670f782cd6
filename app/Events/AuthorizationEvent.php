<?php

declare(strict_types=1);

namespace App\Events;

use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Queue\SerializesModels;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

final class AuthorizationEvent implements ShouldBroadcast
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public ?User $user;

    public ?string $ipAddress;

    public ?string $userAgent;

    public ?string $sessionId;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public string $action,
        public ?Model $subject = null,
        ?User $user = null,
        /** @var array<string, mixed> */
        public array $context = [],
        ?string $ipAddress = null,
        ?string $userAgent = null,
        ?string $sessionId = null
    ) {
        $this->user = $user ?? auth()->user();
        $this->ipAddress = $ipAddress ?? request()->ip();
        $this->userAgent = $userAgent ?? request()->userAgent();
        $this->sessionId = $sessionId ?? session()->getId();
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('authorization.'.($this->user->id ?? 'system')),
        ];
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        return [
            'action' => $this->action,
            'subject_type' => $this->subject instanceof Model ? $this->subject::class : null,
            'subject_id' => $this->subject?->getKey(),
            'user_id' => $this->user?->id,
            'timestamp' => now()->toISOString(),
            'context' => $this->context,
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'authorization.activity';
    }
}
