<?php

declare(strict_types=1);

namespace App\Contracts\Subscription;

use App\Models\Plan;
use Carbon\CarbonInterface;
use App\Models\Subscription;
use App\Enums\SubscriptionStatus;
use Illuminate\Database\Eloquent\Model;

/**
 * Interface for generic subscription operations across payment processors.
 * This abstraction allows the User model to work with subscriptions
 * without being tied to a specific payment processor implementation.
 */
interface SubscriptionInterface
{
    /**
     * Get the subscription model.
     */
    public function getSubscription();

    /**
     * Get the subscription ID.
     */
    public function getId(): ?string;

    /**
     * Get the billable id from the subscription
     */
    public function getBillableId(): ?int;

    /**
     * Get the subscription status.
     */
    public function getStatus(): SubscriptionStatus;

    /**
     * Get the portal URL.
     */
    public function getPortalUrl(): ?string;

    /**
     * Check if the subscription is active.
     */
    public function isActive(): bool;

    /**
     * Check if the subscription is on trial.
     */
    public function isOnTrial(): bool;

    /**
     * Check if the subscription is cancelled.
     */
    public function isCancelled(): bool;

    /**
     * Check if the subscription is on grace period.
     */
    public function isOnGracePeriod(): bool;

    /**
     * Get the subscription ends at date.
     */
    public function getEndsAt(): ?CarbonInterface;

    /**
     * Get the trial ends at date.
     */
    public function getTrialEndsAt(): ?CarbonInterface;

    /**
     * Get the subscription's plan identifier.
     * This could be type, plan_id, or processor-specific identifier.
     */
    public function getPlanIdentifier(): ?int;

    /**
     * Get the subscription's processor name (paddle, stripe, etc.).
     */
    public function getProcessorName(): string;

    /**
     * Get the processor-specific subscription ID.
     */
    public function getProcessorSubscriptionId(): ?string;

    /**
     * Get the subscription's creation date.
     */
    public function getCreatedAt(): CarbonInterface;

    /**
     * Cancel the subscription.
     */
    public function cancel(): self;

    /**
     * Resume a cancelled subscription.
     */
    public function resume(): self;

    /**
     * Change the subscription plan.
     */
    public function changePlan(Plan $newPlan): bool;

    /**
     * Get the subscription type/name (e.g., plan name).
     */
    public function getPlanName(): ?string;

    /**
     * Get the subscription amount/price.
     */
    public function getAmount(): ?float;

    /**
     * Get the subscription currency.
     */
    public function getCurrency(): ?string;

    /**
     * Get the subscription interval (monthly, yearly, etc.).
     */
    public function getInterval(): ?string;

    /**
     * Get metadata associated with the subscription.
     *
     * @return array<string, mixed>
     */
    public function getMetadata(): array;

    /**
     * Get the current billing period start date.
     */
    public function getCurrentPeriodStart(): ?CarbonInterface;

    /**
     * Get the current billing period end date.
     */
    public function getCurrentPeriodEnd(): ?CarbonInterface;

    /**
     * Get the associated plan for this subscription.
     * This establishes the relationship by matching the subscription's 'type'
     * attribute to the plan's 'key' attribute.
     */
    public function getPlan(): ?Plan;

    /**
+     * Get the payment processor instance.
+     *
+     * @return object The processor implementation (e.g., Stripe, Paddle client)
+     */
    public function getProcessor(): mixed;
}
