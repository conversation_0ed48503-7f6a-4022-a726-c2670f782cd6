<?php

declare(strict_types=1);

namespace App\Contracts\Sync;

/**
 * Interface for cache management in sync operations
 *
 * Defines the contract for managing cached data during
 * synchronization operations to improve performance.
 */
interface CacheManagerInterface
{
    /**
     * Get cached data with fallback to callback
     *
     * @param  string  $key  Cache key
     * @param  callable  $fallback  Function to call if cache miss
     * @param  int|null  $ttl  Time to live in seconds (null for default)
     * @return mixed Cached or computed data
     */
    public function remember(string $key, callable $fallback, ?int $ttl = null): mixed;

    /**
     * Store data in cache
     *
     * @param  string  $key  Cache key
     * @param  mixed  $data  Data to cache
     * @param  int|null  $ttl  Time to live in seconds (null for default)
     * @return bool Success status
     */
    public function put(string $key, mixed $data, ?int $ttl = null): bool;

    /**
     * Get data from cache
     *
     * @param  string  $key  Cache key
     * @param  mixed  $default  Default value if not found
     * @return mixed Cached data or default
     */
    public function get(string $key, mixed $default = null): mixed;

    /**
     * Check if data exists in cache
     *
     * @param  string  $key  Cache key
     */
    public function has(string $key): bool;

    /**
     * Remove data from cache
     *
     * @param  string  $key  Cache key
     * @return bool Success status
     */
    public function forget(string $key): bool;

    /**
     * Clear all cache entries for a specific type
     *
     * @param  string  $type  Cache type identifier
     * @return bool Success status
     */
    public function clearType(string $type): bool;

    /**
     * Get cache statistics
     *
     * @return array{hits: int, misses: int, hit_rate: float, size: int}
     */
    public function getStats(): array;
}
