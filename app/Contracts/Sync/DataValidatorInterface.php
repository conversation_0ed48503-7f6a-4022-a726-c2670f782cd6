<?php

declare(strict_types=1);

namespace App\Contracts\Sync;

/**
 * Interface for data validation in sync operations
 *
 * Defines the contract for validating data consistency and integrity
 * during synchronization operations.
 */
interface DataValidatorInterface
{
    /**
     * Validate subscription data structure
     *
     * @param  array  $data  Subscription data to validate
     * @return array{valid: bool, errors: string[], warnings: string[]}
     */
    public function validateSubscriptionStructure(array $data): array;

    /**
     * Validate business rules for subscriptions
     *
     * @param  array  $data  Subscription data to validate
     * @return array{valid: bool, errors: string[], warnings: string[]}
     */
    public function validateBusinessRules(array $data): array;

    /**
     * Validate data consistency between systems
     *
     * @param  array  $localData  Local database data
     * @param  array  $remoteData  Remote processor data
     * @return array{consistent: bool, differences: array[], fixes: array}
     */
    public function validateDataConsistency(array $localData, array $remoteData): array;

    /**
     * Detect and suggest fixes for data issues
     *
     * @param  array  $issues  List of detected issues
     * @return array{fixes: array, manual_intervention: bool}
     */
    public function suggestFixes(array $issues): array;

    /**
     * Apply automatic fixes to data issues
     *
     * @param  array  $data  Data to fix
     * @param  array  $fixes  Fixes to apply
     * @return array{success: bool, fixed_data: array, remaining_issues: array}
     */
    public function applyFixes(array $data, array $fixes): array;
}
