<?php

declare(strict_types=1);

namespace App\Contracts\Sync;

/**
 * Interface for batch processing operations
 *
 * Defines the contract for processing records in batches
 * to optimize performance and resource usage.
 */
interface BatchProcessorInterface
{
    /**
     * Process records in batches
     *
     * @param  iterable  $records  Records to process
     * @param  callable  $processor  Function to process each record
     * @param  int  $batchSize  Number of records per batch
     * @return array{success: bool, processed: int, failed: int, errors: string[]}
     */
    public function processInBatches(iterable $records, callable $processor, int $batchSize = 100): array;

    /**
     * Process a single batch of records
     *
     * @param  array  $records  Records in this batch
     * @param  callable  $processor  Function to process each record
     * @return array{success: bool, processed: int, failed: int, errors: string[]}
     */
    public function processBatch(array $records, callable $processor): array;

    /**
     * Get the optimal batch size for the current operation
     *
     * @param  string  $operationType  Type of operation being performed
     * @return int Optimal batch size
     */
    public function getOptimalBatchSize(string $operationType): int;

    /**
     * Track batch processing metrics
     *
     * @param  array  $metrics  Processing metrics to track
     */
    public function trackMetrics(array $metrics): void;
}
