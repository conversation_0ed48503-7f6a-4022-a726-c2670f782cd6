<?php

declare(strict_types=1);

namespace App\Contracts\PaymentProcessor;

use App\Models\User;
use App\Models\Customer;

interface PaymentMethodSyncServiceInterface
{
    /**
     * Synchronize payment methods for all users from payment processors to local database.
     *
     * @return array{success: bool, synced: int, errors: int, dry_run: bool, message: string}
     */
    public function syncAllPaymentMethods(bool $dryRun = false): array;

    /**
     * Synchronize payment methods for a specific user.
     *
     * @return array{success: bool, synced: int, errors: int, dry_run: bool, message: string}
     */
    public function syncUserPaymentMethods(User $user, bool $dryRun = false): array;

    /**
     * Synchronize payment methods for a specific customer.
     *
     * @return array{success: bool, synced: int, errors: int, dry_run: bool, message: string}
     */
    public function syncCustomerPaymentMethods(Customer $customer, bool $dryRun = false): array;

    /**
     * Cleanup orphaned payment method records.
     *
     * @return array{cleaned: int, remaining: int}
     */
    public function cleanupOrphanedPaymentMethods(): array;

    /**
     * Get the current sync status.
     *
     * @return array{total_payment_methods: int, synced_payment_methods: int, last_sync: string|null, status: string}
     */
    public function getSyncStatus(): array;
}
