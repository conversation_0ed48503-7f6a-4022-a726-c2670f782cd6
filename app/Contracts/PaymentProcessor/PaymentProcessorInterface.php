<?php

declare(strict_types=1);

namespace App\Contracts\PaymentProcessor;

use App\Models\Plan;
use App\Models\User;
use App\Models\Customer;
use Illuminate\Http\Request;
use Lara<PERSON>\Cashier\Cashier;
use App\Models\PaymentMethod;
use App\ValueObjects\PaymentProcessor\WebhookData;
use App\ValueObjects\PaymentProcessor\CheckoutData;
use App\ValueObjects\PaymentProcessor\ValidationData;
use App\ValueObjects\PaymentProcessor\SubscriptionData;

/**
 * Interface for payment processor implementations.
 */
interface PaymentProcessorInterface
{
    /**
     * Get the processor name.
     */
    public function getName(): string;

    /**
     * Create a checkout session for a plan.
     *
     * @param  array<string, mixed>  $options
     */
    public function createCheckout(User $user, Plan $plan, array $options = []): ?CheckoutData;

    /**
     * Create a customer portal session.
     */
    public function createCustomerPortalSession(string $customerId): mixed;

    /**
     * Handle webhook requests from the payment processor.
     */
    public function handleWebhook(Request $request): WebhookData;

    /**
     * Verify webhook signature.
     */
    public function verifyWebhookSignature(Request $request): bool;

    /**
     * Get subscription data by ID.
     */
    public function getSubscription(string $subscriptionId): ?SubscriptionData;

    /**
     * Cancel a subscription.
     */
    public function cancelSubscription(string $subscriptionId): bool;

    /**
     * Resume a cancelled subscription.
     */
    public function resumeSubscription(string $subscriptionId): bool;

    /**
     * Update subscription plan.
     */
    public function updateSubscriptionPlan(string $subscriptionId, Plan $newPlan): bool;

    /**
     * Sync plans from the payment processor.
     *
     * @return array<string, mixed>
     */
    public function syncPlans(): array;

    /**
     * Create or update a plan in the payment processor.
     */
    public function syncPlan(Plan $plan): bool;

    /**
     * Get customer ID for a user.
     */
    public function getCustomerId(User $user): ?string;

    /**
     * Create a customer in the payment processor.
     */
    public function createCustomer(User $user): string;

    /**
     * Update customer information.
     */
    public function updateCustomer(User $user): bool;

    /**
     * Get customer data from the payment processor.
     *
     * @return array<string, mixed>|null
     */
    public function getCustomer(string $customerId): ?array;

    /**
     * Get all subscriptions for a customer.
     *
     * @return array<int, array<string, mixed>>
     */
    public function getCustomerSubscriptions(string $customerId): array;

    /**
     * Create a setup intent for updating payment methods.
     *
     * @param  array<string, mixed>  $options
     * @return array<string, mixed>
     */
    public function createSetupIntent(string $customerId, array $options = []): array;

    /**
     * Get payment methods for a customer.
     *
     * @return array<int, array<string, mixed>>
     */
    public function getPaymentMethods(string $customerId): array;

    /**
     * Check if the processor is properly configured.
     */
    public function isConfigured(): bool;

    /**
     * Get processor-specific configuration.
     *
     * @return array<string, mixed>
     */
    public function getConfig(): array;

    /**
     * Get the transaction model for the specified processor.
     */
    public function getTransactionModel(): ?string;

    /**
     * Validate a payment method.
     *
     * @param  PaymentMethod  $paymentMethod  The payment method to validate
     * @return ValidationData The validation result
     */
    public function validatePaymentMethod(Customer $customer, PaymentMethod $paymentMethod): ValidationData;

    /**
     * Get the Cashier instance for the processor.
     */
    public function getCashier(): Cashier;

    /**
     * Get the payment method sync service for the processor.
     */
    public function getPaymentSyncService(): ?PaymentMethodSyncServiceInterface;

    /**
     * Get the customer sync service for the processor.
     */
    public function getCustomerSyncService(): ?CustomerSyncServiceInterface;

    /**
     * Get the subscription sync service for the processor.
     */
    public function getSubscriptionSyncService(): ?SubscriptionSyncServiceInterface;

    /**
     * Retry a failed checkout operation.
     */
    public function retryCheckout(string $transactionId): array;

    /**
     * Retry a failed subscription change operation.
     */
    public function retrySubscriptionChange(string $transactionId): array;

    /**
     * Retry a failed payment method update operation.
     */
    public function retryPaymentMethodUpdate(string $transactionId): array;
}
