<?php

declare(strict_types=1);

namespace App\Contracts\PaymentProcessor;

use Exception;

/**
 * Interface for subscription synchronization services
 *
 * Defines the contract for synchronizing subscription data between
 * payment processors and the local database following SOLID principles.
 */
interface SubscriptionSyncServiceInterface
{
    /**
     * Synchronize subscriptions for a specific user
     *
     * @param  int  $userId  The user ID to sync subscriptions for
     * @param  bool  $dryRun  Whether to run in dry-run mode
     * @return array{success: bool, processed: int, failed: int, errors: string[]}
     */
    public function syncUserSubscriptions(int $userId, bool $dryRun = false): array;

    /**
     * Synchronize all subscriptions (batch operation)
     *
     * @param  bool  $dryRun  Whether to run in dry-run mode
     * @param  int|null  $batchSize  Number of records per batch (null for default)
     * @return array{success: bool, processed: int, failed: int, errors: string[]}
     */
    public function syncAllSubscriptions(bool $dryRun = false, ?int $batchSize = null): array;

    /**
     * Synchronize subscription plans from payment processor
     *
     * @param  bool  $dryRun  Whether to run in dry-run mode
     * @return array{success: bool, processed: int, failed: int, errors: string[]}
     */
    public function syncSubscriptionPlans(bool $dryRun = false): array;

    /**
     * Validate subscription data consistency
     *
     * @param  int  $subscriptionId  The subscription ID to validate
     * @return array{valid: bool, issues: string[], fixes: array}
     */
    public function validateSubscriptionData(int $subscriptionId): array;

    /**
     * Clean up orphaned subscription records
     *
     * @param  bool  $dryRun  Whether to run in dry-run mode
     * @return array{success: bool, cleaned: int, errors: string[]}
     */
    public function cleanupOrphanedRecords(bool $dryRun = false): array;

    /**
     * Get synchronization status for a user
     *
     * @param  int  $userId  The user ID to check status for
     * @return array{last_sync: string|null, status: string, next_sync: string|null}
     */
    public function getSyncStatus(int $userId): array;

    /**
     * Handle synchronization errors with retry logic
     *
     * @param  Exception  $exception  The exception that occurred
     * @param  array  $context  Context information about the error
     * @return bool Whether the operation should be retried
     */
    public function handleError(Exception $exception, array $context): bool;
}
