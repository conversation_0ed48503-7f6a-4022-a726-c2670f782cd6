<?php

declare(strict_types=1);

namespace App\Contracts\PaymentProcessor;

use Illuminate\Console\Command;

interface CustomerSyncServiceInterface
{
    /**
     * Synchronize customers from a payment processor to local database.
     *
     * @return array{success: bool, synced: int, errors: int, dry_run: bool, message: string}
     */
    public function syncCustomers(bool $dryRun = false): array;

    /**
     * Cleanup orphaned customer records.
     *
     * @return array{cleaned: int, remaining: int}
     */
    public function cleanupOrphanedCustomers(): array;

    /**
     * Get the current sync status.
     *
     * @return array{before: int, after: int, new: int, updated: int}
     */
    public function getSyncStatus(): array;

    /**
     * Set the command instance.
     */
    public function setCommand(Command $command): void;
}
