<?php

declare(strict_types=1);

namespace App\Contracts\PaymentProcessor;

interface PaymentProcessorFactoryInterface
{
    /**
     * Create a payment processor instance
     */
    public function create(?string $processor = null): PaymentProcessorInterface;

    /**
     * Reset the factory (useful for testing)
     */
    public function reset(): void;

    /**
     * Get all available processors
     *
     * @return array<string, class-string<PaymentProcessorInterface>>
     */
    public function getAllProcessors(): array;

    /**
     * Get enabled processors
     *
     * @return array<string, class-string<PaymentProcessorInterface>>
     */
    public function getEnabledProcessors(): array;

    /**
     * Check if a processor is registered
     */
    public function isRegistered(string $processor): bool;

    /**
     * Check if a processor is enabled
     */
    public function isEnabled(string $processor): bool;

    /**
     * Get a payment method sync service instance
     */
    public function getPaymentSyncService(string $processor): PaymentMethodSyncServiceInterface;

    public function getCustomerSyncService(string $processor): CustomerSyncServiceInterface;
}
