<?php

declare(strict_types=1);

namespace App\Jobs;

use Exception;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Contracts\PaymentProcessor\PaymentMethodSyncServiceInterface;

final class SyncPaymentMethodsJob implements ShouldQueue
{
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 300; // 5 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(
        private readonly ?int $userId = null,
        private readonly bool $cleanupOrphaned = false
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(PaymentMethodSyncServiceInterface $syncService): void
    {
        try {
            Log::info('Starting payment method synchronization job', [
                'user_id' => $this->userId,
                'cleanup_orphaned' => $this->cleanupOrphaned,
            ]);

            if ($this->userId !== null && $this->userId !== 0) {
                // Find the user by ID
                $user = User::query()->find($this->userId);
                throw_unless($user, new Exception("User with ID {$this->userId} not found"));

                // Sync payment methods for specific user
                $result = $syncService->syncUserPaymentMethods($user);

                Log::info('Completed payment method sync for user', [
                    'user_id' => $this->userId,
                    'result' => $result,
                ]);
            } else {
                // Sync payment methods for all users
                $result = $syncService->syncAllPaymentMethods();

                Log::info('Completed payment method sync for all users', [
                    'result' => $result,
                ]);
            }

            // Cleanup orphaned payment methods if requested
            if ($this->cleanupOrphaned) {
                $cleanupResult = $syncService->cleanupOrphanedPaymentMethods();

                Log::info('Completed orphaned payment method cleanup', [
                    'result' => $cleanupResult,
                ]);
            }

        } catch (Exception $exception) {
            Log::error('Payment method synchronization job failed', [
                'user_id' => $this->userId,
                'cleanup_orphaned' => $this->cleanupOrphaned,
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString(),
            ]);

            // Re-throw the exception to mark the job as failed
            throw $exception;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error('Payment method synchronization job permanently failed', [
            'user_id' => $this->userId,
            'cleanup_orphaned' => $this->cleanupOrphaned,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}
