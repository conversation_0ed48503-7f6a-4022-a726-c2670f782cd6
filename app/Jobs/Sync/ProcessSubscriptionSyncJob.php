<?php

declare(strict_types=1);

namespace App\Jobs\Sync;

use Throwable;
use RuntimeException;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Contracts\PaymentProcessor\SubscriptionSyncServiceInterface;
use App\Services\PaymentProcessor\Paddle\PaddleSubscriptionSyncService;
use App\Services\PaymentProcessor\Stripe\StripeSubscriptionSyncService;

final class ProcessSubscriptionSyncJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public int $tries = 3;

    public int $backoff = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public readonly string $processor,
        public readonly ?int $userId = null,
        public readonly ?int $batchSize = null
    ) {
        $this->onQueue('sync');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Processing subscription sync job', [
            'processor' => $this->processor,
            'user_id' => $this->userId,
            'batch_size' => $this->batchSize,
        ]);

        $service = $this->getService();

        if ($this->userId !== null && $this->userId !== 0) {
            $result = $service->syncUserSubscriptions($this->userId);
        } else {
            $result = $service->syncAllSubscriptions(false, $this->batchSize);
        }

        if (! $result['success']) {
            Log::error('Subscription sync job failed', [
                'processor' => $this->processor,
                'errors' => $result['errors'],
            ]);

            throw new RuntimeException('Subscription sync failed: '.implode(', ', $result['errors']));
        }

        $processed = data_get($result, 'processed', 0);
        $failed = data_get($result, 'failed', 0);

        Log::info('Subscription sync job completed', [
            'processor' => $this->processor,
            'processed' => $processed,
            'failed' => $failed,
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(Throwable $exception): void
    {
        Log::error('Subscription sync job permanently failed', [
            'processor' => $this->processor,
            'user_id' => $this->userId,
            'exception' => $exception->getMessage(),
        ]);
    }

    private function getService(): SubscriptionSyncServiceInterface
    {
        return match ($this->processor) {
            'paddle' => app(PaddleSubscriptionSyncService::class),
            'stripe' => app(StripeSubscriptionSyncService::class),
            default => throw new \InvalidArgumentException("Unknown processor: {$this->processor}"),
        };
    }
}
