<?php

declare(strict_types=1);

namespace App\Listeners\Sync;

use App\Events\Sync\SyncFailed;
use App\Events\Sync\SyncStarted;
use App\Events\Sync\SyncCompleted;
use App\Events\Sync\BatchProcessed;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

final class SyncEventListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle sync started event.
     */
    public function handleSyncStarted(SyncStarted $event): void
    {
        Log::info('Sync operation started', [
            'sync_type' => $event->syncType,
            'user_id' => $event->userId,
            'metadata' => $event->metadata,
        ]);
    }

    /**
     * Handle sync completed event.
     */
    public function handleSyncCompleted(SyncCompleted $event): void
    {
        Log::info('Sync operation completed', [
            'sync_type' => $event->syncType,
            'user_id' => $event->userId,
            'processed_count' => $event->processedCount,
            'failed_count' => $event->failedCount,
            'duration' => $event->duration,
            'metadata' => $event->metadata,
        ]);

        // Log warning if there were failures
        if ($event->failedCount > 0) {
            Log::warning('Sync completed with failures', [
                'sync_type' => $event->syncType,
                'failed_count' => $event->failedCount,
                'total_count' => $event->processedCount + $event->failedCount,
            ]);
        }
    }

    /**
     * Handle sync failed event.
     */
    public function handleSyncFailed(SyncFailed $event): void
    {
        Log::error('Sync operation failed', [
            'sync_type' => $event->syncType,
            'user_id' => $event->userId,
            'exception' => $event->exception->getMessage(),
            'trace' => $event->exception->getTraceAsString(),
            'metadata' => $event->metadata,
        ]);
    }

    /**
     * Handle batch processed event.
     */
    public function handleBatchProcessed(BatchProcessed $event): void
    {
        Log::debug('Batch processed', [
            'sync_type' => $event->syncType,
            'batch_number' => $event->batchNumber,
            'batch_size' => $event->batchSize,
            'processed_count' => $event->processedCount,
            'failed_count' => $event->failedCount,
            'metadata' => $event->metadata,
        ]);
    }

    /**
     * Register the listeners for the subscriber.
     */
    public function subscribe($events): array
    {
        return [
            SyncStarted::class => 'handleSyncStarted',
            SyncCompleted::class => 'handleSyncCompleted',
            SyncFailed::class => 'handleSyncFailed',
            BatchProcessed::class => 'handleBatchProcessed',
        ];
    }
}
