# Kaikyo Project Rules

## Project Overview

This is a modern Laravel SaaS application built on the Kaikyo starter kit, featuring a Vue.js frontend with Inertia.js, containerized with Docker, and designed for trading signals and stock market data analysis.

## Technology Stack

### Backend

- **Framework**: Laravel 11+ with PHP 8.3+
- **Server**: Docker compose -> FrankenPHP with Laravel Octane for high performance
- **DevServers**: ALWAYS use either existing "make" commands or docker compose commands to run development tasks, tests or debug code
- **Database**: PostgreSQL 17
- **Cache/Queue**: Redis
- **Authentication**: Laravel Jetstream with Fortify
- **API**: Laravel Sanctum for API authentication
- **Admin Panel**: FilamentPHP
- **Payments**: <PERSON>vel Cashier (Stripe/Paddle)
- **Documentation**: Scramble for API docs

### Frontend

- **Framework**: Vue.js 3.5+ with TypeScript
- **Build Tool**: Vite 6+ with HMR
- **Routing**: Inertia.js 2.0
- **Styling**: Nuxt UI components with TailwindCSS 4+
- **Package Manager**: Bun
- **State Management**: Pinia (via Nuxt UI)
- **Forms**: VeeValidate with Zod validation

### Development & Deployment

- **Containerization**: Docker with Docker Compose
- **Reverse Proxy**: Caddy
- **Environment**: Development and Production configurations
- **CI/CD**: GitHub Actions ready

## Code Quality Standards

### PHP Standards

- **SOLID Principles**: Always follow SOLID principles for maintainable and scalable code
- **Laravel First**: Always use laravel helpers and facades before any raw php functions
- **Strict Types**: All PHP files must declare `declare(strict_types=1)`
- **Code Style**: Laravel Pint with custom rules (see `pint.json`)
- **Static Analysis**: PHPStan at maximum level
- **Testing**: Pest PHP for unit and feature tests
- **Architecture**: Follow Laravel best practices and SOLID principles
- **MCP**: Use laravel boost MCP to query database and verify laravel code
- **Classes Communication**: Use Value Objects and Data Transfer Objects (DTOs) instead of raw arrays for any data transfer between layers
- **Status Handling**: Always use enum for local class and database statuses to ensure type safety and prevent invalid states

### TypeScript/JavaScript Standards

- **Linting**: ESLint with Antfu config
- **Formatting**: Prettier for consistent code formatting
- **Type Safety**: Strict TypeScript configuration
- **Vue**: Composition API! Never use options api. Always use Typescript.

## Development Workflow

### Environment Setup

1. Uses Docker Compose for consistent development environment
2. Run `make up` to start development services
3. Use `make dev` for development with logs
4. Access application at `https://kaikyo.test`
5. Vite dev server runs on `http://localhost:5173`
6. ALWAYS use make commands or docker compose (exec) to run lint, analyse, development tasks, tests or debug code NEVER run php in shell directly

### Available Commands

```bash
# Environment Management
make up          # Start development environment
make down        # Stop all services
make prod        # Start production environment
make dev         # Start with logs (foreground)

# Building
make build       # Build images
make build-no-cache  # Build without cache

# Application Management
make migrate     # Run migrations
make fresh       # Fresh migration with seeding
make test        # Run tests
make shell       # Open shell in web container

# Asset Management
make assets      # Build production assets
make watch       # Watch assets for development

# Optimization
make optimize    # Cache Laravel configuration
make clean       # Clean up containers and volumes
```

## File Structure & Organization

### Backend Structure

```
app/
├── Actions/          # Laravel Jetstream actions
├── Console/Commands/ # Artisan commands
├── Http/
│   ├── Controllers/  # HTTP controllers
│   └── Middleware/   # Custom middleware
├── Models/          # Eloquent models
├── Policies/        # Authorization policies
├── Providers/       # Service providers
├── Services/        # Business logic services
└── Jobs/           # Queue jobs
```

### Frontend Structure

```
resources/js/
├── Pages/          # Inertia.js pages
├── Layouts/        # Vue layouts
├── actions/        # API action helpers
├── lib/           # Utility functions
└── types/         # TypeScript type
```

## Coding Conventions

### PHP Conventions

- Use strict typing: `declare(strict_types=1)`
- Follow PSR-12 coding standards
- Use final classes where appropriate
- Implement proper error handling
- Use dependency injection
- Write comprehensive tests

### Vue/TypeScript Conventions

- Use Composition API with `<script setup lang="ts">`
- Implement proper TypeScript typing
- Use Zod for runtime validation
- Follow Vue 3 best practices
- Use Nuxt UI components consistently

### Database Conventions

- Use migrations for all schema changes
- Follow Laravel naming conventions
- Use proper indexing for performance
- Implement soft deletes where appropriate
- Use UUID for primary keys when needed

## Security Guidelines

### Authentication & Authorization

- Use Laravel Jetstream for user management
- Implement proper role-based access control with Spatie Permission
- Use Laravel Sanctum for API authentication
- Validate all user inputs
- Implement CSRF protection

### Data Protection

- Never commit secrets to version control
- Use environment variables for configuration
- Implement proper data validation
- Use HTTPS in production
- Follow OWASP security guidelines

## Testing Standards

### PHP Testing

- Use Pest PHP for all tests
- Write both unit, feature and browser e2e tests
- Maintain high test coverage
- Use factories for test data
- Mock external services

### Frontend Testing

- Test Vue components
- Test user interactions
- Use proper test data
- Mock API calls

## Performance Guidelines

### Backend Performance

- Use Laravel Octane for improved performance
- Implement proper caching strategies
- Use database indexing effectively
- Optimize database queries
- Use queue jobs for heavy operations

### Frontend Performance

- Use Vite for fast builds and HMR
- Implement code splitting
- Optimize images and assets
- Use proper caching headers
- Minimize bundle size

## API Development

### API Standards

- Follow RESTful conventions
- Use proper HTTP status codes
- Implement consistent error responses
- Use Laravel Sanctum for authentication
- Document APIs with Scramble

### API Versioning

- Version APIs appropriately
- Maintain backward compatibility
- Use semantic versioning
- Document breaking changes

### Docker Development

- Always check running containers with `docker compose ps` before issuing new commands
- DON'T EVER RUN docker compose up -d UNLESS docker compose ps RETURNS NO CONTAINERS
- Avoid starting duplicate containers - verify container status first
- If needed containers are already running, use existing containers instead of creating new ones
- Follow container naming

### Development Environment

- Always Use Docker Compose for local development
- Mount source code for live reloading

### Production Deployment

- Use optimized Docker images
- Implement health checks
- Use proper restart policies
- Configure SSL/TLS properly
- Monitor application performance

## Git Workflow

### Branch Strategy

- Use Worktrees for feature branches
- Use git-flow branch strategy for branch management
- Create pull requests for code reviews
- Use conventional commit messages
- Keep commits atomic and focused

### Commit Messages

- Use conventional commit format
- Include clear descriptions
- Reference issues when applicable
- Keep commits focused on single changes

## Documentation

### Code Documentation

- Document complex business logic
- Use PHPDoc for PHP methods
- Use JSDoc for TypeScript functions
- Keep documentation up to date

### API Documentation

- Use Scramble for automatic API documentation
- Document all endpoints
- Include request/response examples
- Keep documentation current

## Monitoring & Logging

### Application Monitoring

- Use Laravel Telescope for debugging
- Implement proper error logging
- Monitor application performance
- Set up alerts for critical issues

### Error Handling

- Implement comprehensive error handling
- Log errors appropriately
- Provide user-friendly error messages
- Monitor error rates

## Third-Party Integrations

### Payment Processing

- Use the Kaikyo Custom Payment System for subscriptions
- Support multiple payment providers
- Implement proper webhook handling
- Ensure PCI compliance

### AI Integration

- Use Prism PHP for LLM integrations
- Support multiple AI providers
- Implement proper rate limiting
- Handle API failures gracefully

### Market Data

- Integrate with EOD Historical Data
- Implement proper data caching
- Handle rate limits appropriately
- Ensure data accuracy

## Maintenance & Updates

### Regular Maintenance

- Keep dependencies updated
- Monitor security vulnerabilities
- Perform regular backups
- Clean up unused code

### Version Updates

- Test updates in staging environment
- Follow semantic versioning
- Document breaking changes
- Maintain changelog

## Team Collaboration

### Code Review

- All code must be reviewed
- Use pull request templates
- Provide constructive feedback
- Ensure code quality standards

### Communication

- Use clear commit messages
- Document architectural decisions
- Share knowledge through documentation
- Maintain project roadmap

These rules ensure consistent, maintainable, and scalable development practices for the Kaikyo project.

DON'T JUST CHANGE FILES HOWEVER YOU FEEL LIKE!!! The changes must make sense, be in accordance with MY request and the task at hand. READ the task carefully, follow ALL BEST PRACTICES and make sure you use DOCUMENTATION ALWAYS, or USE MCP SERVERS as they are there for this as well as the docs in YOUR CONTEXT!!!!!!!!!
