# Kaikyo Project Context

## Current State
Kaikyo is a mature Laravel/Vue.js SaaS application in active development. The platform provides trade signals and AI analysis for stock market traders with a comprehensive subscription-based business model.

## Recent Development Focus

### Core Platform Features
- **Subscription Management**: Multi-processor payment system supporting Stripe, Paddle, and PayPal
- **Real-time Market Data**: Integration with EOD Historical Data API for stock information
- **Watchlist System**: User-customizable stock tracking with real-time updates
- **Admin Panel**: Filament-based admin interface for user and content management

### Authentication & Security
- **Multi-factor Authentication**: TOTP support via Laravel Jetstream
- **Social OAuth Integration**: Google, GitHub, X, and GitLab login options
- **Magic Link Authentication**: Passwordless login functionality
- **Role-based Permissions**: Comprehensive admin/user permission system
- **User Impersonation**: Admin troubleshooting capabilities with safety safeguards

### Technical Infrastructure
- **Container-based Development**: Docker Compose with hot module replacement
- **High-performance Caching**: Redis (DragonflyDB) for sessions and caching
- **Queue System**: Laravel Horizon for background job processing
- **Database**: PostgreSQL 17 with optimized indexing

## Current Work Priorities

### Immediate Focus Areas
1. **Payment Processor Optimization**: Enhancing multi-processor subscription management
2. **Real-time Features**: WebSocket implementation for live market data updates
3. **Performance Optimization**: Database query optimization and caching strategies
4. **Testing Coverage**: Expanding E2E and feature test coverage
5. **UI/UX Improvements**: Frontend component refinement and user experience enhancements

### Technical Debt & Maintenance
- **Code Quality**: Maintaining high standards with Pint, Prettier, and static analysis
- **Security**: Regular security audits and dependency updates
- **Documentation**: Keeping API docs and architecture documentation current
- **Performance Monitoring**: Implementing comprehensive application monitoring

## Development Environment

### Local Setup
- **Docker Compose**: Development environment with hot reload
- **Multi-container Architecture**: Separate web, database, cache, and queue services
- **Asset Pipeline**: Vite with TypeScript and Hot Module Replacement
- **Database**: PostgreSQL with Redis for caching and queues

### Key Development Commands
```bash
# Start development environment
docker-compose up

# Install dependencies
composer install && bun install

# Run tests
php artisan test && bun run test

# Code formatting
composer format && bun run format
```

## Current Technical Challenges

### Subscription System Complexity
- Managing multiple payment processors with different webhook formats
- Ensuring subscription state consistency across processors
- Handling plan changes, trials, and cancellations gracefully

### Real-time Data Processing
- Implementing efficient WebSocket connections for live market data
- Caching strategies for high-frequency market data updates
- Managing API rate limits for external market data services

### Performance at Scale
- Optimizing database queries for large datasets
- Implementing effective caching strategies for market data
- Managing memory usage for real-time data processing

## Team Collaboration

### Development Workflow
- **Feature Development**: Branch-based development with pull requests
- **Code Review**: Peer review process for all code changes
- **Testing**: Comprehensive testing including unit, feature, and E2E tests
- **Deployment**: Container-based deployment with health checks

### Quality Assurance
- **Automated Testing**: Pest for PHP, Playwright for E2E testing
- **Code Quality**: Laravel Pint, ESLint, and static analysis tools
- **Security**: Regular security audits and dependency scanning
- **Performance**: Monitoring and profiling in development and production

## Business Context

### Market Position
Kaikyo targets retail traders and investment clubs seeking data-driven trading insights. The platform competes in the growing fintech SaaS space with differentiation through AI-powered analysis and user-friendly design.

### Revenue Model
- **Subscription Tiers**: Multiple pricing levels with different feature sets
- **Payment Processing**: Global payment support through multiple processors
- **Team Plans**: Collaborative features for investment groups
- **Free Tier**: Basic features to drive user acquisition

### Growth Strategy
- **User Acquisition**: Content marketing and partnerships in trading communities
- **Feature Expansion**: AI-powered analysis and predictive analytics
- **Platform Scalability**: Preparing infrastructure for user growth
- **International Expansion**: Multi-currency and multi-language support

## Next Steps

### Short-term Goals (1-3 months)
1. **Enhanced AI Features**: Implement more sophisticated trading algorithms
2. **Mobile Responsiveness**: Improve mobile experience for traders on-the-go
3. **Performance Optimization**: Reduce latency for real-time data updates
4. **User Onboarding**: Streamline the new user experience
5. **Analytics Dashboard**: Provide insights into trading performance

### Medium-term Goals (3-6 months)
1. **Advanced Charting**: Interactive technical analysis tools
2. **Social Features**: Community-driven insights and discussions
3. **API Expansion**: Public API for third-party integrations
4. **Machine Learning**: Implement predictive analytics models
5. **Mobile Applications**: Native iOS and Android apps

### Long-term Vision (6+ months)
1. **Institutional Features**: Enterprise-grade tools for professional traders
2. **Global Expansion**: International markets and exchanges
3. **Alternative Assets**: Expand beyond stocks to ETFs, crypto, and commodities
4. **Advanced AI**: Sophisticated pattern recognition and predictive models
5. **Platform Ecosystem**: Third-party developer API and marketplace