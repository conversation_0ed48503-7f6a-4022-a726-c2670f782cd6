# Kaikyo System Architecture

## High-Level Architecture
Kaikyo follows a modern microservices-oriented architecture with a Laravel backend API, Vue.js frontend, and supporting services for real-time features and data processing.

## Core Components

### Backend API (Laravel)
- **Framework**: Laravel 12.x with PHP 8.3+
- **Architecture**: RESTful API with Inertia.js for SPA-like experience
- **Database**: PostgreSQL 17 with Redis for caching and queues
- **Authentication**: Laravel Sanctum for API tokens, Jetstream for web auth

### Frontend (Vue.js)
- **Framework**: Vue 3.5 with TypeScript
- **Build Tool**: Vite with Hot Module Replacement
- **UI Library**: Nuxt UI Pro with TailwindCSS 4.x
- **State Management**: Vue Query for server state, local state as needed

### Database Layer
- **Primary Database**: PostgreSQL 17
- **Caching**: Redis (DragonflyDB for performance)
- **Queue System**: Redis with <PERSON><PERSON> Horizon
- **Sessions**: Redis-based session storage

## Key Architecture Patterns

### 1. API-First Design
- RESTful API endpoints for all frontend functionality
- API token authentication for mobile/third-party access
- Consistent response formats and error handling

### 2. Subscription Management System
- Multi-processor payment support (Stripe, Paddle, PayPal)
- Flexible plan configuration with feature flags
- Real-time subscription status synchronization

### 3. Real-Time Data Processing
- WebSocket connections for live market data
- Queue-based processing for heavy computations
- Caching strategies for high-frequency data

## Directory Structure

### Backend (`app/`)
```
app/
├── Actions/                    # Domain actions
├── Console/                    # Artisan commands
├── Contracts/                  # Interfaces and abstractions
├── Data/                       # Data transfer objects
├── Enums/                      # PHP enums
├── Events/                     # Event definitions
├── Exceptions/                 # Custom exceptions
├── Filament/                   # Admin panel resources
├── Http/                       # Controllers and middleware
├── Jobs/                       # Queue jobs
├── Listeners/                  # Event listeners
├── Models/                     # Eloquent models
├── Notifications/              # Notifications
├── Observers/                  # Model observers
├── Policies/                   # Authorization policies
├── Providers/                  # Service providers
├── Services/                   # Business logic services
├── Settings/                   # Configuration settings
└── Traits/                     # Reusable traits
```

### Frontend (`resources/js/`)
```
resources/js/
├── Components/                 # Vue components
├── Composables/                # Vue composables
├── Layouts/                    # Inertia layouts
├── Pages/                      # Page components
├── Plugins/                    # Vue plugins
├── routes/                     # Route definitions
├── types/                      # TypeScript definitions
└── lib/                        # Utility libraries
```

## Database Schema

### Core Tables
- **users**: User accounts with authentication and subscription data
- **plans**: Subscription plans with pricing and features
- **subscriptions**: User subscriptions with multi-processor support
- **watchlists**: User watchlists for tracking stocks
- **watchlist_assets**: Individual assets within watchlists
- **payment_methods**: User payment methods across processors

### Support Tables
- **teams**: Team management for collaboration
- **oauth_connections**: Social login connections
- **activity_log**: Audit trail and user actions
- **audit_logs**: Administrative audit tracking
- **permissions**: Role-based permissions
- **settings**: Dynamic application settings

## Key Services

### Subscription Service
- **Location**: `app/Services/Subscription/`
- **Responsibilities**: 
  - Multi-processor subscription management
  - Plan changes and upgrades/downgrades
  - Trial management and cancellation
  - Webhook processing for payment events

### Payment Processing
- **Processors**: Stripe, Paddle, PayPal
- **Features**: 
  - Processor-agnostic interface
  - Automatic webhook handling
  - Payment method management
  - Subscription synchronization

### Market Data Service
- **External APIs**: EOD Historical Data
- **Features**:
  - Real-time stock data fetching
  - Symbol search and metadata
  - Historical data caching
  - Technical indicator calculations

## Security Architecture

### Authentication
- **Web**: Laravel Jetstream with two-factor auth
- **API**: Laravel Sanctum with token-based auth
- **Social**: OAuth integration (Google, GitHub, X, GitLab)
- **Magic Links**: Passwordless authentication option

### Authorization
- **Roles**: Super admin, admin, user roles
- **Permissions**: Granular permission system
- **Policies**: Model-based authorization policies
- **Impersonation**: Admin user impersonation with safeguards

### Data Protection
- **Encryption**: Application-level encryption for sensitive data
- **Audit Logging**: Comprehensive activity tracking
- **API Rate Limiting**: Prevent abuse and ensure fair usage
- **CSRF Protection**: Cross-site request forgery prevention

## Performance Optimization

### Caching Strategy
- **Redis**: Application caching and session storage
- **Database Query Caching**: Frequent query results
- **Asset Caching**: CDN-ready static assets
- **API Response Caching**: Cache expensive computations

### Database Optimization
- **Indexing**: Strategic indexes for common queries
- **Query Optimization**: Efficient Eloquent relationships
- **Connection Pooling**: Database connection management
- **Read Replicas**: Future scaling consideration

### Frontend Performance
- **Code Splitting**: Lazy-loaded components and routes
- **Asset Optimization**: Minified and compressed assets
- **Image Optimization**: Responsive images and WebP format
- **Service Workers**: Offline capability and caching

## Deployment Architecture

### Container-Based Deployment
- **Docker**: Containerized application services
- **Docker Compose**: Development environment orchestration
- **Reverse Proxy**: Caddy for HTTPS and load balancing
- **Health Checks**: Service health monitoring

### Services
- **Web Server**: FrankenPHP/Laravel Octane for high performance
- **Database**: PostgreSQL 17 with persistent storage
- **Cache**: DragonflyDB (Redis-compatible)
- **Queue Worker**: Horizon for queue management
- **Scheduler**: Cron job scheduling

### Environment Configuration
- **Development**: Local Docker with hot reload
- **Staging**: Production-like environment for testing
- **Production**: Optimized configuration with monitoring

## Integration Points

### External Services
- **Payment Processors**: Stripe, Paddle, PayPal APIs
- **Market Data**: EOD Historical Data API
- **Email**: Resend for transactional emails
- **Monitoring**: Sentry for error tracking
- **Analytics**: User behavior and performance metrics

### API Design Principles
- **RESTful**: Standard HTTP methods and status codes
- **Versioning**: API versioning for backward compatibility
- **Documentation**: Auto-generated API documentation
- **Rate Limiting**: Fair usage policies
- **Error Handling**: Consistent error response format

## Scalability Considerations

### Horizontal Scaling
- **Stateless Application**: Easy horizontal scaling
- **Load Balancing**: Multiple web server instances
- **Database Scaling**: Read replicas and sharding options
- **Cache Scaling**: Redis clustering for high availability

### Monitoring and Observability
- **Application Monitoring**: Laravel Telescope in development
- **Error Tracking**: Sentry integration
- **Performance Monitoring**: Query performance tracking
- **Health Checks**: Service availability monitoring

## Development Workflow

### Code Quality
- **Type Safety**: TypeScript in frontend, strict types in PHP
- **Code Formatting**: Pint (PHP) and Prettier (JavaScript/TypeScript)
- **Static Analysis**: Larastan for PHP, ESLint for JavaScript
- **Testing**: Pest for PHP, Playwright for E2E testing

### Development Tools
- **Hot Module Replacement**: Instant frontend updates
- **Queue Monitoring**: Horizon dashboard
- **API Documentation**: Scribe for automatic API docs
- **Debug Tools**: Laravel Telescope and debugging bar